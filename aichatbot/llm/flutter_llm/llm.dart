/// AutoGen LLM - Unified LLM client using Template Method Pattern
library autogen_llm;

export 'src/image.dart';
// Export memory functionality
export 'src/memory/memory.dart';
export 'src/model_context/buffered_chat_completion_context.dart';
// Export model context functionality
export 'src/model_context/chat_completion_context.dart';
export 'src/model_context/head_and_tail_chat_completion_context.dart';
export 'src/model_context/token_limited_chat_completion_context.dart';
export 'src/model_context/unbounded_chat_completion_context.dart';
// Export Anthropic supporting components
export 'src/models/anthropic/anthropic.dart';
// Export main client architecture
export 'src/models/anthropic/anthropic_client.dart';
export 'src/models/anthropic/anthropic_models.dart';
export 'src/models/base_llm_client.dart';
export 'src/models/base_llm_exception.dart';
export 'src/models/base_multimodal_client.dart';
export 'src/models/client_factory.dart';
// Export Gemini supporting components
export 'src/models/gemini/gemini_client.dart';
export 'src/models/gemini/gemini_models.dart';
export 'src/models/gemini/gemini_types.dart';
// Export unified exception system
export 'src/models/llm_auth_exceptions.dart';
export 'src/models/llm_response.dart';
// Export model enums and types
export 'src/models/model_enums.dart';
// Export core interfaces and types
export 'src/models/model_client.dart'
    show ChatCompletionClient, EmbeddingClient;
// Export unified model registry
export 'src/models/model_registry.dart';
export 'src/models/model_types.dart';
// Export OpenAI supporting components
export 'src/models/openai/openai.dart';
export 'src/models/openai/openai_client.dart';
export 'src/models/openai/openai_models.dart' hide OpenAIModelFamily;
export 'src/models/request_types.dart';
export 'src/models/tool_schema.dart';
export 'src/tools/annotations.dart' show ToolFunction, Param;
export 'src/tools/function_tool.dart'
    show FunctionTool, createSimpleFunctionTool;
// Export tool functionality
export 'src/tools/tool.dart' show Tool, BaseTool, StreamTool, BaseStreamTool;
export 'src/tools/workbench.dart' show Workbench, ToolResult;
// Export utilities
export 'src/utils/logger.dart' show LLMLoggers;
export 'src/utils/proxy_http_client.dart'
    show createProxyHttpClient, ProxyAwareHttpClient;
