import 'dart:convert';
import 'dart:typed_data';

/// Represents different image formats
enum ImageFormat {
  png('image/png'),
  jpeg('image/jpeg'),
  gif('image/gif'),
  webp('image/webp'),
  bmp('image/bmp'),
  tiff('image/tiff');

  const ImageFormat(this.mimeType);

  final String mimeType;

  static ImageFormat? fromMimeType(String mimeType) {
    for (final format in ImageFormat.values) {
      if (format.mimeType == mimeType) {
        return format;
      }
    }
    return null;
  }

  static ImageFormat? fromExtension(String extension) {
    switch (extension.toLowerCase()) {
      case '.png':
        return ImageFormat.png;
      case '.jpg':
      case '.jpeg':
        return ImageFormat.jpeg;
      case '.gif':
        return ImageFormat.gif;
      case '.webp':
        return ImageFormat.webp;
      case '.bmp':
        return ImageFormat.bmp;
      case '.tiff':
      case '.tif':
        return ImageFormat.tiff;
      default:
        return null;
    }
  }
}

/// Represents an image with metadata
class Image {
  final Uint8List data;
  final ImageFormat format;
  final int? width;
  final int? height;
  final String? filename;
  final Map<String, dynamic> metadata;

  const Image({
    required this.data,
    required this.format,
    this.width,
    this.height,
    this.filename,
    this.metadata = const {},
  });

  /// Create an image from base64 encoded data
  factory Image.fromBase64({
    required String base64Data,
    required ImageFormat format,
    int? width,
    int? height,
    String? filename,
    Map<String, dynamic> metadata = const {},
  }) {
    final data = base64Decode(base64Data);
    return Image(
      data: data,
      format: format,
      width: width,
      height: height,
      filename: filename,
      metadata: metadata,
    );
  }

  /// Create an image from a data URL (e.g., "data:image/png;base64,...")
  factory Image.fromDataUrl(String dataUrl) {
    final uri = Uri.parse(dataUrl);
    if (uri.scheme != 'data') {
      throw ArgumentError('Invalid data URL scheme: ${uri.scheme}');
    }

    final mimeType = uri.data?.mimeType ?? 'image/png';
    final format = ImageFormat.fromMimeType(mimeType);
    if (format == null) {
      throw ArgumentError('Unsupported image format: $mimeType');
    }

    final data = uri.data?.contentAsBytes();
    if (data == null) {
      throw ArgumentError('No data found in URL');
    }

    return Image(
      data: data,
      format: format,
    );
  }

  /// Create an image from raw bytes with format detection
  factory Image.fromBytes(Uint8List bytes, {String? filename}) {
    final format = _detectFormat(bytes) ?? ImageFormat.png;
    return Image(
      data: bytes,
      format: format,
      filename: filename,
    );
  }

  /// Convert the image to base64 encoded string
  String toBase64() {
    return base64Encode(data);
  }

  /// Convert the image to a data URL
  String toDataUrl() {
    final base64Data = toBase64();
    return 'data:${format.mimeType};base64,$base64Data';
  }

  /// Get the size of the image data in bytes
  int get sizeInBytes => data.length;

  /// Get the size of the image data in a human-readable format
  String get humanReadableSize {
    const units = ['B', 'KB', 'MB', 'GB'];
    double size = sizeInBytes.toDouble();
    int unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return '${size.toStringAsFixed(size < 10 ? 1 : 0)} ${units[unitIndex]}';
  }

  /// Check if the image has dimension information
  bool get hasDimensions => width != null && height != null;

  /// Get the aspect ratio if dimensions are available
  double? get aspectRatio {
    if (width != null && height != null && height! > 0) {
      return width! / height!;
    }
    return null;
  }

  /// Create a copy of the image with modified properties
  Image copyWith({
    Uint8List? data,
    ImageFormat? format,
    int? width,
    int? height,
    String? filename,
    Map<String, dynamic>? metadata,
  }) {
    return Image(
      data: data ?? this.data,
      format: format ?? this.format,
      width: width ?? this.width,
      height: height ?? this.height,
      filename: filename ?? this.filename,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Convert to JSON representation
  Map<String, dynamic> toJson() {
    return {
      'data': toBase64(),
      'format': format.mimeType,
      'width': width,
      'height': height,
      'filename': filename,
      'metadata': metadata,
      'sizeInBytes': sizeInBytes,
    };
  }

  /// Create an image from JSON representation
  factory Image.fromJson(Map<String, dynamic> json) {
    final formatString = json['format'] as String? ?? 'image/png';
    final format = ImageFormat.fromMimeType(formatString) ?? ImageFormat.png;

    return Image.fromBase64(
      base64Data: json['data'] as String,
      format: format,
      width: json['width'] as int?,
      height: json['height'] as int?,
      filename: json['filename'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>? ?? {},
    );
  }

  @override
  String toString() {
    final dimensions =
        hasDimensions ? '${width}x$height' : 'unknown dimensions';
    final name = filename ?? 'unnamed';
    return 'Image($name, $dimensions, ${format.mimeType}, $humanReadableSize)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! Image) return false;

    return data.length == other.data.length &&
        _uint8ListEquals(data, other.data) &&
        format == other.format &&
        width == other.width &&
        height == other.height &&
        filename == other.filename;
  }

  @override
  int get hashCode {
    return Object.hash(
      Object.hashAll(data.take(100)), // Hash first 100 bytes for performance
      format,
      width,
      height,
      filename,
    );
  }
}

/// Image utilities and helper functions
class ImageUtils {
  /// Detect image format from file signature
  static ImageFormat? detectFormat(Uint8List data) {
    return _detectFormat(data);
  }

  /// Validate if the data represents a valid image
  static bool isValidImage(Uint8List data) {
    return _detectFormat(data) != null;
  }

  /// Get image format from filename extension
  static ImageFormat? formatFromFilename(String filename) {
    final extension = filename.split('.').last;
    return ImageFormat.fromExtension('.$extension');
  }

  /// Calculate the maximum dimension that fits within bounds while preserving aspect ratio
  static Map<String, int> calculateFitDimensions({
    required int originalWidth,
    required int originalHeight,
    required int maxWidth,
    required int maxHeight,
  }) {
    final aspectRatio = originalWidth / originalHeight;

    int newWidth = maxWidth;
    int newHeight = (newWidth / aspectRatio).round();

    if (newHeight > maxHeight) {
      newHeight = maxHeight;
      newWidth = (newHeight * aspectRatio).round();
    }

    return {'width': newWidth, 'height': newHeight};
  }

  /// Estimate memory usage for an image with given dimensions and format
  static int estimateMemoryUsage({
    required int width,
    required int height,
    ImageFormat format = ImageFormat.png,
  }) {
    // Base calculation: width * height * bytes per pixel
    int bytesPerPixel;
    switch (format) {
      case ImageFormat.png:
        bytesPerPixel = 4; // RGBA
        break;
      case ImageFormat.jpeg:
        bytesPerPixel = 3; // RGB (compressed, so this is an estimate)
        break;
      case ImageFormat.gif:
        bytesPerPixel = 1; // Palette-based
        break;
      case ImageFormat.webp:
        bytesPerPixel = 4; // Can be RGB or RGBA
        break;
      case ImageFormat.bmp:
        bytesPerPixel = 3; // RGB
        break;
      case ImageFormat.tiff:
        bytesPerPixel = 4; // Usually RGBA
        break;
    }

    return width * height * bytesPerPixel;
  }
}

/// Private helper functions
ImageFormat? _detectFormat(Uint8List data) {
  if (data.length < 4) return null;

  // PNG signature: 89 50 4E 47
  if (data[0] == 0x89 &&
      data[1] == 0x50 &&
      data[2] == 0x4E &&
      data[3] == 0x47) {
    return ImageFormat.png;
  }

  // JPEG signature: FF D8 FF
  if (data[0] == 0xFF && data[1] == 0xD8 && data[2] == 0xFF) {
    return ImageFormat.jpeg;
  }

  // GIF signature: "GIF8"
  if (data.length >= 6) {
    final signature = String.fromCharCodes(data.sublist(0, 4));
    if (signature == 'GIF8') {
      return ImageFormat.gif;
    }
  }

  // WebP signature: "RIFF" + "WEBP"
  if (data.length >= 12) {
    final riff = String.fromCharCodes(data.sublist(0, 4));
    final webp = String.fromCharCodes(data.sublist(8, 12));
    if (riff == 'RIFF' && webp == 'WEBP') {
      return ImageFormat.webp;
    }
  }

  // BMP signature: "BM"
  if (data.length >= 2) {
    if (data[0] == 0x42 && data[1] == 0x4D) {
      return ImageFormat.bmp;
    }
  }

  // TIFF signature: "II*\0" or "MM\0*"
  if (data.length >= 4) {
    if ((data[0] == 0x49 &&
            data[1] == 0x49 &&
            data[2] == 0x2A &&
            data[3] == 0x00) ||
        (data[0] == 0x4D &&
            data[1] == 0x4D &&
            data[2] == 0x00 &&
            data[3] == 0x2A)) {
      return ImageFormat.tiff;
    }
  }

  return null;
}

bool _uint8ListEquals(Uint8List a, Uint8List b) {
  if (a.length != b.length) return false;
  for (int i = 0; i < a.length; i++) {
    if (a[i] != b[i]) return false;
  }
  return true;
}
