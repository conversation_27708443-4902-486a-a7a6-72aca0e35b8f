import 'dart:async';
import 'dart:math';

import 'memory.dart';

/// Simple in-memory implementation of Memory using a List
class ListMemory extends Memory {
  /// Create a new ListMemory
  ///
  /// [maxSize] - Maximum total size in bytes (null for unlimited)
  /// [maxAge] - Maximum age of contents (null for unlimited)
  /// [maxCount] - Maximum number of contents (null for unlimited)
  ListMemory({
    int? maxSize,
    Duration? maxAge,
    int? maxCount,
  })  : _maxSize = maxSize,
        _maxAge = maxAge,
        _maxCount = maxCount;
  final List<MemoryContent> _contents = [];
  final int? _maxSize;
  final Duration? _maxAge;
  final int? _maxCount;

  @override
  Future<void> add(MemoryContent content) async {
    _contents.add(content);

    // Enforce limits
    await _enforceLimits();
  }

  @override
  Future<List<MemoryContent>> getAll() async {
    await _cleanupExpired();
    return List.from(_contents);
  }

  @override
  Future<MemoryContent?> getById(String id) async {
    await _cleanupExpired();

    try {
      return _contents.firstWhere((content) => content.id == id);
    } catch (e) {
      return null;
    }
  }

  @override
  Future<List<MemoryContent>> search(
    String query, {
    bool caseSensitive = false,
    int? limit,
    MemoryMimeType? mimeType,
    Map<String, dynamic>? metadataFilters,
  }) async {
    await _cleanupExpired();

    var results = _contents.where((content) {
      // Check MIME type filter
      if (mimeType != null && content.mimeType != mimeType) {
        return false;
      }

      // Check metadata filters
      if (metadataFilters != null) {
        for (final filter in metadataFilters.entries) {
          if (content.metadata[filter.key] != filter.value) {
            return false;
          }
        }
      }

      // Check text query
      return content.matches(query, caseSensitive: caseSensitive);
    }).toList();

    // Sort by relevance (most recent first for now)
    results.sort((a, b) => b.timestamp.compareTo(a.timestamp));

    // Apply limit
    if (limit != null && results.length > limit) {
      results = results.take(limit).toList();
    }

    return results;
  }

  @override
  Future<List<MemoryContent>> getByMimeType(MemoryMimeType mimeType) async {
    await _cleanupExpired();

    return _contents.where((content) => content.mimeType == mimeType).toList();
  }

  @override
  Future<List<MemoryContent>> getByTimeRange(
      DateTime start, DateTime end) async {
    await _cleanupExpired();

    return _contents
        .where((content) =>
            content.timestamp.isAfter(start) && content.timestamp.isBefore(end))
        .toList();
  }

  @override
  Future<List<MemoryContent>> getRecent(int count) async {
    await _cleanupExpired();

    final sorted = List<MemoryContent>.from(_contents);
    sorted.sort((a, b) => b.timestamp.compareTo(a.timestamp));

    return sorted.take(count).toList();
  }

  @override
  Future<bool> removeById(String id) async {
    final index = _contents.indexWhere((content) => content.id == id);
    if (index != -1) {
      _contents.removeAt(index);
      return true;
    }
    return false;
  }

  @override
  Future<int> removeWhere({
    String? query,
    MemoryMimeType? mimeType,
    Map<String, dynamic>? metadataFilters,
    DateTime? olderThan,
  }) async {
    int removedCount = 0;

    _contents.removeWhere((content) {
      bool shouldRemove = true;

      // Check age filter
      if (olderThan != null && content.timestamp.isAfter(olderThan)) {
        shouldRemove = false;
      }

      // Check MIME type filter
      if (shouldRemove && mimeType != null && content.mimeType != mimeType) {
        shouldRemove = false;
      }

      // Check metadata filters
      if (shouldRemove && metadataFilters != null) {
        for (final filter in metadataFilters.entries) {
          if (content.metadata[filter.key] != filter.value) {
            shouldRemove = false;
            break;
          }
        }
      }

      // Check text query
      if (shouldRemove && query != null && !content.matches(query)) {
        shouldRemove = false;
      }

      if (shouldRemove) {
        removedCount++;
      }

      return shouldRemove;
    });

    return removedCount;
  }

  @override
  Future<void> clear() async {
    _contents.clear();
  }

  @override
  Future<MemoryStats> getStats() async {
    await _cleanupExpired();

    if (_contents.isEmpty) {
      return const MemoryStats(
        totalContents: 0,
        totalSizeBytes: 0,
        contentsByType: {},
        averageContentSize: 0,
      );
    }

    final contentsByType = <MemoryMimeType, int>{};
    int totalSize = 0;
    DateTime? oldest;
    DateTime? newest;

    for (final content in _contents) {
      contentsByType[content.mimeType] =
          (contentsByType[content.mimeType] ?? 0) + 1;
      totalSize += content.sizeInBytes;

      if (oldest == null || content.timestamp.isBefore(oldest)) {
        oldest = content.timestamp;
      }
      if (newest == null || content.timestamp.isAfter(newest)) {
        newest = content.timestamp;
      }
    }

    final averageSize = totalSize / _contents.length;

    return MemoryStats(
      totalContents: _contents.length,
      totalSizeBytes: totalSize,
      contentsByType: contentsByType,
      oldestContentTime: oldest,
      newestContentTime: newest,
      averageContentSize: averageSize,
    );
  }

  @override
  Future<int> getTotalSize() async {
    await _cleanupExpired();
    return _contents.fold<int>(0, (sum, content) => sum + content.sizeInBytes);
  }

  @override
  Future<int> getCount() async {
    await _cleanupExpired();
    return _contents.length;
  }

  @override
  Future<bool> isEmpty() async {
    await _cleanupExpired();
    return _contents.isEmpty;
  }

  @override
  Future<Map<String, dynamic>> export() async {
    await _cleanupExpired();

    return {
      'version': '1.0',
      'timestamp': DateTime.now().millisecondsSinceEpoch,
      'contents': _contents.map((content) => content.toJson()).toList(),
      'stats': (await getStats()).toJson(),
    };
  }

  @override
  Future<void> import(Map<String, dynamic> data) async {
    final contentsData = data['contents'] as List<dynamic>? ?? [];

    _contents.clear();

    for (final contentData in contentsData) {
      if (contentData is Map<String, dynamic>) {
        try {
          final content = MemoryContent.fromJson(contentData);
          _contents.add(content);
        } catch (e) {
          // Skip invalid content
        }
      }
    }

    await _enforceLimits();
  }

  /// Clean up expired contents based on maxAge
  Future<void> _cleanupExpired() async {
    if (_maxAge == null) return;

    final cutoff = DateTime.now().subtract(_maxAge!);
    _contents.removeWhere((content) => content.timestamp.isBefore(cutoff));
  }

  /// Enforce memory limits (size, count, age)
  Future<void> _enforceLimits() async {
    await _cleanupExpired();

    // Enforce count limit
    if (_maxCount != null && _contents.length > _maxCount!) {
      // Remove oldest contents
      _contents.sort((a, b) => a.timestamp.compareTo(b.timestamp));
      _contents.removeRange(0, _contents.length - _maxCount!);
    }

    // Enforce size limit
    if (_maxSize != null) {
      int totalSize = await getTotalSize();

      if (totalSize > _maxSize!) {
        // Remove oldest contents until under size limit
        _contents.sort((a, b) => a.timestamp.compareTo(b.timestamp));

        while (totalSize > _maxSize! && _contents.isNotEmpty) {
          final removed = _contents.removeAt(0);
          totalSize -= removed.sizeInBytes;
        }
      }
    }
  }
}

/// Memory implementation with LRU (Least Recently Used) eviction
class LRUListMemory extends ListMemory {
  LRUListMemory({
    int? maxSize,
    Duration? maxAge,
    int? maxCount,
  }) : super(maxSize: maxSize, maxAge: maxAge, maxCount: maxCount);
  final Map<String, DateTime> _accessTimes = {};

  @override
  Future<MemoryContent?> getById(String id) async {
    final content = await super.getById(id);
    if (content?.id != null) {
      _accessTimes[content!.id!] = DateTime.now();
    }
    return content;
  }

  @override
  Future<List<MemoryContent>> search(
    String query, {
    bool caseSensitive = false,
    int? limit,
    MemoryMimeType? mimeType,
    Map<String, dynamic>? metadataFilters,
  }) async {
    final results = await super.search(
      query,
      caseSensitive: caseSensitive,
      limit: limit,
      mimeType: mimeType,
      metadataFilters: metadataFilters,
    );

    // Update access times for retrieved contents
    final now = DateTime.now();
    for (final content in results) {
      if (content.id != null) {
        _accessTimes[content.id!] = now;
      }
    }

    return results;
  }

  @override
  Future<void> _enforceLimits() async {
    await _cleanupExpired();

    // Enforce count limit with LRU eviction
    if (_maxCount != null && _contents.length > _maxCount!) {
      _evictLRU(_contents.length - _maxCount!);
    }

    // Enforce size limit with LRU eviction
    if (_maxSize != null) {
      int totalSize = await getTotalSize();

      while (totalSize > _maxSize! && _contents.isNotEmpty) {
        final removed = _evictLRUSingle();
        if (removed != null) {
          totalSize -= removed.sizeInBytes;
        } else {
          break;
        }
      }
    }
  }

  /// Evict the least recently used content
  MemoryContent? _evictLRUSingle() {
    if (_contents.isEmpty) return null;

    // Find the content with the oldest access time
    MemoryContent? lruContent;
    DateTime? oldestAccess;

    for (final content in _contents) {
      DateTime accessTime;

      if (content.id != null && _accessTimes.containsKey(content.id)) {
        accessTime = _accessTimes[content.id!]!;
      } else {
        accessTime = content.timestamp;
      }

      if (oldestAccess == null || accessTime.isBefore(oldestAccess)) {
        oldestAccess = accessTime;
        lruContent = content;
      }
    }

    if (lruContent != null) {
      _contents.remove(lruContent);
      if (lruContent.id != null) {
        _accessTimes.remove(lruContent.id);
      }
    }

    return lruContent;
  }

  /// Evict multiple LRU contents
  void _evictLRU(int count) {
    for (int i = 0; i < count; i++) {
      if (_evictLRUSingle() == null) break;
    }
  }

  @override
  Future<void> clear() async {
    await super.clear();
    _accessTimes.clear();
  }

  @override
  Future<bool> removeById(String id) async {
    final removed = await super.removeById(id);
    if (removed) {
      _accessTimes.remove(id);
    }
    return removed;
  }
}

/// Memory implementation with random eviction (for testing)
class RandomEvictionListMemory extends ListMemory {
  RandomEvictionListMemory({
    int? maxSize,
    Duration? maxAge,
    int? maxCount,
  }) : super(maxSize: maxSize, maxAge: maxAge, maxCount: maxCount);
  final Random _random = Random();

  @override
  Future<void> _enforceLimits() async {
    await _cleanupExpired();

    // Enforce count limit with random eviction
    if (_maxCount != null && _contents.length > _maxCount!) {
      final toRemove = _contents.length - _maxCount!;
      for (int i = 0; i < toRemove; i++) {
        if (_contents.isNotEmpty) {
          final index = _random.nextInt(_contents.length);
          _contents.removeAt(index);
        }
      }
    }

    // Enforce size limit with random eviction
    if (_maxSize != null) {
      int totalSize = await getTotalSize();

      while (totalSize > _maxSize! && _contents.isNotEmpty) {
        final index = _random.nextInt(_contents.length);
        final removed = _contents.removeAt(index);
        totalSize -= removed.sizeInBytes;
      }
    }
  }
}

/// Memory implementation with FIFO (First In, First Out) eviction
class FIFOListMemory extends ListMemory {
  FIFOListMemory({
    int? maxSize,
    Duration? maxAge,
    int? maxCount,
  }) : super(maxSize: maxSize, maxAge: maxAge, maxCount: maxCount);

  @override
  Future<void> _enforceLimits() async {
    await _cleanupExpired();

    // Enforce count limit with FIFO eviction
    if (_maxCount != null && _contents.length > _maxCount!) {
      // Sort by timestamp to ensure FIFO order
      _contents.sort((a, b) => a.timestamp.compareTo(b.timestamp));
      final toRemove = _contents.length - _maxCount!;
      _contents.removeRange(0, toRemove);
    }

    // Enforce size limit with FIFO eviction
    if (_maxSize != null) {
      _contents.sort((a, b) => a.timestamp.compareTo(b.timestamp));

      int totalSize = await getTotalSize();
      int index = 0;

      while (totalSize > _maxSize! && index < _contents.length) {
        final removed = _contents.removeAt(index);
        totalSize -= removed.sizeInBytes;
      }
    }
  }
}
