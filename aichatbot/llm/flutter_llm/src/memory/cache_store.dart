import 'dart:async';
import 'dart:convert';

/// Abstract interface for cache storage
abstract class CacheStore {
  /// Get a value from the cache
  Future<T?> get<T>(String key);

  /// Set a value in the cache with optional TTL
  Future<void> set<T>(String key, T value, {Duration? ttl});

  /// Check if a key exists in the cache
  Future<bool> contains(String key);

  /// Remove a key from the cache
  Future<void> remove(String key);

  /// Clear all entries from the cache
  Future<void> clear();

  /// Get cache statistics
  Future<CacheStats> getStats();
}

/// Cache statistics
class CacheStats {
  const CacheStats({
    required this.hitCount,
    required this.missCount,
    required this.evictionCount,
    required this.size,
    required this.averageLoadTime,
  });
  final int hitCount;
  final int missCount;
  final int evictionCount;
  final int size;
  final Duration averageLoadTime;

  double get hitRate =>
      hitCount + missCount > 0 ? hitCount / (hitCount + missCount) : 0.0;
  double get missRate => 1.0 - hitRate;
}

/// In-memory cache implementation with LRU eviction
class InMemoryCacheStore implements CacheStore {
  InMemoryCacheStore({int maxSize = 1000}) : _maxSize = maxSize;
  final int _maxSize;
  final Map<String, _CacheEntry> _cache = {};
  final Map<String, int> _accessOrder = {};
  int _accessCounter = 0;

  // Statistics
  int _hitCount = 0;
  int _missCount = 0;
  int _evictionCount = 0;
  final List<Duration> _loadTimes = [];

  @override
  Future<T?> get<T>(String key) async {
    final entry = _cache[key];
    if (entry == null || entry.isExpired) {
      if (entry?.isExpired == true) {
        _cache.remove(key);
        _accessOrder.remove(key);
      }
      _missCount++;
      return null;
    }

    _hitCount++;
    _accessOrder[key] = ++_accessCounter;
    return entry.value as T?;
  }

  @override
  Future<void> set<T>(String key, T value, {Duration? ttl}) async {
    final stopwatch = Stopwatch()..start();

    final expiresAt = ttl != null ? DateTime.now().add(ttl) : null;
    _cache[key] = _CacheEntry(value, expiresAt);
    _accessOrder[key] = ++_accessCounter;

    // Evict if necessary
    while (_cache.length > _maxSize) {
      _evictLeastRecentlyUsed();
    }

    stopwatch.stop();
    _loadTimes.add(stopwatch.elapsed);
    if (_loadTimes.length > 1000) {
      _loadTimes.removeRange(0, _loadTimes.length - 1000);
    }
  }

  @override
  Future<bool> contains(String key) async {
    final entry = _cache[key];
    if (entry == null) return false;
    if (entry.isExpired) {
      _cache.remove(key);
      _accessOrder.remove(key);
      return false;
    }
    return true;
  }

  @override
  Future<void> remove(String key) async {
    _cache.remove(key);
    _accessOrder.remove(key);
  }

  @override
  Future<void> clear() async {
    _cache.clear();
    _accessOrder.clear();
    _accessCounter = 0;
  }

  @override
  Future<CacheStats> getStats() async {
    final averageLoadTime = _loadTimes.isNotEmpty
        ? _loadTimes.reduce((a, b) => a + b) ~/ _loadTimes.length
        : Duration.zero;

    return CacheStats(
      hitCount: _hitCount,
      missCount: _missCount,
      evictionCount: _evictionCount,
      size: _cache.length,
      averageLoadTime: averageLoadTime,
    );
  }

  void _evictLeastRecentlyUsed() {
    if (_accessOrder.isEmpty) return;

    final lruKey =
        _accessOrder.entries.reduce((a, b) => a.value < b.value ? a : b).key;

    _cache.remove(lruKey);
    _accessOrder.remove(lruKey);
    _evictionCount++;
  }
}

/// Cache entry with optional expiration
class _CacheEntry {
  _CacheEntry(this.value, this.expiresAt);
  final dynamic value;
  final DateTime? expiresAt;

  bool get isExpired => expiresAt != null && DateTime.now().isAfter(expiresAt!);
}

/// Serializable cache store that persists to storage
abstract class SerializableCacheStore extends CacheStore {
  /// Serialize the cache to JSON
  Future<Map<String, dynamic>> toJson();

  /// Load the cache from JSON
  Future<void> fromJson(Map<String, dynamic> json);

  /// Save the cache to persistent storage
  Future<void> save();

  /// Load the cache from persistent storage
  Future<void> load();
}

/// JSON-based serializable cache implementation
class JsonCacheStore extends SerializableCacheStore {
  JsonCacheStore({int maxSize = 1000, String? filePath})
      : _memoryStore = InMemoryCacheStore(maxSize: maxSize),
        _filePath = filePath;
  final InMemoryCacheStore _memoryStore;
  final String? _filePath;

  @override
  Future<T?> get<T>(String key) => _memoryStore.get<T>(key);

  @override
  Future<void> set<T>(String key, T value, {Duration? ttl}) =>
      _memoryStore.set<T>(key, value, ttl: ttl);

  @override
  Future<bool> contains(String key) => _memoryStore.contains(key);

  @override
  Future<void> remove(String key) => _memoryStore.remove(key);

  @override
  Future<void> clear() => _memoryStore.clear();

  @override
  Future<CacheStats> getStats() => _memoryStore.getStats();

  @override
  Future<Map<String, dynamic>> toJson() async {
    final entries = <String, Map<String, dynamic>>{};

    for (final entry in _memoryStore._cache.entries) {
      if (!entry.value.isExpired) {
        entries[entry.key] = {
          'value': entry.value.value,
          'expiresAt': entry.value.expiresAt?.millisecondsSinceEpoch,
        };
      }
    }

    final stats = await getStats();

    return {
      'entries': entries,
      'stats': {
        'hitCount': stats.hitCount,
        'missCount': stats.missCount,
        'evictionCount': stats.evictionCount,
      },
      'version': '1.0',
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
  }

  @override
  Future<void> fromJson(Map<String, dynamic> json) async {
    await clear();

    final entries = json['entries'] as Map<String, dynamic>? ?? {};

    for (final entry in entries.entries) {
      final data = entry.value as Map<String, dynamic>;
      final value = data['value'];
      final expiresAtMs = data['expiresAt'] as int?;

      Duration? ttl;
      if (expiresAtMs != null) {
        final expiresAt = DateTime.fromMillisecondsSinceEpoch(expiresAtMs);
        if (expiresAt.isAfter(DateTime.now())) {
          ttl = expiresAt.difference(DateTime.now());
        } else {
          continue; // Skip expired entries
        }
      }

      await set(entry.key, value, ttl: ttl);
    }
  }

  @override
  Future<void> save() async {
    if (_filePath == null) return;

    // Note: In a real implementation, you would use dart:io to write to file
    // This is a placeholder for the interface
    final json = await toJson();
    final jsonString = jsonEncode(json);
    // await File(_filePath!).writeAsString(jsonString);
  }

  @override
  Future<void> load() async {
    if (_filePath == null) return;

    // Note: In a real implementation, you would use dart:io to read from file
    // This is a placeholder for the interface
    try {
      // final content = await File(_filePath!).readAsString();
      // final json = jsonDecode(content) as Map<String, dynamic>;
      // await fromJson(json);
    } catch (e) {
      // File doesn't exist or is corrupted, start with empty cache
    }
  }
}
