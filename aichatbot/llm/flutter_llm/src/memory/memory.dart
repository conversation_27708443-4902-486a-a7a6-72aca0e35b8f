import 'dart:async';
import 'dart:convert';

import '../image.dart';

/// Represents different types of memory content
enum MemoryMimeType {
  textPlain('text/plain'),
  textMarkdown('text/markdown'),
  textHtml('text/html'),
  applicationJson('application/json'),
  imagePng('image/png'),
  imageJpeg('image/jpeg'),
  imageGif('image/gif'),
  imageWebp('image/webp'),
  audioPcm('audio/pcm'),
  videoMp4('video/mp4'),
  applicationPdf('application/pdf'),
  unknown('application/octet-stream');

  const MemoryMimeType(this.value);

  final String value;

  static MemoryMimeType fromString(String mimeType) {
    for (final type in MemoryMimeType.values) {
      if (type.value == mimeType) {
        return type;
      }
    }
    return MemoryMimeType.unknown;
  }

  bool get isText => value.startsWith('text/');
  bool get isImage => value.startsWith('image/');
  bool get isAudio => value.startsWith('audio/');
  bool get isVideo => value.startsWith('video/');
  bool get isApplication => value.startsWith('application/');
}

/// Represents a piece of content stored in memory
class MemoryContent {
  MemoryContent({
    required this.content,
    required this.mimeType,
    this.metadata = const {},
    DateTime? timestamp,
    this.id,
  }) : timestamp = timestamp ?? DateTime.now();

  /// Create text content
  factory MemoryContent.text(
    String text, {
    Map<String, dynamic> metadata = const {},
    DateTime? timestamp,
    String? id,
  }) {
    return MemoryContent(
      content: text,
      mimeType: MemoryMimeType.textPlain,
      metadata: metadata,
      timestamp: timestamp,
      id: id,
    );
  }

  /// Create markdown content
  factory MemoryContent.markdown(
    String markdown, {
    Map<String, dynamic> metadata = const {},
    DateTime? timestamp,
    String? id,
  }) {
    return MemoryContent(
      content: markdown,
      mimeType: MemoryMimeType.textMarkdown,
      metadata: metadata,
      timestamp: timestamp,
      id: id,
    );
  }

  /// Create JSON content
  factory MemoryContent.json(
    Map<String, dynamic> json, {
    Map<String, dynamic> metadata = const {},
    DateTime? timestamp,
    String? id,
  }) {
    return MemoryContent(
      content: json,
      mimeType: MemoryMimeType.applicationJson,
      metadata: metadata,
      timestamp: timestamp,
      id: id,
    );
  }

  /// Create image content
  factory MemoryContent.image(
    Image image, {
    Map<String, dynamic> metadata = const {},
    DateTime? timestamp,
    String? id,
  }) {
    final mimeType = MemoryMimeType.fromString(image.format.mimeType);
    return MemoryContent(
      content: image,
      mimeType: mimeType,
      metadata: metadata,
      timestamp: timestamp,
      id: id,
    );
  }

  /// Create from JSON representation
  factory MemoryContent.fromJson(Map<String, dynamic> json) {
    final mimeType = MemoryMimeType.fromString(json['mimeType'] as String);
    dynamic content = json['content'];

    // Deserialize specific content types
    if (mimeType.isImage && content is Map<String, dynamic>) {
      content = Image.fromJson(content);
    }

    return MemoryContent(
      content: content,
      mimeType: mimeType,
      metadata: json['metadata'] as Map<String, dynamic>? ?? {},
      timestamp: DateTime.fromMillisecondsSinceEpoch(json['timestamp'] as int),
      id: json['id'] as String?,
    );
  }
  final dynamic content;
  final MemoryMimeType mimeType;
  final Map<String, dynamic> metadata;
  final DateTime timestamp;
  final String? id;

  /// Get content as string
  String asString() {
    if (content is String) {
      return content as String;
    } else if (content is Map) {
      return jsonEncode(content);
    } else if (content is Image) {
      return (content as Image).toDataUrl();
    } else {
      return content.toString();
    }
  }

  /// Get content as JSON (if applicable)
  Map<String, dynamic>? asJson() {
    if (content is Map<String, dynamic>) {
      return content as Map<String, dynamic>;
    } else if (content is String &&
        mimeType == MemoryMimeType.applicationJson) {
      try {
        return jsonDecode(content as String) as Map<String, dynamic>;
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  /// Get content as image (if applicable)
  Image? asImage() {
    if (content is Image) {
      return content as Image;
    }
    return null;
  }

  /// Get the size of the content in bytes
  int get sizeInBytes {
    if (content is String) {
      return utf8.encode(content as String).length;
    } else if (content is Image) {
      return (content as Image).sizeInBytes;
    } else if (content is Map) {
      return utf8.encode(jsonEncode(content)).length;
    } else {
      return utf8.encode(content.toString()).length;
    }
  }

  /// Check if content matches a search query
  bool matches(String query, {bool caseSensitive = false}) {
    final searchText = asString();
    if (caseSensitive) {
      return searchText.contains(query);
    } else {
      return searchText.toLowerCase().contains(query.toLowerCase());
    }
  }

  /// Create a copy with modified properties
  MemoryContent copyWith({
    dynamic content,
    MemoryMimeType? mimeType,
    Map<String, dynamic>? metadata,
    DateTime? timestamp,
    String? id,
  }) {
    return MemoryContent(
      content: content ?? this.content,
      mimeType: mimeType ?? this.mimeType,
      metadata: metadata ?? this.metadata,
      timestamp: timestamp ?? this.timestamp,
      id: id ?? this.id,
    );
  }

  /// Convert to JSON representation
  Map<String, dynamic> toJson() {
    dynamic serializedContent;

    if (content is String) {
      serializedContent = content;
    } else if (content is Map) {
      serializedContent = content;
    } else if (content is Image) {
      serializedContent = (content as Image).toJson();
    } else {
      serializedContent = content.toString();
    }

    return {
      'content': serializedContent,
      'mimeType': mimeType.value,
      'metadata': metadata,
      'timestamp': timestamp.millisecondsSinceEpoch,
      'id': id,
      'sizeInBytes': sizeInBytes,
    };
  }

  @override
  String toString() {
    final contentPreview = asString();
    final preview = contentPreview.length > 100
        ? '${contentPreview.substring(0, 100)}...'
        : contentPreview;
    return 'MemoryContent(${mimeType.value}, ${sizeInBytes}B): $preview';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! MemoryContent) return false;

    return content == other.content &&
        mimeType == other.mimeType &&
        id == other.id;
  }

  @override
  int get hashCode => Object.hash(content, mimeType, id);
}

/// Abstract base class for memory implementations
abstract class Memory {
  /// Add content to memory
  Future<void> add(MemoryContent content);

  /// Add multiple contents to memory
  Future<void> addAll(List<MemoryContent> contents) async {
    for (final content in contents) {
      await add(content);
    }
  }

  /// Get all contents from memory
  Future<List<MemoryContent>> getAll();

  /// Get content by ID
  Future<MemoryContent?> getById(String id);

  /// Search contents by query
  Future<List<MemoryContent>> search(
    String query, {
    bool caseSensitive = false,
    int? limit,
    MemoryMimeType? mimeType,
    Map<String, dynamic>? metadataFilters,
  });

  /// Get contents by MIME type
  Future<List<MemoryContent>> getByMimeType(MemoryMimeType mimeType);

  /// Get contents within a time range
  Future<List<MemoryContent>> getByTimeRange(DateTime start, DateTime end);

  /// Get the most recent contents
  Future<List<MemoryContent>> getRecent(int count);

  /// Remove content by ID
  Future<bool> removeById(String id);

  /// Remove all contents matching criteria
  Future<int> removeWhere({
    String? query,
    MemoryMimeType? mimeType,
    Map<String, dynamic>? metadataFilters,
    DateTime? olderThan,
  });

  /// Clear all contents
  Future<void> clear();

  /// Get memory statistics
  Future<MemoryStats> getStats();

  /// Get the total size of all contents in bytes
  Future<int> getTotalSize();

  /// Get the number of contents
  Future<int> getCount();

  /// Check if memory is empty
  Future<bool> isEmpty();

  /// Export memory contents to JSON
  Future<Map<String, dynamic>> export();

  /// Import memory contents from JSON
  Future<void> import(Map<String, dynamic> data);
}

/// Memory statistics
class MemoryStats {
  const MemoryStats({
    required this.totalContents,
    required this.totalSizeBytes,
    required this.contentsByType,
    this.oldestContentTime,
    this.newestContentTime,
    required this.averageContentSize,
  });

  /// Create from JSON representation
  factory MemoryStats.fromJson(Map<String, dynamic> json) {
    final contentsByTypeJson =
        json['contentsByType'] as Map<String, dynamic>? ?? {};
    final contentsByType = <MemoryMimeType, int>{};

    for (final entry in contentsByTypeJson.entries) {
      final mimeType = MemoryMimeType.fromString(entry.key);
      contentsByType[mimeType] = entry.value as int;
    }

    return MemoryStats(
      totalContents: json['totalContents'] as int,
      totalSizeBytes: json['totalSizeBytes'] as int,
      contentsByType: contentsByType,
      oldestContentTime: json['oldestContentTime'] != null
          ? DateTime.fromMillisecondsSinceEpoch(
              json['oldestContentTime'] as int)
          : null,
      newestContentTime: json['newestContentTime'] != null
          ? DateTime.fromMillisecondsSinceEpoch(
              json['newestContentTime'] as int)
          : null,
      averageContentSize: json['averageContentSize'] as double,
    );
  }
  final int totalContents;
  final int totalSizeBytes;
  final Map<MemoryMimeType, int> contentsByType;
  final DateTime? oldestContentTime;
  final DateTime? newestContentTime;
  final double averageContentSize;

  /// Get human-readable size
  String get humanReadableSize {
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    double size = totalSizeBytes.toDouble();
    int unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return '${size.toStringAsFixed(size < 10 ? 1 : 0)} ${units[unitIndex]}';
  }

  /// Get the age of the memory (time since oldest content)
  Duration? get memoryAge {
    if (oldestContentTime == null) return null;
    return DateTime.now().difference(oldestContentTime!);
  }

  /// Convert to JSON representation
  Map<String, dynamic> toJson() {
    return {
      'totalContents': totalContents,
      'totalSizeBytes': totalSizeBytes,
      'contentsByType': contentsByType.map((k, v) => MapEntry(k.value, v)),
      'oldestContentTime': oldestContentTime?.millisecondsSinceEpoch,
      'newestContentTime': newestContentTime?.millisecondsSinceEpoch,
      'averageContentSize': averageContentSize,
      'humanReadableSize': humanReadableSize,
    };
  }

  @override
  String toString() {
    return 'MemoryStats($totalContents contents, $humanReadableSize, age: ${memoryAge?.inDays ?? 0} days)';
  }
}

/// Memory query builder for complex searches
class MemoryQuery {
  String? _textQuery;
  bool _caseSensitive = false;
  MemoryMimeType? _mimeType;
  Map<String, dynamic>? _metadataFilters;
  DateTime? _startTime;
  DateTime? _endTime;
  int? _limit;
  String? _sortBy;
  bool _ascending = true;

  /// Search by text content
  MemoryQuery text(String query, {bool caseSensitive = false}) {
    _textQuery = query;
    _caseSensitive = caseSensitive;
    return this;
  }

  /// Filter by MIME type
  MemoryQuery mimeType(MemoryMimeType type) {
    _mimeType = type;
    return this;
  }

  /// Filter by metadata
  MemoryQuery metadata(String key, dynamic value) {
    _metadataFilters ??= {};
    _metadataFilters![key] = value;
    return this;
  }

  /// Filter by time range
  MemoryQuery timeRange(DateTime start, DateTime end) {
    _startTime = start;
    _endTime = end;
    return this;
  }

  /// Filter by time since
  MemoryQuery since(DateTime time) {
    _startTime = time;
    return this;
  }

  /// Filter by time until
  MemoryQuery until(DateTime time) {
    _endTime = time;
    return this;
  }

  /// Limit number of results
  MemoryQuery limit(int count) {
    _limit = count;
    return this;
  }

  /// Sort results
  MemoryQuery sortBy(String field, {bool ascending = true}) {
    _sortBy = field;
    _ascending = ascending;
    return this;
  }

  /// Execute query against memory
  Future<List<MemoryContent>> execute(Memory memory) async {
    List<MemoryContent> results;

    if (_textQuery != null) {
      results = await memory.search(
        _textQuery!,
        caseSensitive: _caseSensitive,
        limit: _limit,
        mimeType: _mimeType,
        metadataFilters: _metadataFilters,
      );
    } else if (_mimeType != null) {
      results = await memory.getByMimeType(_mimeType!);
    } else if (_startTime != null || _endTime != null) {
      final start = _startTime ?? DateTime.fromMillisecondsSinceEpoch(0);
      final end = _endTime ?? DateTime.now();
      results = await memory.getByTimeRange(start, end);
    } else {
      results = await memory.getAll();
    }

    // Apply additional filters
    if (_metadataFilters != null && _textQuery == null) {
      results = results.where((content) {
        return _metadataFilters!.entries.every((filter) {
          return content.metadata[filter.key] == filter.value;
        });
      }).toList();
    }

    // Apply sorting
    if (_sortBy != null) {
      results.sort((a, b) {
        dynamic aValue, bValue;

        switch (_sortBy) {
          case 'timestamp':
            aValue = a.timestamp;
            bValue = b.timestamp;
            break;
          case 'size':
            aValue = a.sizeInBytes;
            bValue = b.sizeInBytes;
            break;
          case 'mimeType':
            aValue = a.mimeType.value;
            bValue = b.mimeType.value;
            break;
          default:
            aValue = a.metadata[_sortBy];
            bValue = b.metadata[_sortBy];
        }

        final comparison = Comparable.compare(aValue, bValue);
        return _ascending ? comparison : -comparison;
      });
    }

    // Apply limit
    if (_limit != null && results.length > _limit!) {
      results = results.take(_limit!).toList();
    }

    return results;
  }
}
