/// Utility for parsing R1-style thinking models content
///
/// R1 models like Claude-3.5-Sonnet-R1 include thinking content wrapped in <think>...</think> tags
class ContentParser {
  /// Parse R1 content that contains thinking tags
  ///
  /// Returns a map with:
  /// - 'thought': The content inside <think>...</think> tags (if found)
  /// - 'content': The remaining content after removing thinking tags
  static Map<String, String?> parseR1Content(String content) {
    final thinkStart = content.indexOf('<think>');
    final thinkEnd = content.indexOf('</think>');

    // No thinking tags found
    if (thinkStart == -1 || thinkEnd == -1) {
      return {
        'thought': null,
        'content': content,
      };
    }

    // Invalid tag order
    if (thinkEnd < thinkStart) {
      return {
        'thought': null,
        'content': content,
      };
    }

    // Extract thinking content
    final thought =
        content.substring(thinkStart + '<think>'.length, thinkEnd).trim();

    // Extract remaining content (everything after </think>)
    final remainingContent =
        content.substring(thinkEnd + '</think>'.length).trim();

    return {
      'thought': thought.isEmpty ? null : thought,
      'content': remainingContent,
    };
  }

  /// Check if content contains thinking tags
  static bool hasThinkingTags(String content) {
    return content.contains('<think>') && content.contains('</think>');
  }

  /// Extract only the thinking content
  static String? extractThought(String content) {
    final parsed = parseR1Content(content);
    return parsed['thought'];
  }

  /// Extract only the non-thinking content
  static String extractContent(String content) {
    final parsed = parseR1Content(content);
    return parsed['content'] ?? '';
  }

  /// Wrap content in thinking tags
  static String wrapInThinkingTags(String thought, String content) {
    if (thought.isEmpty) return content;
    return '<think>$thought</think>\n\n$content';
  }

  /// Remove thinking tags from content
  static String removeThinkingTags(String content) {
    return extractContent(content);
  }

  /// Validate thinking tag structure
  static bool hasValidThinkingStructure(String content) {
    final thinkStartCount = '<think>'.allMatches(content).length;
    final thinkEndCount = '</think>'.allMatches(content).length;

    // Must have equal number of start and end tags
    if (thinkStartCount != thinkEndCount) return false;

    // Must have at least one pair if any tags exist
    if (thinkStartCount == 0 && thinkEndCount == 0) return true;

    // Check that each start tag has a corresponding end tag after it
    int currentPos = 0;
    for (int i = 0; i < thinkStartCount; i++) {
      final startPos = content.indexOf('<think>', currentPos);
      if (startPos == -1) return false;

      final endPos = content.indexOf('</think>', startPos);
      if (endPos == -1) return false;

      currentPos = endPos + '</think>'.length;
    }

    return true;
  }

  /// Parse multiple thinking sections from content
  static List<Map<String, String>> parseMultipleThinkingSections(
      String content) {
    final sections = <Map<String, String>>[];
    String remainingContent = content;

    while (remainingContent.contains('<think>') &&
        remainingContent.contains('</think>')) {
      final thinkStart = remainingContent.indexOf('<think>');
      final thinkEnd = remainingContent.indexOf('</think>');

      if (thinkEnd < thinkStart) break;

      // Extract content before thinking section
      final beforeThinking = remainingContent.substring(0, thinkStart).trim();
      if (beforeThinking.isNotEmpty) {
        sections.add({
          'type': 'content',
          'text': beforeThinking,
        });
      }

      // Extract thinking content
      final thought = remainingContent
          .substring(thinkStart + '<think>'.length, thinkEnd)
          .trim();
      if (thought.isNotEmpty) {
        sections.add({
          'type': 'thought',
          'text': thought,
        });
      }

      // Continue with remaining content
      remainingContent =
          remainingContent.substring(thinkEnd + '</think>'.length);
    }

    // Add any remaining content
    if (remainingContent.trim().isNotEmpty) {
      sections.add({
        'type': 'content',
        'text': remainingContent.trim(),
      });
    }

    return sections;
  }

  /// Merge thinking sections back into content
  static String mergeThinkingSections(List<Map<String, String>> sections) {
    final buffer = StringBuffer();

    for (final section in sections) {
      final type = section['type'];
      final text = section['text'];

      if (text == null || text.isEmpty) continue;

      if (type == 'thought') {
        buffer.write('<think>$text</think>');
      } else {
        buffer.write(text);
      }

      // Add spacing between sections
      if (section != sections.last) {
        buffer.write('\n\n');
      }
    }

    return buffer.toString();
  }
}
