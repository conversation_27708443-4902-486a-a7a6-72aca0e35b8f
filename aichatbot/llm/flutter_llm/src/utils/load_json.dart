import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'json_utils.dart';

/// Utilities for loading JSON data from various sources
class JsonLoader {
  /// Load JSON from a file
  static Future<Map<String, dynamic>> loadFromFile(String filePath) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        throw FileSystemException('File not found', filePath);
      }

      final content = await file.readAsString();
      return jsonDecode(content) as Map<String, dynamic>;
    } catch (e) {
      throw JsonLoadException('Failed to load JSON from file $filePath: $e');
    }
  }

  /// Load JSON from a file synchronously
  static Map<String, dynamic> loadFromFileSync(String filePath) {
    try {
      final file = File(filePath);
      if (!file.existsSync()) {
        throw FileSystemException('File not found', filePath);
      }

      final content = file.readAsStringSync();
      return jsonDecode(content) as Map<String, dynamic>;
    } catch (e) {
      throw JsonLoadException('Failed to load JSON from file $filePath: $e');
    }
  }

  /// Load JSON from a string
  static Map<String, dynamic> loadFromString(String jsonString) {
    try {
      return jsonDecode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      throw JsonLoadException('Failed to parse JSON string: $e');
    }
  }

  /// Load JSON from bytes
  static Map<String, dynamic> loadFromBytes(List<int> bytes) {
    try {
      final jsonString = utf8.decode(bytes);
      return jsonDecode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      throw JsonLoadException('Failed to load JSON from bytes: $e');
    }
  }

  /// Load JSON from URL (HTTP/HTTPS)
  static Future<Map<String, dynamic>> loadFromUrl(
    String url, {
    Map<String, String>? headers,
    Duration timeout = const Duration(seconds: 30),
  }) async {
    try {
      final client = HttpClient();
      client.connectionTimeout = timeout;

      final uri = Uri.parse(url);
      final request = await client.getUrl(uri);

      if (headers != null) {
        headers.forEach((key, value) {
          request.headers.add(key, value);
        });
      }

      final response = await request.close();

      if (response.statusCode != 200) {
        throw HttpException(
            'HTTP ${response.statusCode}: ${response.reasonPhrase}');
      }

      final content = await response.transform(utf8.decoder).join();
      client.close();

      return jsonDecode(content) as Map<String, dynamic>;
    } catch (e) {
      throw JsonLoadException('Failed to load JSON from URL $url: $e');
    }
  }

  /// Load multiple JSON files from a directory
  static Future<Map<String, Map<String, dynamic>>> loadFromDirectory(
    String directoryPath, {
    String pattern = '*.json',
    bool recursive = false,
  }) async {
    try {
      final directory = Directory(directoryPath);
      if (!await directory.exists()) {
        throw FileSystemException('Directory not found', directoryPath);
      }

      final result = <String, Map<String, dynamic>>{};
      final entities = await directory.list(recursive: recursive).toList();

      for (final entity in entities) {
        if (entity is File && entity.path.endsWith('.json')) {
          if (_matchesPattern(entity.path, pattern)) {
            final key = _getFileKey(entity.path, directoryPath);
            result[key] = await loadFromFile(entity.path);
          }
        }
      }

      return result;
    } catch (e) {
      throw JsonLoadException(
          'Failed to load JSON files from directory $directoryPath: $e');
    }
  }

  /// Load JSON with schema validation
  static Future<Map<String, dynamic>> loadFromFileWithValidation(
    String filePath,
    Map<String, dynamic> schema,
  ) async {
    final data = await loadFromFile(filePath);

    final validationResult = JsonUtils.validateSchemaWithErrors(data, schema);
    if (!validationResult.isValid) {
      final errors =
          validationResult.errors.map((e) => e.toString()).join(', ');
      throw JsonValidationException(
          'JSON data does not match schema: $errors', filePath);
    }

    return data;
  }

  /// Load JSON with fallback sources
  static Future<Map<String, dynamic>> loadWithFallback(
    List<JsonSource> sources,
  ) async {
    for (final source in sources) {
      try {
        switch (source.type) {
          case JsonSourceType.file:
            return await loadFromFile(source.path);
          case JsonSourceType.url:
            return await loadFromUrl(source.path,
                headers: source.headers, timeout: source.timeout);
          case JsonSourceType.string:
            return loadFromString(source.data!);
        }
      } catch (e) {
        if (source == sources.last) {
          rethrow; // Last source, propagate error
        }
        // Continue to next source
      }
    }

    throw const JsonLoadException('All sources failed to load JSON');
  }

  /// Load JSON with caching
  static Future<Map<String, dynamic>> loadFromFileWithCache(
    String filePath, {
    Duration? maxAge,
    bool forceRefresh = false,
  }) async {
    if (!forceRefresh && _cache.containsKey(filePath)) {
      final cached = _cache[filePath]!;

      if (maxAge == null ||
          DateTime.now().difference(cached.timestamp) < maxAge) {
        return cached.data;
      }
    }

    final data = await loadFromFile(filePath);
    _cache[filePath] = _CacheEntry(data, DateTime.now());

    return data;
  }

  /// Clear cache
  static void clearCache() {
    _cache.clear();
  }

  /// Watch file for changes and reload
  static Stream<Map<String, dynamic>> watchFile(String filePath) async* {
    final file = File(filePath);

    // Yield initial content
    if (await file.exists()) {
      yield await loadFromFile(filePath);
    }

    // Watch for changes
    await for (final event in file.parent.watch()) {
      if (event.path == filePath && event.type == FileSystemEvent.modify) {
        try {
          yield await loadFromFile(filePath);
        } catch (e) {
          // Ignore errors during watch
        }
      }
    }
  }

  /// Load JSON with preprocessing
  static Future<Map<String, dynamic>> loadFromFileWithPreprocessing(
    String filePath,
    String Function(String) preprocessor,
  ) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        throw FileSystemException('File not found', filePath);
      }

      final content = await file.readAsString();
      final processedContent = preprocessor(content);

      return jsonDecode(processedContent) as Map<String, dynamic>;
    } catch (e) {
      throw JsonLoadException(
          'Failed to load and preprocess JSON from file $filePath: $e');
    }
  }

  /// Load JSONL (JSON Lines) file
  static Future<List<Map<String, dynamic>>> loadJsonLines(
      String filePath) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        throw FileSystemException('File not found', filePath);
      }

      final lines = await file.readAsLines();
      final result = <Map<String, dynamic>>[];

      for (int i = 0; i < lines.length; i++) {
        final line = lines[i].trim();
        if (line.isNotEmpty) {
          try {
            result.add(jsonDecode(line) as Map<String, dynamic>);
          } catch (e) {
            throw JsonLoadException('Invalid JSON on line ${i + 1}: $e');
          }
        }
      }

      return result;
    } catch (e) {
      throw JsonLoadException('Failed to load JSONL from file $filePath: $e');
    }
  }

  /// Save JSON to file
  static Future<void> saveToFile(
    String filePath,
    Map<String, dynamic> data, {
    bool prettyPrint = false,
    int indent = 2,
  }) async {
    try {
      final file = File(filePath);
      await file.parent.create(recursive: true);

      final content = prettyPrint
          ? JsonUtils.prettyPrint(data, indent: indent)
          : jsonEncode(data);

      await file.writeAsString(content);
    } catch (e) {
      throw JsonLoadException('Failed to save JSON to file $filePath: $e');
    }
  }

  /// Save JSONL to file
  static Future<void> saveJsonLines(
    String filePath,
    List<Map<String, dynamic>> data,
  ) async {
    try {
      final file = File(filePath);
      await file.parent.create(recursive: true);

      final lines = data.map(jsonEncode).join('\n');
      await file.writeAsString(lines);
    } catch (e) {
      throw JsonLoadException('Failed to save JSONL to file $filePath: $e');
    }
  }

  /// Create a backup of JSON file before saving
  static Future<void> saveToFileWithBackup(
    String filePath,
    Map<String, dynamic> data, {
    bool prettyPrint = false,
    int indent = 2,
    String backupSuffix = '.bak',
  }) async {
    final file = File(filePath);

    // Create backup if file exists
    if (await file.exists()) {
      final backupPath = '$filePath$backupSuffix';
      await file.copy(backupPath);
    }

    await saveToFile(filePath, data, prettyPrint: prettyPrint, indent: indent);
  }

  /// Merge multiple JSON files
  static Future<Map<String, dynamic>> mergeFiles(
    List<String> filePaths, {
    bool deepMerge = true,
  }) async {
    Map<String, dynamic> result = {};

    for (final filePath in filePaths) {
      final data = await loadFromFile(filePath);

      if (deepMerge) {
        result = JsonUtils.deepMerge(result, data);
      } else {
        result.addAll(data);
      }
    }

    return result;
  }

  // Private members
  static final Map<String, _CacheEntry> _cache = {};

  static bool _matchesPattern(String path, String pattern) {
    // Simple pattern matching - could be enhanced with proper glob support
    if (pattern == '*' || pattern == '*.*') return true;
    if (pattern.startsWith('*.')) {
      return path.endsWith(pattern.substring(1));
    }
    return path.contains(pattern);
  }

  static String _getFileKey(String filePath, String basePath) {
    if (filePath.startsWith(basePath)) {
      return filePath.substring(basePath.length + 1);
    }
    return filePath;
  }
}

/// Represents a JSON source for loading
class JsonSource {
  const JsonSource.string(this.data)
      : type = JsonSourceType.string,
        path = '',
        headers = null,
        timeout = const Duration(seconds: 30);

  const JsonSource.file(this.path)
      : type = JsonSourceType.file,
        data = null,
        headers = null,
        timeout = const Duration(seconds: 30);

  const JsonSource.url(this.path,
      {this.headers, this.timeout = const Duration(seconds: 30)})
      : type = JsonSourceType.url,
        data = null;
  final JsonSourceType type;
  final String path;
  final String? data;
  final Map<String, String>? headers;
  final Duration timeout;
}

/// Types of JSON sources
enum JsonSourceType {
  file,
  url,
  string,
}

/// Cache entry for JSON data
class _CacheEntry {
  const _CacheEntry(this.data, this.timestamp);
  final Map<String, dynamic> data;
  final DateTime timestamp;
}

/// Exception thrown when JSON loading fails
class JsonLoadException implements Exception {
  const JsonLoadException(this.message, [this.cause]);
  final String message;
  final dynamic cause;

  @override
  String toString() =>
      'JsonLoadException: $message${cause != null ? ' (caused by: $cause)' : ''}';
}

/// Exception thrown when JSON validation fails
class JsonValidationException extends JsonLoadException {
  const JsonValidationException(super.message, this.filePath);
  final String? filePath;

  @override
  String toString() =>
      'JsonValidationException: $message${filePath != null ? ' in file $filePath' : ''}';
}

/// Configuration for JSON loading behavior
class JsonLoaderConfig {
  const JsonLoaderConfig({
    this.defaultTimeout = const Duration(seconds: 30),
    this.maxFileSize = 10 * 1024 * 1024, // 10MB
    this.enableCaching = true,
    this.defaultCacheMaxAge = const Duration(minutes: 5),
    this.defaultHeaders = const {},
  });
  final Duration defaultTimeout;
  final int maxFileSize;
  final bool enableCaching;
  final Duration defaultCacheMaxAge;
  final Map<String, String> defaultHeaders;
}

/// Global JSON loader with configuration
class ConfigurableJsonLoader {
  const ConfigurableJsonLoader(this.config);
  final JsonLoaderConfig config;

  Future<Map<String, dynamic>> loadFromFile(String filePath) async {
    if (config.enableCaching) {
      return JsonLoader.loadFromFileWithCache(filePath,
          maxAge: config.defaultCacheMaxAge);
    } else {
      return JsonLoader.loadFromFile(filePath);
    }
  }

  Future<Map<String, dynamic>> loadFromUrl(String url,
      {Map<String, String>? headers}) async {
    final mergedHeaders = <String, String>{
      ...config.defaultHeaders,
      if (headers != null) ...headers,
    };

    return JsonLoader.loadFromUrl(
      url,
      headers: mergedHeaders,
      timeout: config.defaultTimeout,
    );
  }
}
