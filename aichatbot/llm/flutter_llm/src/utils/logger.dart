/// Simple logging utility for the LLM module.
///
/// This provides consistent logging across all LLM clients and utilities.

import 'package:logging/logging.dart';

/// Logger instances for different components of the LLM module.
class LLMLoggers {
  /// Logger for OpenAI client operations.
  static final openai = Logger('llm.openai');

  /// Logger for Anthropic client operations.
  static final anthropic = Logger('llm.anthropic');

  /// Logger for Gemini client operations.
  static final gemini = Logger('llm.gemini');

  /// Logger for HTTP client operations.
  static final http = Logger('llm.http');

  /// Logger for general LLM operations.
  static final general = Logger('llm.general');

  /// Configure logging level and output format.
  ///
  /// This should be called once at application startup to set up logging.
  ///
  /// [level] - The minimum log level to output (defaults to INFO)
  /// [hierarchicalLoggingEnabled] - Whether to enable hierarchical logging (defaults to true)
  static void configure(
      {Level level = Level.INFO, bool hierarchicalLoggingEnabled = true}) {
    Logger.root.level = level;
    hierarchicalLoggingEnabled = hierarchicalLoggingEnabled;

    // Set up a simple console handler
    Logger.root.onRecord.listen((record) {
      final time = record.time.toIso8601String();
      final level = record.level.name.padRight(7);
      final logger = record.loggerName.padRight(15);
      print('$time [$level] $logger: ${record.message}');

      if (record.error != null) {
        print('  Error: ${record.error}');
      }

      if (record.stackTrace != null) {
        print('  Stack trace:\n${record.stackTrace}');
      }
    });
  }
}
