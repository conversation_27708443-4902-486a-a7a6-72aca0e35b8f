import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:http/io_client.dart';

/// Helper function to format proxy URL for HttpClient.findProxy
String _formatProxyUrl(String proxyUrl) {
  final uri = Uri.parse(proxyUrl);
  return '${uri.host}:${uri.port}';
}

/// Creates an HTTP client with proxy support
///
/// This utility function creates an HTTP client that can work with HTTP/HTTPS proxies.
/// It supports both environment variables and explicit proxy configuration.
///
/// ## Environment Variables
/// - `HTTP_PROXY` or `http_proxy`: Proxy for HTTP requests
/// - `HTTPS_PROXY` or `https_proxy`: Proxy for HTTPS requests
/// - `NO_PROXY` or `no_proxy`: Comma-separated list of hosts to bypass proxy
///
/// ## Usage
/// ```dart
/// // Use environment variables
/// final client = createProxyHttpClient();
///
/// // Or specify proxy explicitly
/// final client = createProxyHttpClient(
///   proxyUrl: 'http://proxy.example.com:8080',
/// );
///
/// // With authentication
/// final client = createProxyHttpClient(
///   proxyUrl: 'http://username:<EMAIL>:8080',
/// );
/// ```
http.Client createProxyHttpClient({
  String? proxyUrl,
  List<String>? noProxyHosts,
}) {
  final httpClient = HttpClient();

  // Configure proxy
  if (proxyUrl != null) {
    httpClient.findProxy = (uri) => 'PROXY ${_formatProxyUrl(proxyUrl)}';
  } else {
    // Use system environment variables
    final httpProxy = Platform.environment['HTTP_PROXY'] ??
        Platform.environment['http_proxy'];
    final httpsProxy = Platform.environment['HTTPS_PROXY'] ??
        Platform.environment['https_proxy'];
    final noProxy =
        Platform.environment['NO_PROXY'] ?? Platform.environment['no_proxy'];

    if (httpProxy != null || httpsProxy != null) {
      httpClient.findProxy = (uri) {
        // Check if host should bypass proxy
        if (noProxy != null) {
          final bypasses = noProxy.split(',').map((s) => s.trim());
          for (final bypass in bypasses) {
            if (uri.host == bypass || uri.host.endsWith('.$bypass')) {
              return 'DIRECT';
            }
          }
        }

        // Check custom no-proxy hosts
        if (noProxyHosts != null) {
          for (final bypass in noProxyHosts) {
            if (uri.host == bypass || uri.host.endsWith('.$bypass')) {
              return 'DIRECT';
            }
          }
        }

        // Select appropriate proxy based on scheme
        if (uri.scheme == 'https' && httpsProxy != null) {
          return 'PROXY ${_formatProxyUrl(httpsProxy)}';
        } else if (uri.scheme == 'http' && httpProxy != null) {
          return 'PROXY ${_formatProxyUrl(httpProxy)}';
        }

        return 'DIRECT';
      };
    }
  }

  // Configure proxy authentication if needed
  httpClient.authenticateProxy = (host, port, scheme, realm) async {
    // Extract credentials from proxy URL if provided
    if (proxyUrl != null) {
      final uri = Uri.parse(proxyUrl);
      if (uri.userInfo.isNotEmpty) {
        final parts = uri.userInfo.split(':');
        if (parts.length == 2) {
          httpClient.addProxyCredentials(host, port, realm ?? '',
              HttpClientBasicCredentials(parts[0], parts[1]));
          return true;
        }
      }
    }
    return false;
  };

  // Additional security settings
  httpClient.badCertificateCallback = (cert, host, port) {
    // In production, you might want to validate certificates properly
    // For development/testing, you might allow self-signed certificates
    return false; // Reject bad certificates by default
  };

  return IOClient(httpClient);
}

/// Creates a proxy-aware HTTP client from environment or explicit configuration
///
/// This is a convenience wrapper that automatically detects proxy settings.
class ProxyAwareHttpClient extends IOClient {
  ProxyAwareHttpClient({
    String? proxyUrl,
    List<String>? noProxyHosts,
  }) : super(_createInnerClient(proxyUrl, noProxyHosts));

  static HttpClient _createInnerClient(
    String? proxyUrl,
    List<String>? noProxyHosts,
  ) {
    final httpClient = HttpClient();

    if (proxyUrl != null) {
      httpClient.findProxy = (uri) => 'PROXY ${_formatProxyUrl(proxyUrl)}';
    } else {
      final httpProxy = Platform.environment['HTTP_PROXY'] ??
          Platform.environment['http_proxy'];
      final httpsProxy = Platform.environment['HTTPS_PROXY'] ??
          Platform.environment['https_proxy'];
      final noProxy =
          Platform.environment['NO_PROXY'] ?? Platform.environment['no_proxy'];

      if (httpProxy != null || httpsProxy != null) {
        httpClient.findProxy = (uri) {
          if (noProxy != null) {
            final bypasses = noProxy.split(',').map((s) => s.trim());
            for (final bypass in bypasses) {
              if (uri.host == bypass || uri.host.endsWith('.$bypass')) {
                return 'DIRECT';
              }
            }
          }

          if (noProxyHosts != null) {
            for (final bypass in noProxyHosts) {
              if (uri.host == bypass || uri.host.endsWith('.$bypass')) {
                return 'DIRECT';
              }
            }
          }

          if (uri.scheme == 'https' && httpsProxy != null) {
            return 'PROXY ${_formatProxyUrl(httpsProxy)}';
          } else if (uri.scheme == 'http' && httpProxy != null) {
            return 'PROXY ${_formatProxyUrl(httpProxy)}';
          }

          return 'DIRECT';
        };
      }
    }

    httpClient.authenticateProxy = (host, port, scheme, realm) async {
      if (proxyUrl != null) {
        final uri = Uri.parse(proxyUrl);
        if (uri.userInfo.isNotEmpty) {
          final parts = uri.userInfo.split(':');
          if (parts.length == 2) {
            httpClient.addProxyCredentials(host, port, realm ?? '',
                HttpClientBasicCredentials(parts[0], parts[1]));
            return true;
          }
        }
      }
      return false;
    };

    return httpClient;
  }
}
