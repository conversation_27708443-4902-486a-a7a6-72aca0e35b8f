/// Normalize Anthropic stop reasons to standard finish reasons
class StopReasonNormalizer {
  /// Map Anthropic stop reasons to standard finish reasons
  static const Map<String, String> _stopReasonMappings = {
    'end_turn': 'stop',
    'max_tokens': 'length',
    'stop_sequence': 'stop',
    'tool_use': 'function_calls',
    'content_filter': 'content_filter',
  };

  /// Normalize Anthropic stop reason to standard format
  static String normalize(String? stopReason) {
    if (stopReason == null) return 'unknown';

    final normalizedReason = stopReason.toLowerCase();
    return _stopReasonMappings[normalizedReason] ?? 'unknown';
  }

  /// Check if stop reason indicates completion
  static bool isComplete(String? stopReason) {
    final normalized = normalize(stopReason);
    return normalized == 'stop' || normalized == 'function_calls';
  }

  /// Check if stop reason indicates truncation
  static bool isTruncated(String? stopReason) {
    final normalized = normalize(stopReason);
    return normalized == 'length';
  }

  /// Check if stop reason indicates error
  static bool isError(String? stopReason) {
    final normalized = normalize(stopReason);
    return normalized == 'content_filter' || normalized == 'unknown';
  }

  /// Get human-readable description of stop reason
  static String getDescription(String? stopReason) {
    final normalized = normalize(stopReason);

    switch (normalized) {
      case 'stop':
        return 'Completed normally';
      case 'length':
        return 'Stopped due to maximum token limit';
      case 'function_calls':
        return 'Stopped to execute function calls';
      case 'content_filter':
        return 'Stopped due to content policy violation';
      case 'unknown':
      default:
        return 'Stopped for unknown reason';
    }
  }
}
