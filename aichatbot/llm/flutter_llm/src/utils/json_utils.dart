import 'dart:convert';
import 'dart:typed_data';

import 'package:json_schema/json_schema.dart';

/// Utilities for JSON processing and conversion
class JsonUtils {
  /// Deep clone a JSON-serializable object
  static T deepClone<T>(T object) {
    if (object == null) return object;

    try {
      final jsonString = jsonEncode(object);
      return jsonDecode(jsonString) as T;
    } catch (e) {
      throw ArgumentError('Object is not JSON serializable: $e');
    }
  }

  /// Merge two JSON objects deeply
  static Map<String, dynamic> deepMerge(
    Map<String, dynamic> target,
    Map<String, dynamic> source,
  ) {
    final result = Map<String, dynamic>.from(target);

    for (final entry in source.entries) {
      final key = entry.key;
      final value = entry.value;

      if (result.containsKey(key)) {
        final existingValue = result[key];

        if (existingValue is Map<String, dynamic> &&
            value is Map<String, dynamic>) {
          // Recursively merge nested objects
          result[key] = deepMerge(existingValue, value);
        } else if (existingValue is List && value is List) {
          // Merge arrays
          result[key] = [...existingValue, ...value];
        } else {
          // Override with new value
          result[key] = value;
        }
      } else {
        result[key] = value;
      }
    }

    return result;
  }

  /// Get nested value from JSON object using dot notation
  static dynamic getNestedValue(Map<String, dynamic> object, String path) {
    final keys = path.split('.');
    dynamic current = object;

    for (final key in keys) {
      if (current is Map<String, dynamic> && current.containsKey(key)) {
        current = current[key];
      } else {
        return null;
      }
    }

    return current;
  }

  /// Set nested value in JSON object using dot notation
  static void setNestedValue(
      Map<String, dynamic> object, String path, dynamic value) {
    final keys = path.split('.');
    Map<String, dynamic> current = object;

    for (int i = 0; i < keys.length - 1; i++) {
      final key = keys[i];

      if (!current.containsKey(key) || current[key] is! Map<String, dynamic>) {
        current[key] = <String, dynamic>{};
      }

      current = current[key] as Map<String, dynamic>;
    }

    current[keys.last] = value;
  }

  /// Remove nested value from JSON object using dot notation
  static bool removeNestedValue(Map<String, dynamic> object, String path) {
    final keys = path.split('.');
    Map<String, dynamic> current = object;

    for (int i = 0; i < keys.length - 1; i++) {
      final key = keys[i];

      if (current.containsKey(key) && current[key] is Map<String, dynamic>) {
        current = current[key] as Map<String, dynamic>;
      } else {
        return false;
      }
    }

    return current.remove(keys.last) != null;
  }

  /// Flatten a nested JSON object
  static Map<String, dynamic> flatten(
    Map<String, dynamic> object, {
    String separator = '.',
    String prefix = '',
  }) {
    final result = <String, dynamic>{};

    for (final entry in object.entries) {
      final key = prefix.isEmpty ? entry.key : '$prefix$separator${entry.key}';
      final value = entry.value;

      if (value is Map<String, dynamic>) {
        result.addAll(flatten(value, separator: separator, prefix: key));
      } else {
        result[key] = value;
      }
    }

    return result;
  }

  /// Unflatten a flat JSON object
  static Map<String, dynamic> unflatten(
    Map<String, dynamic> flatObject, {
    String separator = '.',
  }) {
    final result = <String, dynamic>{};

    for (final entry in flatObject.entries) {
      setNestedValue(result, entry.key.replaceAll(separator, '.'), entry.value);
    }

    return result;
  }

  /// Filter JSON object by keys
  static Map<String, dynamic> filterKeys(
    Map<String, dynamic> object,
    bool Function(String key) predicate,
  ) {
    final result = <String, dynamic>{};

    for (final entry in object.entries) {
      if (predicate(entry.key)) {
        result[entry.key] = entry.value;
      }
    }

    return result;
  }

  /// Transform JSON object values
  static Map<String, dynamic> transformValues(
    Map<String, dynamic> object,
    dynamic Function(String key, dynamic value) transformer,
  ) {
    final result = <String, dynamic>{};

    for (final entry in object.entries) {
      result[entry.key] = transformer(entry.key, entry.value);
    }

    return result;
  }

  /// Validate JSON schema using json_schema package
  static bool validateSchema(dynamic object, Map<String, dynamic> schema) {
    try {
      final jsonSchema = JsonSchema.create(schema);
      final result = jsonSchema.validate(object);
      return result.isValid;
    } catch (e) {
      return false;
    }
  }

  /// Validate JSON schema with detailed error information
  static ValidationResults validateSchemaWithErrors(
      dynamic object, Map<String, dynamic> schema) {
    try {
      final jsonSchema = JsonSchema.create(schema);
      return jsonSchema.validate(object);
    } catch (e) {
      // For errors during schema creation, return a basic validation result
      // This is a simplified fallback - in practice, you'd want proper error handling
      throw ArgumentError('Schema validation failed: $e');
    }
  }

  /// Pretty print JSON with custom formatting
  static String prettyPrint(
    dynamic object, {
    int indent = 2,
    bool sortKeys = false,
  }) {
    return _prettyPrintValue(object, 0, indent, sortKeys);
  }

  static String _prettyPrintValue(
    dynamic value,
    int currentIndent,
    int indentSize,
    bool sortKeys,
  ) {
    final spaces = ' ' * currentIndent;
    final nextSpaces = ' ' * (currentIndent + indentSize);

    if (value == null) {
      return 'null';
    } else if (value is String) {
      return jsonEncode(value);
    } else if (value is num || value is bool) {
      return value.toString();
    } else if (value is List) {
      if (value.isEmpty) return '[]';

      final items = value
          .map((item) =>
              '$nextSpaces${_prettyPrintValue(item, currentIndent + indentSize, indentSize, sortKeys)}')
          .join(',\n');

      return '[\n$items\n$spaces]';
    } else if (value is Map<String, dynamic>) {
      if (value.isEmpty) return '{}';

      final entries = value.entries.toList();
      if (sortKeys) {
        entries.sort((a, b) => a.key.compareTo(b.key));
      }

      final items = entries
          .map((entry) =>
              '$nextSpaces${jsonEncode(entry.key)}: ${_prettyPrintValue(entry.value, currentIndent + indentSize, indentSize, sortKeys)}')
          .join(',\n');

      return '{\n$items\n$spaces}';
    } else {
      return jsonEncode(value);
    }
  }

  /// Minify JSON by removing unnecessary whitespace
  static String minify(String jsonString) {
    try {
      final object = jsonDecode(jsonString);
      return jsonEncode(object);
    } catch (e) {
      throw ArgumentError('Invalid JSON string: $e');
    }
  }

  /// Check if string is valid JSON
  static bool isValidJson(String jsonString) {
    try {
      jsonDecode(jsonString);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Convert object to JSON with custom encoders
  static String toJsonWithEncoders(
    dynamic object,
    Map<Type, dynamic Function(dynamic)> encoders,
  ) {
    return jsonEncode(object, toEncodable: (nonEncodable) {
      final encoder = encoders[nonEncodable.runtimeType];
      if (encoder != null) {
        return encoder(nonEncodable);
      }

      // Handle common non-encodable types
      if (nonEncodable is DateTime) {
        return nonEncodable.millisecondsSinceEpoch;
      } else if (nonEncodable is Duration) {
        return nonEncodable.inMilliseconds;
      } else if (nonEncodable is Uri) {
        return nonEncodable.toString();
      } else if (nonEncodable is Uint8List) {
        return nonEncodable.toList();
      }

      throw ArgumentError('Cannot encode type ${nonEncodable.runtimeType}');
    });
  }

  /// Parse JSON with custom decoders
  static dynamic fromJsonWithDecoders(
    String jsonString,
    Map<String, dynamic Function(dynamic)> decoders,
  ) {
    final object = jsonDecode(jsonString);
    return _applyDecoders(object, decoders);
  }

  static dynamic _applyDecoders(
    dynamic object,
    Map<String, dynamic Function(dynamic)> decoders,
  ) {
    if (object is Map<String, dynamic>) {
      final result = <String, dynamic>{};

      for (final entry in object.entries) {
        final decoder = decoders[entry.key];
        if (decoder != null) {
          result[entry.key] = decoder(entry.value);
        } else {
          result[entry.key] = _applyDecoders(entry.value, decoders);
        }
      }

      return result;
    } else if (object is List) {
      return object.map((item) => _applyDecoders(item, decoders)).toList();
    } else {
      return object;
    }
  }

  /// Calculate size of JSON object in bytes
  static int calculateSize(dynamic object) {
    final jsonString = jsonEncode(object);
    return utf8.encode(jsonString).length;
  }

  /// Get all paths in a JSON object
  static List<String> getAllPaths(Map<String, dynamic> object,
      {String separator = '.'}) {
    final paths = <String>[];
    _collectPaths(object, '', separator, paths);
    return paths;
  }

  static void _collectPaths(
    dynamic object,
    String currentPath,
    String separator,
    List<String> paths,
  ) {
    if (object is Map<String, dynamic>) {
      for (final entry in object.entries) {
        final newPath = currentPath.isEmpty
            ? entry.key
            : '$currentPath$separator${entry.key}';
        paths.add(newPath);
        _collectPaths(entry.value, newPath, separator, paths);
      }
    } else if (object is List) {
      for (int i = 0; i < object.length; i++) {
        final newPath = '$currentPath[$i]';
        paths.add(newPath);
        _collectPaths(object[i], newPath, separator, paths);
      }
    }
  }

  /// Compare two JSON objects for equality
  static bool deepEquals(dynamic a, dynamic b) {
    if (identical(a, b)) return true;

    if (a is Map && b is Map) {
      if (a.length != b.length) return false;

      for (final key in a.keys) {
        if (!b.containsKey(key) || !deepEquals(a[key], b[key])) {
          return false;
        }
      }

      return true;
    } else if (a is List && b is List) {
      if (a.length != b.length) return false;

      for (int i = 0; i < a.length; i++) {
        if (!deepEquals(a[i], b[i])) return false;
      }

      return true;
    } else {
      return a == b;
    }
  }

  /// Get JSON patch between two objects (simplified RFC 6902)
  static List<Map<String, dynamic>> createPatch(
    Map<String, dynamic> source,
    Map<String, dynamic> target,
  ) {
    final patches = <Map<String, dynamic>>[];
    _createPatches(source, target, '', patches);
    return patches;
  }

  static void _createPatches(
    dynamic source,
    dynamic target,
    String path,
    List<Map<String, dynamic>> patches,
  ) {
    if (source is Map<String, dynamic> && target is Map<String, dynamic>) {
      // Check for removed properties
      for (final key in source.keys) {
        if (!target.containsKey(key)) {
          patches.add({
            'op': 'remove',
            'path': path.isEmpty ? '/$key' : '$path/$key',
          });
        }
      }

      // Check for added and modified properties
      for (final entry in target.entries) {
        final key = entry.key;
        final newPath = path.isEmpty ? '/$key' : '$path/$key';

        if (!source.containsKey(key)) {
          patches.add({
            'op': 'add',
            'path': newPath,
            'value': entry.value,
          });
        } else if (!deepEquals(source[key], entry.value)) {
          if (source[key] is Map && entry.value is Map) {
            _createPatches(source[key], entry.value, newPath, patches);
          } else {
            patches.add({
              'op': 'replace',
              'path': newPath,
              'value': entry.value,
            });
          }
        }
      }
    } else if (!deepEquals(source, target)) {
      patches.add({
        'op': 'replace',
        'path': path.isEmpty ? '/' : path,
        'value': target,
      });
    }
  }
}
