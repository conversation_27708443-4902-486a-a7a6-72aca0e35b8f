import 'dart:async';
import 'dart:convert';

import '../image.dart';

/// Represents a message in a chat completion context
abstract class LLMMessage {
  String get role;
  Map<String, dynamic> toJson();

  /// Get the token count estimate for this message
  int get estimatedTokenCount;

  /// Check if this message contains images
  bool get hasImages => false;

  /// Get all images in this message
  List<Image> get images => [];
}

/// System message for setting context and instructions
class SystemMessage extends LLMMessage {
  SystemMessage(this.content, {this.metadata = const {}});
  final String content;
  final Map<String, dynamic> metadata;

  @override
  String get role => 'system';

  @override
  int get estimatedTokenCount => (content.length / 4).ceil();

  @override
  Map<String, dynamic> toJson() {
    return {
      'role': role,
      'content': content,
      'metadata': metadata,
    };
  }

  @override
  String toString() => 'SystemMessage: $content';
}

/// User message with text and optional images
class UserMessage extends LLMMessage {
  UserMessage({
    this.text,
    List<Image>? images,
    this.metadata = const {},
    this.source = 'user',
    dynamic content,
  })  : _images = images ?? [],
        content = content ?? text;

  final String? text;
  final List<Image> _images;
  final Map<String, dynamic> metadata;
  final String source;
  final dynamic content;

  @override
  String get role => 'user';

  @override
  bool get hasImages => _images.isNotEmpty;

  @override
  List<Image> get images => List.unmodifiable(_images);

  @override
  int get estimatedTokenCount {
    int tokens = 0;
    if (text != null) {
      tokens += (text!.length / 4).ceil();
    }
    // Images typically cost more tokens
    tokens += _images.length * 85; // Rough estimate
    return tokens;
  }

  @override
  Map<String, dynamic> toJson() {
    final content = <Map<String, dynamic>>[];

    if (text != null) {
      content.add({
        'type': 'text',
        'text': text,
      });
    }

    for (final image in _images) {
      content.add({
        'type': 'image_url',
        'image_url': {
          'url': image.toDataUrl(),
          'detail': 'auto',
        },
      });
    }

    return {
      'role': role,
      'content':
          content.length == 1 && content[0]['type'] == 'text' ? text : content,
      'metadata': metadata,
    };
  }

  @override
  String toString() {
    final parts = <String>[];
    if (text != null) parts.add('text: ${text!.substring(0, 50)}...');
    if (_images.isNotEmpty) parts.add('${_images.length} images');
    return 'UserMessage(${parts.join(', ')})';
  }
}

/// Assistant message with text and optional function calls
class AssistantMessage extends LLMMessage {
  AssistantMessage({
    this.text,
    this.functionCalls = const [],
    this.metadata = const {},
    this.source = 'assistant',
  });
  final String? text;
  final List<FunctionCall> functionCalls;
  final Map<String, dynamic> metadata;
  final String? source;

  @override
  String get role => 'assistant';

  @override
  int get estimatedTokenCount {
    int tokens = 0;
    if (text != null) {
      tokens += (text!.length / 4).ceil();
    }
    for (final call in functionCalls) {
      tokens += call.estimatedTokenCount;
    }
    return tokens;
  }

  @override
  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{
      'role': role,
      'metadata': metadata,
    };

    if (text != null) {
      json['content'] = text;
    }

    if (functionCalls.isNotEmpty) {
      json['tool_calls'] = functionCalls.map((call) => call.toJson()).toList();
    }

    return json;
  }

  @override
  String toString() {
    final parts = <String>[];
    if (text != null) {
      final truncatedText = text!.length > 50 ? text!.substring(0, 50) + '...' : text!;
      parts.add('text: $truncatedText');
    }
    if (functionCalls.isNotEmpty)
      parts.add('${functionCalls.length} function calls');
    return 'AssistantMessage(${parts.join(', ')})';
  }
}

/// Function execution result message
class FunctionExecutionResultMessage extends LLMMessage {
  FunctionExecutionResultMessage({
    required this.content,
    this.metadata = const {},
  });

  final List<FunctionExecutionResult> content;
  final Map<String, dynamic> metadata;

  @override
  String get role => 'tool';

  @override
  int get estimatedTokenCount {
    return content.fold(0, (sum, result) => sum + result.estimatedTokenCount);
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'role': role,
      'content': content.map((result) => result.toJson()).toList(),
      'metadata': metadata,
    };
  }

  @override
  String toString() =>
      'FunctionExecutionResultMessage(${content.length} results)';
}

/// Single function execution result
class FunctionExecutionResult {
  FunctionExecutionResult({
    required this.callId,
    required this.content,
    this.isError = false,
    this.metadata = const {},
  });

  final String callId;
  final String content;
  final bool isError;
  final Map<String, dynamic> metadata;

  int get estimatedTokenCount {
    return (content.length / 4).ceil() + 10; // +10 for overhead
  }

  Map<String, dynamic> toJson() {
    return {
      'call_id': callId,
      'content': content,
      'is_error': isError,
      'metadata': metadata,
    };
  }

  @override
  String toString() =>
      'FunctionExecutionResult($callId: ${isError ? 'ERROR' : 'SUCCESS'})';
}

/// Function call representation
class FunctionCall {
  FunctionCall({required this.id, required this.name, required this.arguments});
  final String id;
  final String name;
  final String arguments;

  int get estimatedTokenCount {
    final argsStr = jsonEncode(arguments);
    return (name.length + argsStr.length) ~/ 4 + 5;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': 'function',
      'function': {
        'name': name,
        'arguments': jsonEncode(arguments),
      }
    };
  }

  @override
  String toString() => 'FunctionCall($name with ${arguments.length} args)';
}

/// Abstract base class for chat completion contexts
abstract class ChatCompletionContext {
  /// Get all messages in the context
  List<LLMMessage> get messages;

  /// Add a message to the context
  void addMessage(LLMMessage message);

  /// Add multiple messages to the context
  void addMessages(List<LLMMessage> messages) {
    for (final message in messages) {
      addMessage(message);
    }
  }

  /// Get the estimated token count for all messages
  int get estimatedTokenCount {
    return messages.fold(
        0, (sum, message) => sum + message.estimatedTokenCount);
  }

  /// Check if the context is empty
  bool get isEmpty => messages.isEmpty;

  /// Get the number of messages
  int get messageCount => messages.length;

  /// Clear all messages
  void clear();

  /// Get messages as JSON array
  List<Map<String, dynamic>> toJson() {
    return messages.map((message) => message.toJson()).toList();
  }

  /// Create a copy of this context
  ChatCompletionContext copy();

  /// Get the last message of a specific type
  T? getLastMessage<T extends LLMMessage>() {
    for (int i = messages.length - 1; i >= 0; i--) {
      if (messages[i] is T) {
        return messages[i] as T;
      }
    }
    return null;
  }

  /// Get all messages of a specific type
  List<T> getMessages<T extends LLMMessage>() {
    return messages.whereType<T>().toList();
  }

  /// Get messages within a token limit (from the end)
  List<LLMMessage> getMessagesWithinTokenLimit(int maxTokens) {
    final result = <LLMMessage>[];
    int currentTokens = 0;

    for (int i = messages.length - 1; i >= 0; i--) {
      final message = messages[i];
      final messageTokens = message.estimatedTokenCount;

      if (currentTokens + messageTokens <= maxTokens) {
        result.insert(0, message);
        currentTokens += messageTokens;
      } else {
        break;
      }
    }

    return result;
  }

  /// Remove messages older than the specified duration
  void removeOlderThan(Duration maxAge) {
    final cutoff = DateTime.now().subtract(maxAge);
    // Note: This would require timestamps on messages
    // For now, this is a placeholder
  }

  /// Compress the context by removing or summarizing old messages
  Future<void> compress(
      {int? targetTokenCount, String? summarizationStrategy}) async {
    // This is a placeholder for context compression logic
    // In a real implementation, this might:
    // 1. Summarize old conversations
    // 2. Remove less important messages
    // 3. Keep only recent messages
  }

  /// Get context statistics
  ChatContextStats getStats() {
    final stats = <String, int>{};
    int totalTokens = 0;
    int totalImages = 0;

    for (final message in messages) {
      final type = message.runtimeType.toString();
      stats[type] = (stats[type] ?? 0) + 1;
      totalTokens += message.estimatedTokenCount;
      totalImages += message.images.length;
    }

    return ChatContextStats(
      messageCount: messages.length,
      totalTokens: totalTokens,
      messagesByType: stats,
      totalImages: totalImages,
    );
  }
}

/// Statistics for a chat completion context
class ChatContextStats {
  const ChatContextStats({
    required this.messageCount,
    required this.totalTokens,
    required this.messagesByType,
    required this.totalImages,
  });
  final int messageCount;
  final int totalTokens;
  final Map<String, int> messagesByType;
  final int totalImages;

  @override
  String toString() {
    return 'ChatContextStats($messageCount messages, $totalTokens tokens, $totalImages images)';
  }

  Map<String, dynamic> toJson() {
    return {
      'messageCount': messageCount,
      'totalTokens': totalTokens,
      'messagesByType': messagesByType,
      'totalImages': totalImages,
    };
  }
}

/// Context manager for handling multiple contexts
class ChatContextManager {
  final Map<String, ChatCompletionContext> _contexts = {};

  /// Create a new context
  void createContext(String id, ChatCompletionContext context) {
    _contexts[id] = context;
  }

  /// Get a context by ID
  ChatCompletionContext? getContext(String id) {
    return _contexts[id];
  }

  /// Remove a context
  bool removeContext(String id) {
    return _contexts.remove(id) != null;
  }

  /// Get all context IDs
  List<String> getContextIds() {
    return _contexts.keys.toList();
  }

  /// Clear all contexts
  void clearAll() {
    _contexts.clear();
  }

  /// Get total token count across all contexts
  int getTotalTokenCount() {
    return _contexts.values
        .fold(0, (sum, context) => sum + context.estimatedTokenCount);
  }

  /// Get manager statistics
  Map<String, dynamic> getStats() {
    final contextStats = <String, dynamic>{};
    int totalTokens = 0;
    int totalMessages = 0;

    for (final entry in _contexts.entries) {
      final stats = entry.value.getStats();
      contextStats[entry.key] = stats.toJson();
      totalTokens += stats.totalTokens;
      totalMessages += stats.messageCount;
    }

    return {
      'contextCount': _contexts.length,
      'totalTokens': totalTokens,
      'totalMessages': totalMessages,
      'contexts': contextStats,
    };
  }
}
