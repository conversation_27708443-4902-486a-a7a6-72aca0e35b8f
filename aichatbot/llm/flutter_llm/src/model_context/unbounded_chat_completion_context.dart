import 'chat_completion_context.dart';

/// Unbounded chat completion context that stores all messages without limits
class UnboundedChatCompletionContext extends ChatCompletionContext {
  final List<LLMMessage> _messages = [];

  @override
  List<LLMMessage> get messages => List.unmodifiable(_messages);

  @override
  void addMessage(LLMMessage message) {
    _messages.add(message);
  }

  @override
  void clear() {
    _messages.clear();
  }

  @override
  ChatCompletionContext copy() {
    final copy = UnboundedChatCompletionContext();
    copy.addMessages(_messages);
    return copy;
  }

  /// Remove message at specific index
  bool removeMessageAt(int index) {
    if (index >= 0 && index < _messages.length) {
      _messages.removeAt(index);
      return true;
    }
    return false;
  }

  /// Remove a specific message
  bool removeMessage(LLMMessage message) {
    return _messages.remove(message);
  }

  /// Insert message at specific position
  void insertMessage(int index, LLMMessage message) {
    if (index >= 0 && index <= _messages.length) {
      _messages.insert(index, message);
    }
  }

  /// Replace message at specific index
  bool replaceMessage(int index, LLMMessage message) {
    if (index >= 0 && index < _messages.length) {
      _messages[index] = message;
      return true;
    }
    return false;
  }

  /// Get message at specific index
  LLMMessage? getMessageAt(int index) {
    if (index >= 0 && index < _messages.length) {
      return _messages[index];
    }
    return null;
  }

  /// Find the index of a message
  int indexOfMessage(LLMMessage message) {
    return _messages.indexOf(message);
  }

  /// Remove all messages of a specific type
  int removeMessagesOfType<T extends LLMMessage>() {
    final originalLength = _messages.length;
    _messages.removeWhere((message) => message is T);
    return originalLength - _messages.length;
  }

  /// Remove messages from start to index (exclusive)
  void removeMessagesUpTo(int index) {
    if (index > 0 && index <= _messages.length) {
      _messages.removeRange(0, index);
    }
  }

  /// Remove messages from index to end
  void removeMessagesFrom(int index) {
    if (index >= 0 && index < _messages.length) {
      _messages.removeRange(index, _messages.length);
    }
  }

  /// Get a slice of messages
  List<LLMMessage> getMessageSlice(int start, [int? end]) {
    final actualEnd = end ?? _messages.length;
    if (start >= 0 &&
        start < _messages.length &&
        actualEnd <= _messages.length) {
      return _messages.sublist(start, actualEnd);
    }
    return [];
  }

  /// Reverse the order of messages
  void reverseMessages() {
    _messages.reversed.toList();
  }

  /// Sort messages by a custom comparator
  void sortMessages(int Function(LLMMessage a, LLMMessage b) comparator) {
    _messages.sort(comparator);
  }

  /// Find messages matching a predicate
  List<LLMMessage> findMessages(bool Function(LLMMessage message) predicate) {
    return _messages.where(predicate).toList();
  }

  /// Check if context contains a specific message
  bool containsMessage(LLMMessage message) {
    return _messages.contains(message);
  }

  /// Get the first message matching a predicate
  LLMMessage? findFirstMessage(bool Function(LLMMessage message) predicate) {
    for (final message in _messages) {
      if (predicate(message)) {
        return message;
      }
    }
    return null;
  }

  /// Get the last message matching a predicate
  LLMMessage? findLastMessage(bool Function(LLMMessage message) predicate) {
    for (int i = _messages.length - 1; i >= 0; i--) {
      final message = _messages[i];
      if (predicate(message)) {
        return message;
      }
    }
    return null;
  }

  /// Get messages in reverse order
  List<LLMMessage> get messagesReversed => _messages.reversed.toList();

  /// Append messages from another context
  void appendFrom(ChatCompletionContext other) {
    addMessages(other.messages);
  }

  /// Prepend messages from another context
  void prependFrom(ChatCompletionContext other) {
    final otherMessages = other.messages;
    for (int i = otherMessages.length - 1; i >= 0; i--) {
      _messages.insert(0, otherMessages[i]);
    }
  }

  /// Merge with another context (avoiding duplicates)
  void mergeWith(ChatCompletionContext other,
      {bool Function(LLMMessage, LLMMessage)? isDuplicate}) {
    for (final message in other.messages) {
      bool isdup = false;

      if (isDuplicate != null) {
        for (final existing in _messages) {
          if (isDuplicate(existing, message)) {
            isdup = true;
            break;
          }
        }
      }

      if (!isdup) {
        addMessage(message);
      }
    }
  }

  /// Create a new context with filtered messages
  UnboundedChatCompletionContext filter(
      bool Function(LLMMessage message) predicate) {
    final filtered = UnboundedChatCompletionContext();
    for (final message in _messages) {
      if (predicate(message)) {
        filtered.addMessage(message);
      }
    }
    return filtered;
  }

  /// Transform all messages using a function
  UnboundedChatCompletionContext transform(
      LLMMessage Function(LLMMessage message) transformer) {
    final transformed = UnboundedChatCompletionContext();
    for (final message in _messages) {
      transformed.addMessage(transformer(message));
    }
    return transformed;
  }

  /// Group messages by role
  Map<String, List<LLMMessage>> groupByRole() {
    final groups = <String, List<LLMMessage>>{};

    for (final message in _messages) {
      final role = message.role;
      groups.putIfAbsent(role, () => []).add(message);
    }

    return groups;
  }

  /// Get conversation turns (pairs of user and assistant messages)
  List<ConversationTurn> getConversationTurns() {
    final turns = <ConversationTurn>[];
    UserMessage? currentUser;

    for (final message in _messages) {
      if (message is UserMessage) {
        currentUser = message;
      } else if (message is AssistantMessage && currentUser != null) {
        turns.add(ConversationTurn(currentUser, message));
        currentUser = null;
      }
    }

    return turns;
  }

  /// Get the most recent conversation turn
  ConversationTurn? getLastConversationTurn() {
    final turns = getConversationTurns();
    return turns.isNotEmpty ? turns.last : null;
  }

  /// Count messages by role
  Map<String, int> countMessagesByRole() {
    final counts = <String, int>{};

    for (final message in _messages) {
      final role = message.role;
      counts[role] = (counts[role] ?? 0) + 1;
    }

    return counts;
  }

  /// Get total estimated tokens by role
  Map<String, int> getTokensByRole() {
    final tokens = <String, int>{};

    for (final message in _messages) {
      final role = message.role;
      tokens[role] = (tokens[role] ?? 0) + message.estimatedTokenCount;
    }

    return tokens;
  }

  /// Check if context has alternating user/assistant pattern
  bool hasAlternatingPattern() {
    if (_messages.length < 2) return true;

    for (int i = 1; i < _messages.length; i++) {
      final prev = _messages[i - 1];
      final curr = _messages[i];

      // Check for user -> assistant or assistant -> user pattern
      if (!((prev is UserMessage && curr is AssistantMessage) ||
          (prev is AssistantMessage && curr is UserMessage) ||
          prev is SystemMessage ||
          curr is FunctionExecutionResult)) {
        return false;
      }
    }

    return true;
  }

  /// Fix alternating pattern by removing or rearranging messages
  void enforceAlternatingPattern() {
    final fixed = <LLMMessage>[];
    LLMMessage? lastNonSystem;

    for (final message in _messages) {
      if (message is SystemMessage) {
        // System messages can be placed anywhere
        fixed.add(message);
      } else if (message is FunctionExecutionResult) {
        // Function results follow assistant messages
        fixed.add(message);
      } else if (lastNonSystem == null) {
        // First non-system message
        fixed.add(message);
        lastNonSystem = message;
      } else if ((lastNonSystem is UserMessage &&
              message is AssistantMessage) ||
          (lastNonSystem is AssistantMessage && message is UserMessage)) {
        // Proper alternation
        fixed.add(message);
        lastNonSystem = message;
      }
      // Skip messages that break alternation
    }

    _messages.clear();
    _messages.addAll(fixed);
  }

  @override
  String toString() {
    return 'UnboundedChatCompletionContext(${_messages.length} messages, ${estimatedTokenCount} tokens)';
  }
}

/// Represents a conversation turn between user and assistant
class ConversationTurn {
  final UserMessage userMessage;
  final AssistantMessage assistantMessage;

  const ConversationTurn(this.userMessage, this.assistantMessage);

  int get totalTokens =>
      userMessage.estimatedTokenCount + assistantMessage.estimatedTokenCount;

  bool get hasFunctionCalls => assistantMessage.functionCalls.isNotEmpty;

  List<FunctionCall> get functionCalls => assistantMessage.functionCalls;

  @override
  String toString() {
    return 'ConversationTurn(${totalTokens} tokens, ${hasFunctionCalls ? 'with' : 'no'} function calls)';
  }
}
