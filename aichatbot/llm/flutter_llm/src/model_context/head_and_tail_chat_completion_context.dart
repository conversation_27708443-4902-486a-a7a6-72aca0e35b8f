import 'dart:collection';
import 'chat_completion_context.dart';

/// Chat completion context that keeps the first N and last M messages
class HeadAndTailChatCompletionContext extends ChatCompletionContext {
  final Queue<LLMMessage> _headMessages;
  final Queue<LLMMessage> _tailMessages;
  final int _headSize;
  final int _tailSize;
  final bool _preserveSystemMessages;
  int _totalMessagesAdded = 0;

  /// Create a head-and-tail context
  ///
  /// [headSize] - Number of messages to keep at the beginning
  /// [tailSize] - Number of messages to keep at the end
  /// [preserveSystemMessages] - Whether to always keep system messages in head
  HeadAndTailChatCompletionContext({
    required int headSize,
    required int tailSize,
    bool preserveSystemMessages = true,
  })  : _headSize = headSize,
        _tailSize = tailSize,
        _preserveSystemMessages = preserveSystemMessages,
        _headMessages = Queue<LLMMessage>(),
        _tailMessages = Queue<LLMMessage>();

  @override
  List<LLMMessage> get messages {
    final result = <LLMMessage>[];
    result.addAll(_headMessages);

    // Add a separator marker if there's a gap
    if (_headMessages.isNotEmpty && _tailMessages.isNotEmpty && hasGap) {
      // In a real implementation, you might add a special marker message
      // or handle this differently based on your needs
    }

    result.addAll(_tailMessages);
    return List.unmodifiable(result);
  }

  @override
  void addMessage(LLMMessage message) {
    _totalMessagesAdded++;

    // System messages go to head if preservation is enabled
    if (_preserveSystemMessages && message is SystemMessage) {
      _addToHead(message);
      return;
    }

    // If we haven't filled the head yet, add to head
    if (_headMessages.length < _headSize) {
      _addToHead(message);
      return;
    }

    // If tail isn't full, add to tail
    if (_tailMessages.length < _tailSize) {
      _addToTail(message);
      return;
    }

    // Both head and tail are full, replace oldest in tail
    _tailMessages.removeFirst();
    _addToTail(message);
  }

  @override
  void clear() {
    _headMessages.clear();
    _tailMessages.clear();
    _totalMessagesAdded = 0;
  }

  @override
  ChatCompletionContext copy() {
    final copy = HeadAndTailChatCompletionContext(
      headSize: _headSize,
      tailSize: _tailSize,
      preserveSystemMessages: _preserveSystemMessages,
    );

    // Copy head messages
    for (final message in _headMessages) {
      copy._headMessages.addLast(message);
    }

    // Copy tail messages
    for (final message in _tailMessages) {
      copy._tailMessages.addLast(message);
    }

    copy._totalMessagesAdded = _totalMessagesAdded;
    return copy;
  }

  /// Add message to head buffer
  void _addToHead(LLMMessage message) {
    if (_headMessages.length >= _headSize) {
      _headMessages.removeFirst();
    }
    _headMessages.addLast(message);
  }

  /// Add message to tail buffer
  void _addToTail(LLMMessage message) {
    _tailMessages.addLast(message);
  }

  /// Check if there's a gap between head and tail
  bool get hasGap => _totalMessagesAdded > (_headSize + _tailSize);

  /// Get the number of messages that have been dropped (in the gap)
  int get droppedMessageCount {
    if (!hasGap) return 0;
    return _totalMessagesAdded - (_headMessages.length + _tailMessages.length);
  }

  /// Get head messages
  List<LLMMessage> get headMessages => List.unmodifiable(_headMessages);

  /// Get tail messages
  List<LLMMessage> get tailMessages => List.unmodifiable(_tailMessages);

  /// Get the size configuration
  int get headSize => _headSize;
  int get tailSize => _tailSize;

  /// Get total capacity
  int get totalCapacity => _headSize + _tailSize;

  /// Get current head utilization
  double get headUtilization => (_headMessages.length / _headSize) * 100;

  /// Get current tail utilization
  double get tailUtilization => (_tailMessages.length / _tailSize) * 100;

  /// Check if head is full
  bool get isHeadFull => _headMessages.length >= _headSize;

  /// Check if tail is full
  bool get isTailFull => _tailMessages.length >= _tailSize;

  /// Get the oldest message in head
  LLMMessage? get oldestHeadMessage =>
      _headMessages.isNotEmpty ? _headMessages.first : null;

  /// Get the newest message in head
  LLMMessage? get newestHeadMessage =>
      _headMessages.isNotEmpty ? _headMessages.last : null;

  /// Get the oldest message in tail
  LLMMessage? get oldestTailMessage =>
      _tailMessages.isNotEmpty ? _tailMessages.first : null;

  /// Get the newest message in tail
  LLMMessage? get newestTailMessage =>
      _tailMessages.isNotEmpty ? _tailMessages.last : null;

  /// Get system messages (should be in head)
  List<SystemMessage> getSystemMessages() {
    return _headMessages.whereType<SystemMessage>().toList();
  }

  /// Try to add message to head specifically
  bool tryAddToHead(LLMMessage message) {
    if (_headMessages.length < _headSize) {
      _addToHead(message);
      return true;
    }
    return false;
  }

  /// Try to add message to tail specifically
  bool tryAddToTail(LLMMessage message) {
    if (_tailMessages.length < _tailSize) {
      _addToTail(message);
      return true;
    }
    return false;
  }

  /// Force add to head (removing oldest if necessary)
  void forceAddToHead(LLMMessage message) {
    _addToHead(message);
  }

  /// Force add to tail (removing oldest if necessary)
  void forceAddToTail(LLMMessage message) {
    if (_tailMessages.length >= _tailSize) {
      _tailMessages.removeFirst();
    }
    _addToTail(message);
  }

  /// Remove message from head
  bool removeFromHead(LLMMessage message) {
    return _headMessages.remove(message);
  }

  /// Remove message from tail
  bool removeFromTail(LLMMessage message) {
    return _tailMessages.remove(message);
  }

  /// Clear only the head
  void clearHead() {
    _headMessages.clear();
  }

  /// Clear only the tail
  void clearTail() {
    _tailMessages.clear();
  }

  /// Get the estimated tokens in head
  int get headTokenCount {
    return _headMessages.fold(
        0, (sum, message) => sum + message.estimatedTokenCount);
  }

  /// Get the estimated tokens in tail
  int get tailTokenCount {
    return _tailMessages.fold(
        0, (sum, message) => sum + message.estimatedTokenCount);
  }

  /// Compact head by removing non-essential messages
  int compactHead() {
    if (!_preserveSystemMessages) return 0;

    int removedCount = 0;
    final nonSystemMessages =
        _headMessages.where((msg) => msg is! SystemMessage).toList();

    for (final message in nonSystemMessages) {
      if (_headMessages.remove(message)) {
        removedCount++;
      }
    }

    return removedCount;
  }

  /// Rebalance head and tail sizes
  void rebalance({int? newHeadSize, int? newTailSize}) {
    final allMessages = messages;
    final newHead = newHeadSize ?? _headSize;
    final newTail = newTailSize ?? _tailSize;

    // Create new context with new sizes
    final newContext = HeadAndTailChatCompletionContext(
      headSize: newHead,
      tailSize: newTail,
      preserveSystemMessages: _preserveSystemMessages,
    );

    // Re-add all messages
    for (final message in allMessages) {
      newContext.addMessage(message);
    }

    // Copy state back
    _headMessages.clear();
    _tailMessages.clear();
    _headMessages.addAll(newContext._headMessages);
    _tailMessages.addAll(newContext._tailMessages);
  }

  /// Get context statistics
  @override
  ChatContextStats getStats() {
    // Return base class stats for compatibility
    final allMessages = [..._headMessages, ..._tailMessages];
    final stats = <String, int>{};
    int totalTokens = 0;
    int totalImages = 0;

    for (final message in allMessages) {
      final type = message.runtimeType.toString();
      stats[type] = (stats[type] ?? 0) + 1;
      totalTokens += message.estimatedTokenCount;
      totalImages += message.images.length;
    }

    return ChatContextStats(
      messageCount: allMessages.length,
      totalTokens: totalTokens,
      messagesByType: stats,
      totalImages: totalImages,
    );
  }

  /// Create a summary message for the gap (if any)
  String? createGapSummary() {
    if (!hasGap) return null;

    return '... [${droppedMessageCount} messages omitted] ...';
  }

  @override
  String toString() {
    return 'HeadAndTailChatCompletionContext('
        'head: ${_headMessages.length}/$_headSize, '
        'tail: ${_tailMessages.length}/$_tailSize, '
        'dropped: $droppedMessageCount)';
  }
}

/// Statistics for head-and-tail context
class HeadAndTailStats {
  final int headSize;
  final int tailSize;
  final int headCount;
  final int tailCount;
  final int totalAdded;
  final int droppedCount;
  final int headTokens;
  final int tailTokens;
  final bool hasGap;
  final double headUtilization;
  final double tailUtilization;

  const HeadAndTailStats({
    required this.headSize,
    required this.tailSize,
    required this.headCount,
    required this.tailCount,
    required this.totalAdded,
    required this.droppedCount,
    required this.headTokens,
    required this.tailTokens,
    required this.hasGap,
    required this.headUtilization,
    required this.tailUtilization,
  });

  int get totalMessages => headCount + tailCount;
  int get totalTokens => headTokens + tailTokens;
  int get totalCapacity => headSize + tailSize;
  double get overallUtilization => (totalMessages / totalCapacity) * 100;
  double get compressionRatio =>
      totalAdded > 0 ? (totalMessages / totalAdded) * 100 : 100;

  @override
  String toString() {
    return 'HeadAndTailStats('
        'messages: $totalMessages/$totalCapacity, '
        'dropped: $droppedCount, '
        'compression: ${compressionRatio.toStringAsFixed(1)}%)';
  }

  Map<String, dynamic> toJson() {
    return {
      'headSize': headSize,
      'tailSize': tailSize,
      'headCount': headCount,
      'tailCount': tailCount,
      'totalAdded': totalAdded,
      'droppedCount': droppedCount,
      'headTokens': headTokens,
      'tailTokens': tailTokens,
      'totalTokens': totalTokens,
      'hasGap': hasGap,
      'headUtilization': headUtilization,
      'tailUtilization': tailUtilization,
      'overallUtilization': overallUtilization,
      'compressionRatio': compressionRatio,
    };
  }
}
