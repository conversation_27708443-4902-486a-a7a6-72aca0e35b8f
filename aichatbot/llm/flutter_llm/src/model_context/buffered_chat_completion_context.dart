import 'dart:collection';
import 'chat_completion_context.dart';
import 'unbounded_chat_completion_context.dart';

/// Chat completion context with a fixed buffer size
class BufferedChatCompletionContext extends ChatCompletionContext {
  final Queue<LLMMessage> _messages;
  final int _maxMessages;
  final bool _preserveSystemMessages;
  final bool _preserveLastUserMessage;

  /// Create a buffered context
  ///
  /// [maxMessages] - Maximum number of messages to keep
  /// [preserveSystemMessages] - Whether to always keep system messages
  /// [preserveLastUserMessage] - Whether to always keep the last user message
  BufferedChatCompletionContext({
    required int maxMessages,
    bool preserveSystemMessages = true,
    bool preserveLastUserMessage = true,
  })  : _maxMessages = maxMessages,
        _preserveSystemMessages = preserveSystemMessages,
        _preserveLastUserMessage = preserveLastUserMessage,
        _messages = Queue<LLMMessage>();

  @override
  List<LLMMessage> get messages => List.unmodifiable(_messages);

  @override
  void addMessage(LLMMessage message) {
    _messages.addLast(message);
    _enforceBufferSize();
  }

  @override
  void clear() {
    _messages.clear();
  }

  @override
  ChatCompletionContext copy() {
    final copy = BufferedChatCompletionContext(
      maxMessages: _maxMessages,
      preserveSystemMessages: _preserveSystemMessages,
      preserveLastUserMessage: _preserveLastUserMessage,
    );
    copy.addMessages(_messages.toList());
    return copy;
  }

  /// Get the maximum message capacity
  int get maxMessages => _maxMessages;

  /// Check if the buffer is at capacity
  bool get isAtCapacity => _messages.length >= _maxMessages;

  /// Get the number of available slots
  int get availableSlots => _maxMessages - _messages.length;

  /// Enforce buffer size by removing old messages
  void _enforceBufferSize() {
    while (_messages.length > _maxMessages) {
      _removeOldestEligibleMessage();
    }
  }

  /// Remove the oldest message that can be removed based on preservation rules
  void _removeOldestEligibleMessage() {
    if (_messages.isEmpty) return;

    // Find the first message that can be removed
    for (int i = 0; i < _messages.length; i++) {
      final message = _messages.elementAt(i);

      if (_canRemoveMessage(message, i)) {
        _messages.remove(message);
        return;
      }
    }

    // If no message can be removed based on rules, remove the oldest anyway
    if (_messages.isNotEmpty) {
      _messages.removeFirst();
    }
  }

  /// Check if a message can be removed based on preservation rules
  bool _canRemoveMessage(LLMMessage message, int index) {
    // Never remove system messages if preservation is enabled
    if (_preserveSystemMessages && message is SystemMessage) {
      return false;
    }

    // Never remove the last user message if preservation is enabled
    if (_preserveLastUserMessage && message is UserMessage) {
      // Check if this is the last user message
      for (int i = _messages.length - 1; i > index; i--) {
        if (_messages.elementAt(i) is UserMessage) {
          // There's a newer user message, so this one can be removed
          return true;
        }
      }
      // This is the last user message
      return false;
    }

    return true;
  }

  /// Remove specific message if allowed
  bool removeMessage(LLMMessage message) {
    final index = _messages.toList().indexOf(message);
    if (index == -1) return false;

    if (_canRemoveMessage(message, index)) {
      _messages.remove(message);
      return true;
    }
    return false;
  }

  /// Force add message even if buffer is full
  void forceAddMessage(LLMMessage message) {
    _messages.addLast(message);
    _enforceBufferSize();
  }

  /// Add messages with capacity check
  bool tryAddMessage(LLMMessage message) {
    if (availableSlots > 0) {
      addMessage(message);
      return true;
    }
    return false;
  }

  /// Get the oldest message
  LLMMessage? get oldestMessage =>
      _messages.isNotEmpty ? _messages.first : null;

  /// Get the newest message
  LLMMessage? get newestMessage => _messages.isNotEmpty ? _messages.last : null;

  /// Get system messages (always preserved)
  List<SystemMessage> getSystemMessages() {
    return _messages.whereType<SystemMessage>().toList();
  }

  /// Get the last user message
  UserMessage? getLastUserMessage() {
    for (final message in _messages.toList().reversed) {
      if (message is UserMessage) {
        return message;
      }
    }
    return null;
  }

  /// Get the last assistant message
  AssistantMessage? getLastAssistantMessage() {
    for (final message in _messages.toList().reversed) {
      if (message is AssistantMessage) {
        return message;
      }
    }
    return null;
  }

  /// Resize the buffer
  void resize(int newMaxMessages) {
    if (newMaxMessages <= 0) {
      throw ArgumentError('Buffer size must be positive');
    }

    final oldMaxMessages = _maxMessages;
    // Note: We can't change final fields, so this would require a different approach
    // In a real implementation, you might return a new instance or make _maxMessages non-final

    // For now, we'll just enforce the new size
    while (_messages.length > newMaxMessages) {
      _removeOldestEligibleMessage();
    }
  }

  /// Get buffer utilization as a percentage
  double get utilizationPercentage => (_messages.length / _maxMessages) * 100;

  /// Check if buffer has room for N more messages
  bool hasRoomFor(int messageCount) {
    return availableSlots >= messageCount;
  }

  /// Get messages that would be removed if we added N more messages
  List<LLMMessage> getMessagesAtRisk(int incomingMessageCount) {
    if (availableSlots >= incomingMessageCount) {
      return [];
    }

    final excessCount = incomingMessageCount - availableSlots;
    final atRisk = <LLMMessage>[];

    for (int i = 0; i < _messages.length && atRisk.length < excessCount; i++) {
      final message = _messages.elementAt(i);
      if (_canRemoveMessage(message, i)) {
        atRisk.add(message);
      }
    }

    return atRisk;
  }

  /// Compact the buffer by removing eligible messages until under target size
  int compactTo(int targetSize) {
    if (targetSize >= _messages.length) return 0;

    int removedCount = 0;
    final targetRemoveCount = _messages.length - targetSize;

    while (_messages.length > targetSize && removedCount < targetRemoveCount) {
      final originalSize = _messages.length;
      _removeOldestEligibleMessage();

      if (_messages.length < originalSize) {
        removedCount++;
      } else {
        // No more eligible messages to remove
        break;
      }
    }

    return removedCount;
  }

  /// Get buffer statistics
  BufferStats getBufferStats() {
    final messagesByType = <String, int>{};
    final preservedMessages = <String, int>{};
    int totalTokens = 0;

    for (int i = 0; i < _messages.length; i++) {
      final message = _messages.elementAt(i);
      final type = message.runtimeType.toString();

      messagesByType[type] = (messagesByType[type] ?? 0) + 1;
      totalTokens += message.estimatedTokenCount;

      if (!_canRemoveMessage(message, i)) {
        preservedMessages[type] = (preservedMessages[type] ?? 0) + 1;
      }
    }

    return BufferStats(
      currentSize: _messages.length,
      maxSize: _maxMessages,
      utilizationPercentage: utilizationPercentage,
      totalTokens: totalTokens,
      messagesByType: messagesByType,
      preservedMessages: preservedMessages,
      availableSlots: availableSlots,
    );
  }

  /// Convert to unbounded context
  UnboundedChatCompletionContext toUnbounded() {
    final unbounded = UnboundedChatCompletionContext();
    unbounded.addMessages(_messages.toList());
    return unbounded;
  }

  @override
  String toString() {
    return 'BufferedChatCompletionContext(${_messages.length}/$_maxMessages messages, '
        '${utilizationPercentage.toStringAsFixed(1)}% full)';
  }
}

/// Statistics for a buffered context
class BufferStats {
  final int currentSize;
  final int maxSize;
  final double utilizationPercentage;
  final int totalTokens;
  final Map<String, int> messagesByType;
  final Map<String, int> preservedMessages;
  final int availableSlots;

  const BufferStats({
    required this.currentSize,
    required this.maxSize,
    required this.utilizationPercentage,
    required this.totalTokens,
    required this.messagesByType,
    required this.preservedMessages,
    required this.availableSlots,
  });

  bool get isFull => currentSize >= maxSize;
  bool get isEmpty => currentSize == 0;
  bool get hasPreservedMessages => preservedMessages.isNotEmpty;

  @override
  String toString() {
    return 'BufferStats($currentSize/$maxSize messages, ${utilizationPercentage.toStringAsFixed(1)}% full, $totalTokens tokens)';
  }

  Map<String, dynamic> toJson() {
    return {
      'currentSize': currentSize,
      'maxSize': maxSize,
      'utilizationPercentage': utilizationPercentage,
      'totalTokens': totalTokens,
      'messagesByType': messagesByType,
      'preservedMessages': preservedMessages,
      'availableSlots': availableSlots,
      'isFull': isFull,
      'isEmpty': isEmpty,
      'hasPreservedMessages': hasPreservedMessages,
    };
  }
}
