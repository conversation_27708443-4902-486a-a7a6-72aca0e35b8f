import 'dart:collection';
import 'chat_completion_context.dart';

/// Chat completion context that maintains a maximum token count
class TokenLimitedChatCompletionContext extends ChatCompletionContext {
  final Queue<LLMMessage> _messages = Queue<LLMMessage>();
  final int _maxTokens;
  final bool _preserveSystemMessages;
  final bool _preserveLastUserMessage;
  final TokenEvictionStrategy _evictionStrategy;

  /// Create a token-limited context
  ///
  /// [maxTokens] - Maximum total token count
  /// [preserveSystemMessages] - Whether to always keep system messages
  /// [preserveLastUserMessage] - Whether to always keep the last user message
  /// [evictionStrategy] - Strategy for removing messages when limit is exceeded
  TokenLimitedChatCompletionContext({
    required int maxTokens,
    bool preserveSystemMessages = true,
    bool preserveLastUserMessage = true,
    TokenEvictionStrategy evictionStrategy = TokenEvictionStrategy.fifo,
  })  : _maxTokens = maxTokens,
        _preserveSystemMessages = preserveSystemMessages,
        _preserveLastUserMessage = preserveLastUserMessage,
        _evictionStrategy = evictionStrategy;

  @override
  List<LLMMessage> get messages => List.unmodifiable(_messages);

  @override
  int get estimatedTokenCount {
    return _messages.fold(
        0, (sum, message) => sum + message.estimatedTokenCount);
  }

  @override
  void addMessage(LLMMessage message) {
    _messages.addLast(message);
    _enforceTokenLimit();
  }

  @override
  void clear() {
    _messages.clear();
  }

  @override
  ChatCompletionContext copy() {
    final copy = TokenLimitedChatCompletionContext(
      maxTokens: _maxTokens,
      preserveSystemMessages: _preserveSystemMessages,
      preserveLastUserMessage: _preserveLastUserMessage,
      evictionStrategy: _evictionStrategy,
    );
    copy.addMessages(_messages.toList());
    return copy;
  }

  /// Get the maximum token limit
  int get maxTokens => _maxTokens;

  /// Get remaining token capacity
  int get remainingTokens => _maxTokens - estimatedTokenCount;

  /// Check if adding a message would exceed the token limit
  bool wouldExceedLimit(LLMMessage message) {
    return estimatedTokenCount + message.estimatedTokenCount > _maxTokens;
  }

  /// Get token utilization as a percentage
  double get tokenUtilization => (estimatedTokenCount / _maxTokens) * 100;

  /// Check if the context is at or near the token limit
  bool get isAtLimit => estimatedTokenCount >= _maxTokens;

  /// Check if the context is near the token limit (within 10%)
  bool get isNearLimit => tokenUtilization >= 90.0;

  /// Enforce the token limit by removing messages
  void _enforceTokenLimit() {
    while (estimatedTokenCount > _maxTokens && _messages.isNotEmpty) {
      if (!_removeMessageByStrategy()) {
        // If we can't remove any more messages, break to avoid infinite loop
        break;
      }
    }
  }

  /// Remove a message based on the eviction strategy
  bool _removeMessageByStrategy() {
    switch (_evictionStrategy) {
      case TokenEvictionStrategy.fifo:
        return _removeFIFO();
      case TokenEvictionStrategy.lifo:
        return _removeLIFO();
      case TokenEvictionStrategy.lru:
        return _removeLRU();
      case TokenEvictionStrategy.largestFirst:
        return _removeLargestFirst();
      case TokenEvictionStrategy.smallestFirst:
        return _removeSmallestFirst();
      case TokenEvictionStrategy.preserveImportant:
        return _removePreservingImportant();
    }
  }

  /// Remove using First-In-First-Out strategy
  bool _removeFIFO() {
    for (final message in _messages) {
      if (_canRemoveMessage(message)) {
        _messages.remove(message);
        return true;
      }
    }
    return false;
  }

  /// Remove using Last-In-First-Out strategy
  bool _removeLIFO() {
    for (final message in _messages.toList().reversed) {
      if (_canRemoveMessage(message)) {
        _messages.remove(message);
        return true;
      }
    }
    return false;
  }

  /// Remove using Least Recently Used strategy (simplified - removes oldest)
  bool _removeLRU() {
    // In a full implementation, you'd track access times
    return _removeFIFO();
  }

  /// Remove the message with the most tokens first
  bool _removeLargestFirst() {
    LLMMessage? largest;
    int maxTokens = 0;

    for (final message in _messages) {
      if (_canRemoveMessage(message) &&
          message.estimatedTokenCount > maxTokens) {
        largest = message;
        maxTokens = message.estimatedTokenCount;
      }
    }

    if (largest != null) {
      _messages.remove(largest);
      return true;
    }
    return false;
  }

  /// Remove the message with the fewest tokens first
  bool _removeSmallestFirst() {
    LLMMessage? smallest;
    int minTokens = double.maxFinite.toInt();

    for (final message in _messages) {
      if (_canRemoveMessage(message) &&
          message.estimatedTokenCount < minTokens) {
        smallest = message;
        minTokens = message.estimatedTokenCount;
      }
    }

    if (smallest != null) {
      _messages.remove(smallest);
      return true;
    }
    return false;
  }

  /// Remove messages while preserving important ones
  bool _removePreservingImportant() {
    // First try to remove non-important messages
    for (final message in _messages) {
      if (_canRemoveMessage(message) && !_isImportantMessage(message)) {
        _messages.remove(message);
        return true;
      }
    }

    // If no non-important messages, fall back to FIFO
    return _removeFIFO();
  }

  /// Check if a message can be removed based on preservation rules
  bool _canRemoveMessage(LLMMessage message) {
    // Never remove system messages if preservation is enabled
    if (_preserveSystemMessages && message is SystemMessage) {
      return false;
    }

    // Never remove the last user message if preservation is enabled
    if (_preserveLastUserMessage && message is UserMessage) {
      // Check if this is the last user message
      UserMessage? lastUser;
      for (final msg in _messages.toList().reversed) {
        if (msg is UserMessage) {
          lastUser = msg;
          break;
        }
      }
      if (lastUser == message) {
        return false;
      }
    }

    return true;
  }

  /// Check if a message is considered important
  bool _isImportantMessage(LLMMessage message) {
    if (message is SystemMessage) return true;
    if (message is FunctionExecutionResult) return true;
    if (message is AssistantMessage && message.functionCalls.isNotEmpty)
      return true;
    return false;
  }

  /// Try to add a message without exceeding the token limit
  bool tryAddMessage(LLMMessage message) {
    if (!wouldExceedLimit(message)) {
      addMessage(message);
      return true;
    }
    return false;
  }

  /// Force add a message, removing others if necessary
  void forceAddMessage(LLMMessage message) {
    _messages.addLast(message);
    _enforceTokenLimit();
  }

  /// Get messages that would be removed if we added a new message
  List<LLMMessage> getMessagesAtRisk(LLMMessage newMessage) {
    final tempContext = copy() as TokenLimitedChatCompletionContext;
    final originalMessages = tempContext._messages.toList();
    tempContext.addMessage(newMessage);
    final finalMessages = tempContext._messages.toSet();

    return originalMessages
        .where((msg) => !finalMessages.contains(msg))
        .toList();
  }

  /// Compact the context to fit within a smaller token limit
  int compactTo(int targetTokens) {
    if (targetTokens >= estimatedTokenCount) return 0;

    int removedCount = 0;
    while (estimatedTokenCount > targetTokens && _messages.isNotEmpty) {
      final originalSize = _messages.length;
      if (_removeMessageByStrategy()) {
        removedCount++;
      } else {
        break; // Can't remove any more messages
      }
    }

    return removedCount;
  }

  /// Get the distribution of tokens by message type
  Map<String, int> getTokenDistribution() {
    final distribution = <String, int>{};

    for (final message in _messages) {
      final type = message.runtimeType.toString();
      distribution[type] =
          (distribution[type] ?? 0) + message.estimatedTokenCount;
    }

    return distribution;
  }

  /// Get the largest message by token count
  LLMMessage? getLargestMessage() {
    if (_messages.isEmpty) return null;

    return _messages.reduce(
        (a, b) => a.estimatedTokenCount > b.estimatedTokenCount ? a : b);
  }

  /// Get the smallest message by token count
  LLMMessage? getSmallestMessage() {
    if (_messages.isEmpty) return null;

    return _messages.reduce(
        (a, b) => a.estimatedTokenCount < b.estimatedTokenCount ? a : b);
  }

  /// Remove messages of a specific type to free up tokens
  int removeMessageType<T extends LLMMessage>() {
    final toRemove = <LLMMessage>[];

    for (final message in _messages) {
      if (message is T && _canRemoveMessage(message)) {
        toRemove.add(message);
      }
    }

    for (final message in toRemove) {
      _messages.remove(message);
    }

    return toRemove.length;
  }

  /// Get context statistics
  @override
  ChatContextStats getStats() {
    final messagesByType = <String, int>{};
    final tokensByType = <String, int>{};
    final preservedMessages = <String, int>{};

    for (final message in _messages) {
      final type = message.runtimeType.toString();
      messagesByType[type] = (messagesByType[type] ?? 0) + 1;
      tokensByType[type] =
          (tokensByType[type] ?? 0) + message.estimatedTokenCount;

      if (!_canRemoveMessage(message)) {
        preservedMessages[type] = (preservedMessages[type] ?? 0) + 1;
      }
    }

    // Return base class compatible stats
    int totalTokens = 0;
    int totalImages = 0;

    for (final message in _messages) {
      totalTokens += message.estimatedTokenCount;
      totalImages += message.images.length;
    }

    return ChatContextStats(
      messageCount: _messages.length,
      totalTokens: totalTokens,
      messagesByType: messagesByType,
      totalImages: totalImages,
    );
  }

  @override
  String toString() {
    return 'TokenLimitedChatCompletionContext('
        '${estimatedTokenCount}/$_maxTokens tokens, '
        '${_messages.length} messages, '
        '${tokenUtilization.toStringAsFixed(1)}% full)';
  }
}

/// Strategies for removing messages when token limit is exceeded
enum TokenEvictionStrategy {
  /// Remove oldest messages first
  fifo,

  /// Remove newest messages first
  lifo,

  /// Remove least recently used messages first
  lru,

  /// Remove messages with the most tokens first
  largestFirst,

  /// Remove messages with the fewest tokens first
  smallestFirst,

  /// Preserve important messages (system, function calls)
  preserveImportant,
}

/// Statistics for token-limited context
class TokenLimitedStats {
  final int maxTokens;
  final int currentTokens;
  final int remainingTokens;
  final double tokenUtilization;
  final int messageCount;
  final Map<String, int> messagesByType;
  final Map<String, int> tokensByType;
  final Map<String, int> preservedMessages;
  final TokenEvictionStrategy evictionStrategy;
  final bool isAtLimit;
  final bool isNearLimit;

  const TokenLimitedStats({
    required this.maxTokens,
    required this.currentTokens,
    required this.remainingTokens,
    required this.tokenUtilization,
    required this.messageCount,
    required this.messagesByType,
    required this.tokensByType,
    required this.preservedMessages,
    required this.evictionStrategy,
    required this.isAtLimit,
    required this.isNearLimit,
  });

  double get averageTokensPerMessage =>
      messageCount > 0 ? currentTokens / messageCount : 0;

  @override
  String toString() {
    return 'TokenLimitedStats('
        '$currentTokens/$maxTokens tokens, '
        '$messageCount messages, '
        '${tokenUtilization.toStringAsFixed(1)}% full)';
  }

  Map<String, dynamic> toJson() {
    return {
      'maxTokens': maxTokens,
      'currentTokens': currentTokens,
      'remainingTokens': remainingTokens,
      'tokenUtilization': tokenUtilization,
      'messageCount': messageCount,
      'messagesByType': messagesByType,
      'tokensByType': tokensByType,
      'preservedMessages': preservedMessages,
      'evictionStrategy': evictionStrategy.toString(),
      'isAtLimit': isAtLimit,
      'isNearLimit': isNearLimit,
      'averageTokensPerMessage': averageTokensPerMessage,
    };
  }
}
