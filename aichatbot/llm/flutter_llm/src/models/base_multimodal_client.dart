import 'dart:async';

import 'package:cancellation_token/cancellation_token.dart';

import 'base_llm_client.dart';
import 'llm_response.dart';
import 'model_client.dart';
import 'model_enums.dart';
import 'request_types.dart';

/// Abstract base class for multi-modal LLM clients that support both 
/// chat completion and embedding functionality.
///
/// This class extends [BaseLLMClient] to leverage its HTTP transport,
/// retry logic, and usage tracking capabilities while adding support
/// for embedding operations through the [EmbeddingClient] interface.
///
/// ## Supported Capabilities
///
/// - All [BaseLLMClient] functionality (chat completion, streaming, etc.)
/// - Text embedding generation through standardized interface
/// - Batch embedding operations for efficiency
/// - Unified model information and resource management
///
/// ## Template Method for Embeddings
///
/// Similar to chat completions, this class uses the Template Method pattern
/// for embedding operations:
/// 
/// ```
/// 1. validateEmbeddingRequest() - Validate input parameters
/// 2. buildEmbeddingRequest() - Transform to provider format  
/// 3. executeRequest() - HTTP transport (inherited)
/// 4. parseEmbeddingResponse() - Parse provider response
/// 5. updateUsage() - Track usage statistics (inherited)
/// ```
///
/// ## Usage
///
/// This class is not meant to be used directly. Use concrete implementations
/// like [GeminiClient] for providers that support both chat and embeddings.
///
/// ```dart
/// final client = GeminiClient(
///   model: 'gemini-1.5-flash',
///   apiKey: 'your-api-key',
/// );
///
/// // Chat completion
/// final chatResponse = await client.createChatCompletion(
///   messages: [UserMessage(text: 'Hello!')],
/// );
///
/// // Embeddings  
/// final embedResponse = await client.createEmbeddings(
///   inputs: ['Hello world', 'How are you?'],
/// );
/// ```
abstract class BaseMultiModalClient extends BaseLLMClient 
    implements EmbeddingClient {
  
  /// Create a multi-modal client with common configuration
  ///
  /// Parameters inherit from [BaseLLMClient] constructor.
  BaseMultiModalClient({
    required super.model,
    super.timeout,
    super.maxRetries,
    super.retryDelay,
    super.customHeaders,
    super.enableLogging,
    super.httpClient,
  });

  // === Abstract Methods for Embedding Support ===

  /// Validate embedding request parameters before processing
  ///
  /// Each provider implements their specific validation rules.
  /// Should throw ArgumentError for invalid requests.
  void validateEmbeddingRequest(
    List<String> inputs,
    String? encodingFormat,
    int? dimensions,
  );

  /// Build HTTP request body for embedding requests in provider-specific format
  ///
  /// Each provider transforms the universal parameters into their API format.
  Map<String, dynamic> buildEmbeddingRequestBody({
    required List<String> inputs,
    String? encodingFormat,
    int? dimensions,
    Map<String, dynamic>? additionalParams,
  });

  /// Parse HTTP response into EmbeddingResponse
  ///
  /// Each provider parses their embedding API response format.
  EmbeddingResponse parseEmbeddingResponse(Map<String, dynamic> response);

  // === Template Methods for Embeddings ===

  /// Template method for embedding creation requests
  ///
  /// This method defines the standard flow for embedding operations:
  /// 1. Validate request parameters
  /// 2. Build provider-specific request
  /// 3. Execute HTTP request with retry logic
  /// 4. Parse response
  /// 5. Update usage tracking
  @override
  Future<EmbeddingResponse> createEmbeddings({
    required List<String> inputs,
    String? encodingFormat,
    int? dimensions,
    Map<String, dynamic>? additionalParams,
    CancellationToken? cancellationToken,
  }) async {
    // Step 1: Validation
    validateEmbeddingRequest(inputs, encodingFormat, dimensions);

    // Step 2: Build request
    final requestBody = buildEmbeddingRequestBody(
      inputs: inputs,
      encodingFormat: encodingFormat,
      dimensions: dimensions,
      additionalParams: additionalParams,
    );

    // Step 3: Build URI and execute HTTP request
    final uri = buildRequestUri(RequestType.embedding, requestBody);
    final headers = <String, String>{};
    headers.addAll(defaultHeaders);
    headers.addAll(customHeaders);

    final responseData = await executeHttpRequest(
      uri: uri,
      requestBody: requestBody,
      headers: headers,
      cancellationToken: cancellationToken,
    );

    // Step 4: Parse response
    final response = parseEmbeddingResponse(responseData);

    // Step 5: Set the original inputs in the response
    final updatedResponse = EmbeddingResponse(
      content: response.content,
      inputs: inputs,
      usage: response.usage,
      modelInfo: response.modelInfo,
      metadata: response.metadata,
      timestamp: response.timestamp,
    );

    // Step 6: Update usage tracking (if embedding responses include usage)
    if (updatedResponse.usage != null) {
      // Note: Usage tracking update method is protected in base class
      // Subclasses can override this behavior if needed
    }

    return updatedResponse;
  }

  /// Get embedding dimension for this client's model
  ///
  /// Default implementation attempts to extract from model capabilities.
  /// Subclasses should override if they have specific dimension information.
  @override
  int get embeddingDimension {
    // Provider-specific dimension lookup could be implemented here
    // For now, return a default that subclasses should override
    return 1536; // Common default for many models
  }

  /// Check if this client supports embedding operations
  ///
  /// This is a convenience method that checks the model's capabilities.
  bool get supportsEmbeddings {
    return modelInfo.hasCapability(ModelCapability.embeddings);
  }

  /// Validate that embeddings are supported before performing operations
  ///
  /// This is a helper method for subclasses to use in their validation.
  void ensureEmbeddingSupport() {
    if (!supportsEmbeddings) {
      throw UnsupportedError(
        '${runtimeType} does not support embedding operations. '
        'Model: $model'
      );
    }
  }
}