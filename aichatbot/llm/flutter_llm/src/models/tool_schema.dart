import 'dart:convert';

/// JSON Schema property definition for OpenAI function calling
class PropertySchema {
  const PropertySchema({
    required this.type,
    this.description,
    this.defaultValue,
    this.enumValues,
    this.metadata = const {},
  });

  factory PropertySchema.fromJson(Map<String, dynamic> json) {
    // Extract known fields
    final knownFields = {'type', 'description', 'default', 'enum'};
    final metadata = Map<String, dynamic>.from(json);

    // Remove known fields from metadata
    for (final field in knownFields) {
      metadata.remove(field);
    }

    return PropertySchema(
      type: json['type'] as String,
      description: json['description'] as String?,
      defaultValue: json['default'],
      enumValues: (json['enum'] as List<dynamic>?)?.cast<String>(),
      metadata: metadata,
    );
  }

  final String type;
  final String? description;
  final dynamic defaultValue;
  final List<String>? enumValues;
  final Map<String, dynamic> metadata;

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{
      'type': type,
    };
    if (description != null) json['description'] = description;
    if (defaultValue != null) json['default'] = defaultValue;
    if (enumValues != null) json['enum'] = enumValues;

    // Add metadata fields
    json.addAll(metadata);

    return json;
  }
}

/// TypedDict equivalent for ParametersSchema
class ParametersSchema {
  const ParametersSchema({
    required this.type,
    required this.properties,
    this.required,
    this.additionalProperties,
  });

  factory ParametersSchema.fromJson(Map<String, dynamic> json) {
    final propertiesJson = json['properties'] as Map<String, dynamic>? ?? {};
    final properties = propertiesJson.map((key, value) =>
        MapEntry(key, PropertySchema.fromJson(value as Map<String, dynamic>)));

    return ParametersSchema(
      type: json['type'] as String,
      properties: properties,
      required: (json['required'] as List<dynamic>?)?.cast<String>(),
      additionalProperties: json['additionalProperties'] as bool?,
    );
  }

  final String type;
  final Map<String, PropertySchema> properties;
  final List<String>? required;
  final bool? additionalProperties;

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{
      'type': type,
      'properties':
          properties.map((key, value) => MapEntry(key, value.toJson())),
    };
    if (required != null) json['required'] = required;
    if (additionalProperties != null) {
      json['additionalProperties'] = additionalProperties;
    }
    return json;
  }
}

/// TypedDict equivalent for ToolSchema
class ToolSchema {
  const ToolSchema({
    required this.name,
    this.description,
    this.parameters,
    this.strict,
  });

  factory ToolSchema.fromJson(Map<String, dynamic> json) {
    return ToolSchema(
      name: json['name'] as String,
      parameters: json['parameters'] != null
          ? ParametersSchema.fromJson(
              json['parameters'] as Map<String, dynamic>,
            )
          : null,
      description: json['description'] as String?,
      strict: json['strict'] as bool?,
    );
  }
  final ParametersSchema? parameters;
  final String name;
  final String? description;
  final bool? strict;

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{
      'name': name,
    };
    if (parameters != null) json['parameters'] = parameters!.toJson();
    if (description != null) json['description'] = description;
    if (strict != null) json['strict'] = strict;
    return json;
  }

  String get json => jsonEncode(toJson());
}
