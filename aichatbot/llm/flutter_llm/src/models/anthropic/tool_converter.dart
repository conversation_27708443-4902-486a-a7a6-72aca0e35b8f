import '../tool_schema.dart';

/// Converts tool schemas and tool choices to Anthropic API format
class AnthropicToolConverter {
  /// Convert tool schemas to Anthropic format
  ///
  /// Anthropic API expects tools in the format:
  /// ```json
  /// {
  ///   "name": "function_name",
  ///   "description": "Function description",
  ///   "input_schema": {
  ///     "type": "object",
  ///     "properties": {...},
  ///     "required": [...]
  ///   }
  /// }
  /// ```
  static List<Map<String, dynamic>> convertTools(List<ToolSchema> tools) {
    return tools.map((tool) => convertTool(tool)).toList();
  }

  /// Convert a single tool schema to Anthropic format
  static Map<String, dynamic> convertTool(ToolSchema tool) {
    // Validate tool name (Anthropic has strict naming requirements)
    _validateToolName(tool.name);

    final inputSchema = <String, dynamic>{
      'type': 'object',
      'properties': <String, dynamic>{},
    };

    // Convert parameters if present
    if (tool.parameters != null) {
      final params = tool.parameters!;

      // Transfer properties
      inputSchema['properties'] = _convertProperties(params.properties);

      // Transfer required fields
      if (params.required != null && params.required!.isNotEmpty) {
        inputSchema['required'] = params.required;
      }

      // Handle additional properties
      if (params.additionalProperties != null) {
        inputSchema['additionalProperties'] = params.additionalProperties;
      }
    }

    return {
      'name': tool.name,
      'description': tool.description,
      'input_schema': inputSchema,
    };
  }

  /// Convert tool choice to Anthropic format
  ///
  /// Anthropic supports:
  /// - "auto": Let the model choose
  /// - "any": Force tool usage (equivalent to "required")
  /// - "none": Disable tool usage
  /// - {"type": "tool", "name": "tool_name"}: Force specific tool
  static Map<String, dynamic>? convertToolChoice(String? toolChoice,
      {List<ToolSchema>? availableTools}) {
    if (toolChoice == null) return null;

    switch (toolChoice.toLowerCase()) {
      case 'auto':
        return {'type': 'auto'};

      case 'required':
      case 'any':
        return {'type': 'any'};

      case 'none':
        return {'type': 'none'};

      default:
        // Check if toolChoice is a specific tool name
        if (availableTools != null) {
          final tool =
              availableTools.where((t) => t.name == toolChoice).firstOrNull;
          if (tool != null) {
            return {
              'type': 'tool',
              'name': tool.name,
            };
          }
        }

        // Fallback: treat as specific tool name
        return {
          'type': 'tool',
          'name': toolChoice,
        };
    }
  }

  /// Convert properties to Anthropic format
  static Map<String, dynamic> _convertProperties(
      Map<String, PropertySchema> properties) {
    final result = <String, dynamic>{};

    for (final entry in properties.entries) {
      result[entry.key] = _convertPropertySchema(entry.value);
    }

    return result;
  }

  /// Convert a single property schema
  static Map<String, dynamic> _convertPropertySchema(PropertySchema property) {
    final result = <String, dynamic>{
      'type': property.type,
    };

    if (property.description != null) {
      result['description'] = property.description;
    }

    if (property.defaultValue != null) {
      result['default'] = property.defaultValue;
    }

    // Handle enum values
    if (property.enumValues != null && property.enumValues!.isNotEmpty) {
      result['enum'] = property.enumValues;
    }

    // Add metadata fields (constraints, nested schemas, etc.)
    result.addAll(property.metadata);

    return result;
  }

  /// Validate tool name according to Anthropic requirements
  static void _validateToolName(String name) {
    // Anthropic tool names must be alphanumeric with underscores and hyphens
    // and must be 64 characters or less
    final nameRegex = RegExp(r'^[a-zA-Z0-9_-]+$');

    if (!nameRegex.hasMatch(name)) {
      throw ArgumentError(
          'Invalid tool name: $name. Only letters, numbers, underscores, and hyphens are allowed.');
    }

    if (name.length > 64) {
      throw ArgumentError(
          'Invalid tool name: $name. Name must be 64 characters or less.');
    }

    if (name.isEmpty) {
      throw ArgumentError('Tool name cannot be empty.');
    }
  }

  /// Normalize tool name to meet Anthropic requirements
  static String normalizeToolName(String name) {
    // Replace invalid characters with underscores and limit length
    final normalized = name.replaceAll(RegExp(r'[^a-zA-Z0-9_-]'), '_');
    return normalized.length > 64 ? normalized.substring(0, 64) : normalized;
  }

  /// Parse tool use from Anthropic response
  ///
  /// Anthropic returns tool use in content blocks like:
  /// ```json
  /// {
  ///   "type": "tool_use",
  ///   "id": "call_123",
  ///   "name": "function_name",
  ///   "input": {...}
  /// }
  /// ```
  static Map<String, dynamic> parseToolUse(Map<String, dynamic> toolUseBlock) {
    return {
      'id': toolUseBlock['id'] as String,
      'name': toolUseBlock['name'] as String,
      'arguments': toolUseBlock['input'],
    };
  }

  /// Convert tool result to Anthropic format
  ///
  /// Anthropic expects tool results in user messages with tool_result content blocks:
  /// ```json
  /// {
  ///   "role": "user",
  ///   "content": [
  ///     {
  ///       "type": "tool_result",
  ///       "tool_use_id": "call_123",
  ///       "content": "result content"
  ///     }
  ///   ]
  /// }
  /// ```
  static List<Map<String, dynamic>> convertToolResults(
      List<Map<String, dynamic>> results) {
    return results
        .map((result) => {
              'type': 'tool_result',
              'tool_use_id': result['call_id'] as String,
              'content': result['content'] as String,
              if (result['is_error'] == true) 'is_error': true,
            })
        .toList();
  }

  /// Validate tool choice against available tools
  static void validateToolChoice(
      String? toolChoice, List<ToolSchema> availableTools) {
    if (toolChoice == null) return;

    // Standard choices are always valid
    if (['auto', 'required', 'any', 'none']
        .contains(toolChoice.toLowerCase())) {
      return;
    }

    // Check if specific tool name exists
    final toolExists = availableTools.any((tool) => tool.name == toolChoice);
    if (!toolExists) {
      throw ArgumentError(
          'Tool choice "$toolChoice" references a tool that is not available. '
          'Available tools: ${availableTools.map((t) => t.name).join(', ')}');
    }
  }
}

/// Extension methods for easier tool conversion
extension ToolSchemaAnthropicExtension on ToolSchema {
  /// Convert this tool schema to Anthropic format
  Map<String, dynamic> toAnthropicFormat() {
    return AnthropicToolConverter.convertTool(this);
  }
}

extension ToolSchemaListAnthropicExtension on List<ToolSchema> {
  /// Convert list of tool schemas to Anthropic format
  List<Map<String, dynamic>> toAnthropicFormat() {
    return AnthropicToolConverter.convertTools(this);
  }
}
