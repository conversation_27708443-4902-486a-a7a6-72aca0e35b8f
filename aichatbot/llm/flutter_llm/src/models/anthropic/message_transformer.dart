import 'dart:convert';

import '../../image.dart';
import '../../model_context/chat_completion_context.dart';

/// Transforms messages between autogen-dart format and Anthropic API format
class AnthropicMessageTransformer {
  /// Transform LLM messages to Anthropic format
  ///
  /// Returns a map containing:
  /// - 'system': System message content (if any)
  /// - 'messages': List of conversation messages
  static Map<String, dynamic> transformMessages(List<LLMMessage> messages) {
    String? systemMessage;
    final List<Map<String, dynamic>> anthropicMessages = [];

    // Merge continuous system messages
    final mergedMessages = _mergeSystemMessages(messages);

    for (final message in mergedMessages) {
      if (message is SystemMessage) {
        if (systemMessage != null) {
          throw ArgumentError(
              'Multiple system messages are not supported by Anthropic');
        }
        systemMessage = _transformSystemMessage(message);
      } else {
        final transformed = _transformMessage(message);
        if (transformed is List) {
          anthropicMessages.addAll(transformed.cast<Map<String, dynamic>>());
        } else {
          anthropicMessages.add(transformed as Map<String, dynamic>);
        }
      }
    }

    return {
      if (systemMessage != null) 'system': systemMessage,
      'messages': anthropicMessages,
    };
  }

  /// Merge continuous system messages into a single message
  static List<LLMMessage> _mergeSystemMessages(List<LLMMessage> messages) {
    final result = <LLMMessage>[];
    String systemContent = '';
    int firstSystemIndex = -1;
    int lastSystemIndex = -1;

    for (int i = 0; i < messages.length; i++) {
      final message = messages[i];

      if (message is SystemMessage) {
        if (firstSystemIndex == -1) {
          firstSystemIndex = i;
        } else if (lastSystemIndex + 1 != i) {
          // Non-continuous system messages
          throw ArgumentError(
              'Multiple non-continuous system messages are not supported');
        }

        systemContent += message.content;
        if (i < messages.length - 1 && messages[i + 1] is SystemMessage) {
          systemContent += '\n';
        }
        lastSystemIndex = i;
      } else {
        result.add(message);
      }
    }

    if (systemContent.isNotEmpty) {
      final systemMessage = SystemMessage(systemContent.trim());
      result.insert(
          firstSystemIndex >= result.length ? result.length : firstSystemIndex,
          systemMessage);
    }

    return result;
  }

  /// Transform system message
  static String _transformSystemMessage(SystemMessage message) {
    return _emptyContentToWhitespace(message.content);
  }

  /// Transform user message
  static Map<String, dynamic> _transformUserMessage(UserMessage message) {
    _validateName(message.source);

    if (message.content is String) {
      return {
        'role': 'user',
        'content': _emptyContentToWhitespace(message.content as String),
      };
    } else if (message.content is List) {
      final blocks = <Map<String, dynamic>>[];

      for (final part in message.content as List) {
        if (part is String) {
          blocks.add({
            'type': 'text',
            'text': _emptyContentToWhitespace(part),
          });
        } else if (part is Image) {
          blocks.add(_transformImageContent(part));
        } else {
          throw ArgumentError('Unknown content type: ${part.runtimeType}');
        }
      }

      return {
        'role': 'user',
        'content': blocks,
      };
    } else {
      throw ArgumentError(
          'Invalid user message content type: ${message.content.runtimeType}');
    }
  }

  /// Transform assistant message
  static Map<String, dynamic> _transformAssistantMessage(
      AssistantMessage message) {
    _validateName(message.source ?? 'assistant');

    if (message.functionCalls.isNotEmpty) {
      // Handle function calls
      final contentBlocks = <Map<String, dynamic>>[];

      // Add thought if available
      if (message.text != null && message.text!.isNotEmpty) {
        contentBlocks.add({
          'type': 'text',
          'text': message.text!,
        });
      }

      // Add tool use blocks
      for (final call in message.functionCalls) {
        contentBlocks.add({
          'type': 'tool_use',
          'id': call.id,
          'name': call.name,
          'input': _parseArguments(call.arguments),
        });
      }

      return {
        'role': 'assistant',
        'content': contentBlocks,
      };
    } else {
      // Simple text content
      return {
        'role': 'assistant',
        'content': message.text ?? '',
      };
    }
  }

  /// Transform function execution result message
  static List<Map<String, dynamic>> _transformFunctionResultMessage(
      FunctionExecutionResultMessage message) {
    final contentBlocks = <Map<String, dynamic>>[];

    for (final result in message.content) {
      contentBlocks.add({
        'type': 'tool_result',
        'tool_use_id': result.callId,
        'content': result.content,
        if (result.isError == true) 'is_error': true,
      });
    }

    return [
      {
        'role': 'user',
        'content': contentBlocks,
      }
    ];
  }

  /// Transform image content to Anthropic format
  static Map<String, dynamic> _transformImageContent(Image image) {
    final mimeType = _getMimeTypeFromImage(image);
    final base64Data = image.toBase64();

    return {
      'type': 'image',
      'source': {
        'type': 'base64',
        'media_type': mimeType,
        'data': base64Data,
      },
    };
  }

  /// Get MIME type from image content
  static String _getMimeTypeFromImage(Image image) {
    final base64Data = image.toBase64();
    final imageData = base64Decode(base64Data);

    // Check magic bytes for common image formats
    if (imageData.length >= 4) {
      // JPEG: FF D8 FF
      if (imageData[0] == 0xFF &&
          imageData[1] == 0xD8 &&
          imageData[2] == 0xFF) {
        return 'image/jpeg';
      }

      // PNG: 89 50 4E 47 0D 0A 1A 0A
      if (imageData.length >= 8 &&
          imageData[0] == 0x89 &&
          imageData[1] == 0x50 &&
          imageData[2] == 0x4E &&
          imageData[3] == 0x47 &&
          imageData[4] == 0x0D &&
          imageData[5] == 0x0A &&
          imageData[6] == 0x1A &&
          imageData[7] == 0x0A) {
        return 'image/png';
      }

      // GIF: GIF87a or GIF89a
      if (imageData.length >= 6) {
        final header = String.fromCharCodes(imageData.sublist(0, 6));
        if (header == 'GIF87a' || header == 'GIF89a') {
          return 'image/gif';
        }
      }

      // WebP: RIFF....WEBP
      if (imageData.length >= 12 &&
          imageData[0] == 0x52 &&
          imageData[1] == 0x49 &&
          imageData[2] == 0x46 &&
          imageData[3] == 0x46 &&
          imageData[8] == 0x57 &&
          imageData[9] == 0x45 &&
          imageData[10] == 0x42 &&
          imageData[11] == 0x50) {
        return 'image/webp';
      }
    }

    // Default to JPEG
    return 'image/jpeg';
  }

  /// Transform a single message based on its type
  static dynamic _transformMessage(LLMMessage message) {
    if (message is UserMessage) {
      return _transformUserMessage(message);
    } else if (message is AssistantMessage) {
      return _transformAssistantMessage(message);
    } else if (message is FunctionExecutionResultMessage) {
      return _transformFunctionResultMessage(message);
    } else {
      throw ArgumentError('Unknown message type: ${message.runtimeType}');
    }
  }

  /// Parse function call arguments
  static dynamic _parseArguments(dynamic arguments) {
    if (arguments is String) {
      try {
        return jsonDecode(arguments);
      } catch (e) {
        // If not valid JSON, wrap in a text object
        return {'text': arguments};
      }
    }
    return arguments;
  }

  /// Convert empty content to whitespace (Anthropic requirement)
  static String _emptyContentToWhitespace(String content) {
    return content.trim().isEmpty ? ' ' : content;
  }

  /// Validate name according to Anthropic requirements
  static void _validateName(String name) {
    final nameRegex = RegExp(r'^[a-zA-Z0-9_-]+$');

    if (!nameRegex.hasMatch(name)) {
      throw ArgumentError(
          'Invalid name: $name. Only letters, numbers, underscores, and hyphens are allowed.');
    }

    if (name.length > 64) {
      throw ArgumentError(
          'Invalid name: $name. Name must be 64 characters or less.');
    }
  }

  /// Normalize name to meet Anthropic requirements
  static String normalizeName(String name) {
    final normalized = name.replaceAll(RegExp(r'[^a-zA-Z0-9_-]'), '_');
    return normalized.length > 64 ? normalized.substring(0, 64) : normalized;
  }

  /// Parse Anthropic response to create AssistantMessage
  static AssistantMessage parseAnthropicResponse(
      Map<String, dynamic> response) {
    final content = response['content'] as List<dynamic>;
    String? text;
    final functionCalls = <FunctionCall>[];
    String? thought;

    for (final block in content) {
      final blockData = block as Map<String, dynamic>;
      final blockType = blockData['type'] as String;

      switch (blockType) {
        case 'text':
          final textContent = blockData['text'] as String;
          if (functionCalls.isEmpty) {
            text = (text ?? '') + textContent;
          } else {
            // Text after tool calls is treated as thought
            thought = (thought ?? '') + textContent;
          }
          break;

        case 'tool_use':
          final toolUse = blockData;
          final arguments = toolUse['input'];

          functionCalls.add(FunctionCall(
            id: toolUse['id'] as String,
            name: toolUse['name'] as String,
            arguments: arguments is String ? arguments : jsonEncode(arguments),
          ));
          break;

        default:
          // Ignore unknown block types
          break;
      }
    }

    return AssistantMessage(
      text: text,
      functionCalls: functionCalls,
      metadata: {
        if (thought != null) 'thought': thought,
      },
    );
  }

  /// Strip whitespace from last assistant message (Anthropic requirement)
  static List<LLMMessage> stripLastAssistantMessage(List<LLMMessage> messages) {
    if (messages.isEmpty) return messages;

    final lastMessage = messages.last;
    if (lastMessage is AssistantMessage && lastMessage.text != null) {
      final updatedMessage = AssistantMessage(
        text: lastMessage.text!.trimRight(),
        functionCalls: lastMessage.functionCalls,
        source: lastMessage.source,
        metadata: lastMessage.metadata,
      );

      return [
        ...messages.sublist(0, messages.length - 1),
        updatedMessage,
      ];
    }

    return messages;
  }
}

/// Extension methods for easier message transformation
extension LLMMessageListAnthropicExtension on List<LLMMessage> {
  /// Transform this list of messages to Anthropic format
  Map<String, dynamic> toAnthropicFormat() {
    return AnthropicMessageTransformer.transformMessages(this);
  }
}
