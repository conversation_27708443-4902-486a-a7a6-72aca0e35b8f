import 'dart:async';
import 'dart:convert';

import '../../model_context/chat_completion_context.dart';
import '../../utils/stop_reason_normalizer.dart';
import '../llm_response.dart';
import '../model_types.dart';
import '../llm_auth_exceptions.dart';

/// Handles Anthropic streaming response events
class AnthropicStreamingHandler {
  /// Process Anthropic streaming response
  ///
  /// Anthropic streaming events:
  /// - message_start: Initial message with usage info
  /// - content_block_start: Begins a content block (text or tool_use)
  /// - content_block_delta: Progressive content updates
  /// - content_block_stop: Ends a content block
  /// - message_delta: Final usage and stop reason
  /// - message_stop: End of stream
  static Stream<ChatStreamChunk> processStream(
    Stream<String> rawStream,
    ModelInfo modelInfo,
  ) async* {
    final streamState = _StreamState();

    await for (final line in rawStream) {
      if (line.isEmpty || !line.startsWith('data: ')) continue;

      final data = line.substring(6).trim();
      if (data == '[DONE]') break;

      if (data.isEmpty) continue;

      try {
        final event = jsonDecode(data) as Map<String, dynamic>;
        final chunk = _processEvent(event, streamState, modelInfo);

        if (chunk != null) {
          yield chunk;
        }
      } catch (e) {
        // Skip malformed JSON events
        continue;
      }
    }

    // Yield final chunk if we have accumulated content
    final finalChunk = _createFinalChunk(streamState, modelInfo);
    if (finalChunk != null) {
      yield finalChunk;
    }
  }

  /// Process a single streaming event
  static ChatStreamChunk? _processEvent(
    Map<String, dynamic> event,
    _StreamState state,
    ModelInfo modelInfo,
  ) {
    final eventType = event['type'] as String?;

    switch (eventType) {
      case 'message_start':
        return _handleMessageStart(event, state);

      case 'content_block_start':
        return _handleContentBlockStart(event, state);

      case 'content_block_delta':
        return _handleContentBlockDelta(event, state, modelInfo);

      case 'content_block_stop':
        return _handleContentBlockStop(event, state);

      case 'message_delta':
        return _handleMessageDelta(event, state);

      case 'message_stop':
        return _handleMessageStop(event, state, modelInfo);

      default:
        // Unknown event type, ignore
        return null;
    }
  }

  static ChatStreamChunk? _handleMessageStart(
    Map<String, dynamic> event,
    _StreamState state,
  ) {
    final message = event['message'] as Map<String, dynamic>?;
    if (message?['usage'] != null) {
      final usage = message!['usage'] as Map<String, dynamic>;
      state.inputTokens = usage['input_tokens'] as int? ?? 0;
      state.outputTokens = usage['output_tokens'] as int? ?? 0;
    }

    state.messageId = message?['id'] as String?;
    return null; // Don't yield chunk for message_start
  }

  static ChatStreamChunk? _handleContentBlockStart(
    Map<String, dynamic> event,
    _StreamState state,
  ) {
    final contentBlock = event['content_block'] as Map<String, dynamic>?;
    if (contentBlock == null) return null;

    final blockType = contentBlock['type'] as String?;

    if (blockType == 'text') {
      state.currentBlockType = 'text';
    } else if (blockType == 'tool_use') {
      state.currentBlockType = 'tool_use';
      state.currentToolId = contentBlock['id'] as String?;
      state.currentToolName = contentBlock['name'] as String?;

      // Initialize tool call if not exists
      if (state.currentToolId != null) {
        state.toolCalls[state.currentToolId!] = FunctionCall(
          id: state.currentToolId!,
          name: state.currentToolName ?? '',
          arguments: '',
        );
      }
    }

    return null; // Don't yield chunk for content_block_start
  }

  static ChatStreamChunk? _handleContentBlockDelta(
    Map<String, dynamic> event,
    _StreamState state,
    ModelInfo modelInfo,
  ) {
    final delta = event['delta'] as Map<String, dynamic>?;
    if (delta == null) return null;

    final deltaType = delta['type'] as String?;

    if (deltaType == 'text_delta') {
      final text = delta['text'] as String? ?? '';
      state.textContent.add(text);

      // Yield text chunk
      return ChatStreamChunk(
        id: state.messageId,
        text: text,
        isFirst: state.isFirstChunk,
        modelInfo: modelInfo,
      );
    } else if (deltaType == 'input_json_delta') {
      final partialJson = delta['partial_json'] as String? ?? '';

      if (state.currentToolId != null) {
        final currentCall = state.toolCalls[state.currentToolId!];
        if (currentCall != null) {
          state.toolCalls[state.currentToolId!] = FunctionCall(
            id: currentCall.id,
            name: currentCall.name,
            arguments: currentCall.arguments + partialJson,
          );
        }
      }
    }

    state.isFirstChunk = false;
    return null;
  }

  static ChatStreamChunk? _handleContentBlockStop(
    Map<String, dynamic> event,
    _StreamState state,
  ) {
    state.currentBlockType = null;
    state.currentToolId = null;
    state.currentToolName = null;
    return null; // Don't yield chunk for content_block_stop
  }

  static ChatStreamChunk? _handleMessageDelta(
    Map<String, dynamic> event,
    _StreamState state,
  ) {
    final delta = event['delta'] as Map<String, dynamic>?;
    if (delta == null) return null;

    if (delta['stop_reason'] != null) {
      state.stopReason = delta['stop_reason'] as String;
    }

    final usage = event['usage'] as Map<String, dynamic>?;
    if (usage != null) {
      state.outputTokens = usage['output_tokens'] as int? ?? state.outputTokens;
    }

    return null; // Don't yield chunk for message_delta
  }

  static ChatStreamChunk? _handleMessageStop(
    Map<String, dynamic> event,
    _StreamState state,
    ModelInfo modelInfo,
  ) {
    // This indicates the end of the stream
    state.isComplete = true;
    return null; // Final chunk will be created separately
  }

  /// Create final chunk with complete response
  static ChatStreamChunk? _createFinalChunk(
    _StreamState state,
    ModelInfo modelInfo,
  ) {
    final usage = RequestUsage(
      promptTokens: state.inputTokens,
      completionTokens: state.outputTokens,
      totalTokens: state.inputTokens + state.outputTokens,
    );

    final normalizedStopReason =
        StopReasonNormalizer.normalize(state.stopReason);
    final hasToolCalls = state.toolCalls.isNotEmpty;

    // Parse tool call arguments
    final functionCalls = <FunctionCall>[];
    for (final call in state.toolCalls.values) {
      try {
        // Try to parse JSON arguments
        final parsedArgs = call.arguments.trim().startsWith('{') &&
                call.arguments.trim().endsWith('}')
            ? jsonDecode(call.arguments)
            : call.arguments;

        functionCalls.add(FunctionCall(
          id: call.id,
          name: call.name,
          arguments: parsedArgs is String ? parsedArgs : jsonEncode(parsedArgs),
        ));
      } catch (e) {
        // If JSON parsing fails, keep as string
        functionCalls.add(call);
      }
    }

    return ChatStreamChunk(
      id: state.messageId,
      text: hasToolCalls ? null : state.textContent.join(),
      functionCalls: functionCalls,
      finishReason: normalizedStopReason,
      usage: usage,
      isLast: true,
      modelInfo: modelInfo,
    );
  }
}

/// Internal state for tracking streaming progress
class _StreamState {
  String? messageId;
  String? currentBlockType;
  String? currentToolId;
  String? currentToolName;
  String? stopReason;

  final List<String> textContent = [];
  final Map<String, FunctionCall> toolCalls = {};

  int inputTokens = 0;
  int outputTokens = 0;
  bool isFirstChunk = true;
  bool isComplete = false;
}

/// Stream processor for handling Anthropic Server-Sent Events
class AnthropicStreamProcessor {
  /// Process raw HTTP stream into Anthropic events
  static Stream<String> processRawStream(Stream<List<int>> rawStream) async* {
    String buffer = '';

    await for (final chunk in rawStream) {
      buffer += utf8.decode(chunk);

      // Process complete lines
      while (buffer.contains('\n')) {
        final lineEnd = buffer.indexOf('\n');
        final line = buffer.substring(0, lineEnd).trim();
        buffer = buffer.substring(lineEnd + 1);

        if (line.isNotEmpty) {
          yield line;
        }
      }
    }

    // Process remaining buffer
    if (buffer.trim().isNotEmpty) {
      yield buffer.trim();
    }
  }

  /// Handle stream errors and reconnection
  static Stream<T> withErrorHandling<T>(
    Stream<T> stream, {
    int maxRetries = 3,
    Duration initialDelay = const Duration(seconds: 1),
  }) async* {
    int retryCount = 0;
    Duration delay = initialDelay;

    while (retryCount <= maxRetries) {
      try {
        await for (final item in stream) {
          yield item;
        }
        break; // Stream completed successfully
      } catch (e) {
        retryCount++;

        if (retryCount > maxRetries) {
          throw LLMNetworkException(
              provider: 'anthropic',
              message: 'Stream failed after $maxRetries retries: $e');
        }

        // Wait before retrying
        await Future.delayed(delay);
        delay *= 2; // Exponential backoff
      }
    }
  }

  /// Buffer stream chunks to handle partial events
  static Stream<Map<String, dynamic>> bufferEvents(
      Stream<String> eventStream) async* {
    String eventBuffer = '';

    await for (final line in eventStream) {
      eventBuffer += line + '\n';

      // Check if we have a complete event
      if (line.isEmpty || line.startsWith('data: [DONE]')) {
        if (eventBuffer.trim().isNotEmpty) {
          try {
            // Try to parse buffered event
            final lines = eventBuffer.trim().split('\n');
            for (final eventLine in lines) {
              if (eventLine.startsWith('data: ') &&
                  eventLine != 'data: [DONE]') {
                final data = eventLine.substring(6).trim();
                if (data.isNotEmpty) {
                  yield jsonDecode(data) as Map<String, dynamic>;
                }
              }
            }
          } catch (e) {
            // Skip malformed events
          }
        }
        eventBuffer = '';
      }
    }
  }
}
