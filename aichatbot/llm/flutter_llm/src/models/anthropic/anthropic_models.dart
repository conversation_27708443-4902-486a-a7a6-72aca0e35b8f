import '../model_enums.dart';
import '../model_types.dart';

/// Get all built-in Anthropic model definitions
List<ModelInfo> getAnthropicBuiltinModels() {

  return [
    // Claude 4 models (latest)
    ModelInfo(
      name: 'claude-opus-4-20250514',
      family: 'claude-4-opus',
      description:
          'Most powerful Claude model with exceptional reasoning and creativity',
      capabilities: {
        ModelCapability.textGeneration,
        ModelCapability.imageInput,
        ModelCapability.functionCalling,
        ModelCapability.streaming,
        ModelCapability.systemMessages,
      },
      inputTokens: 0.015, // $15 per 1M tokens
      outputTokens: 0.075, // $75 per 1M tokens
      currency: Currency.usd,
      pricingTier: PricingTier.enterprise,
      maxTokens: 200000,
      maxInputTokens: 200000,
      maxOutputTokens: 4096,
      supportedLanguages: {
        SupportedLanguage.english,
        SupportedLanguage.chineseSimplified,
        SupportedLanguage.japanese,
        SupportedLanguage.spanish,
        SupportedLanguage.french,
        SupportedLanguage.german,
        SupportedLanguage.italian,
        SupportedLanguage.portuguese,
        SupportedLanguage.russian,
      },
    ),

    ModelInfo(
      name: 'claude-sonnet-4-20250514',
      family: 'claude-4-sonnet',
      description: 'Balanced Claude model with strong performance and speed',
      capabilities: {
        ModelCapability.textGeneration,
        ModelCapability.imageInput,
        ModelCapability.functionCalling,
        ModelCapability.streaming,
        ModelCapability.systemMessages,
      },
      inputTokens: 0.003, // $3 per 1M tokens
      outputTokens: 0.015, // $15 per 1M tokens
      currency: Currency.usd,
      pricingTier: PricingTier.premium,
      maxTokens: 200000,
      maxInputTokens: 200000,
      maxOutputTokens: 4096,
      supportedLanguages: {
        SupportedLanguage.english,
        SupportedLanguage.chineseSimplified,
        SupportedLanguage.japanese,
        SupportedLanguage.spanish,
        SupportedLanguage.french,
        SupportedLanguage.german,
        SupportedLanguage.italian,
        SupportedLanguage.portuguese,
        SupportedLanguage.russian,
      },
    ),

    // Claude 3.5 Sonnet
    ModelInfo(
      name: 'claude-3-5-sonnet-20240620',
      family: 'claude-3-5-sonnet',
      description: 'Fast and capable Claude model for most tasks',
      capabilities: {
        ModelCapability.textGeneration,
        ModelCapability.imageInput,
        ModelCapability.functionCalling,
        ModelCapability.streaming,
        ModelCapability.systemMessages,
      },
      inputTokens: 0.003,
      outputTokens: 0.015,
      currency: Currency.usd,
      pricingTier: PricingTier.standard,
      maxTokens: 200000,
      maxInputTokens: 200000,
      maxOutputTokens: 4096,
      supportedLanguages: {
        SupportedLanguage.english,
        SupportedLanguage.chineseSimplified,
        SupportedLanguage.japanese,
        SupportedLanguage.spanish,
        SupportedLanguage.french,
        SupportedLanguage.german,
        SupportedLanguage.italian,
        SupportedLanguage.portuguese,
        SupportedLanguage.russian,
      },
    ),

    // Claude 3 models
    ModelInfo(
      name: 'claude-3-opus-20240229',
      family: 'claude-3-opus',
      description: 'Most powerful Claude 3 model',
      capabilities: {
        ModelCapability.textGeneration,
        ModelCapability.imageInput,
        ModelCapability.functionCalling,
        ModelCapability.streaming,
        ModelCapability.systemMessages,
      },
      inputTokens: 0.015,
      outputTokens: 0.075,
      currency: Currency.usd,
      pricingTier: PricingTier.enterprise,
      maxTokens: 200000,
      maxInputTokens: 200000,
      maxOutputTokens: 4096,
      supportedLanguages: {
        SupportedLanguage.english,
        SupportedLanguage.chineseSimplified,
        SupportedLanguage.japanese,
        SupportedLanguage.spanish,
        SupportedLanguage.french,
        SupportedLanguage.german,
        SupportedLanguage.italian,
        SupportedLanguage.portuguese,
        SupportedLanguage.russian,
      },
    ),

    ModelInfo(
      name: 'claude-3-haiku-20240307',
      family: 'claude-3-haiku',
      description: 'Fastest Claude 3 model for simple tasks',
      capabilities: {
        ModelCapability.textGeneration,
        ModelCapability.imageInput,
        ModelCapability.functionCalling,
        ModelCapability.streaming,
        ModelCapability.systemMessages,
      },
      inputTokens: 0.00025,
      outputTokens: 0.00125,
      currency: Currency.usd,
      pricingTier: PricingTier.standard,
      maxTokens: 200000,
      maxInputTokens: 200000,
      maxOutputTokens: 4096,
      supportedLanguages: {
        SupportedLanguage.english,
        SupportedLanguage.chineseSimplified,
        SupportedLanguage.japanese,
        SupportedLanguage.spanish,
        SupportedLanguage.french,
        SupportedLanguage.german,
        SupportedLanguage.italian,
        SupportedLanguage.portuguese,
        SupportedLanguage.russian,
      },
    ),

    // Legacy models
    ModelInfo(
      name: 'claude-instant-1.2',
      family: 'claude-instant',
      description: 'Legacy Claude Instant model',
      capabilities: {
        ModelCapability.textGeneration,
        ModelCapability.streaming,
        ModelCapability.systemMessages,
      },
      inputTokens: 0.00163,
      outputTokens: 0.00551,
      currency: Currency.usd,
      pricingTier: PricingTier.developer,
      maxTokens: 100000,
      maxInputTokens: 100000,
      maxOutputTokens: 4096,
      supportedLanguages: {SupportedLanguage.english},
    ),
  ];
}

/// Anthropic 模型家族工具类
class AnthropicModelFamily {
  static const String claude4Opus = 'claude-4-opus';
  static const String claude4Sonnet = 'claude-4-sonnet';
  static const String claude35Sonnet = 'claude-3-5-sonnet';
  static const String claude3Opus = 'claude-3-opus';
  static const String claude3Sonnet = 'claude-3-sonnet';
  static const String claude3Haiku = 'claude-3-haiku';
  static const String claude2 = 'claude-2';
  static const String claudeInstant = 'claude-instant';

  static const List<String> all = [
    claude4Opus,
    claude4Sonnet,
    claude35Sonnet,
    claude3Opus,
    claude3Sonnet,
    claude3Haiku,
    claude2,
    claudeInstant,
  ];

  /// 检查家族是否支持视觉能力
  static bool supportsVision(String family) {
    return [
      claude4Opus,
      claude4Sonnet,
      claude35Sonnet,
      claude3Opus,
      claude3Sonnet,
      claude3Haiku,
    ].contains(family);
  }

  /// 检查家族是否支持函数调用
  static bool supportsFunctionCalling(String family) {
    return [
      claude4Opus,
      claude4Sonnet,
      claude35Sonnet,
      claude3Opus,
      claude3Sonnet,
      claude3Haiku,
    ].contains(family);
  }

  /// 检查是否有大上下文窗口
  static bool hasLargeContext(String family) {
    return all.contains(family); // 所有 Claude 模型都有大上下文窗口
  }

  /// 获取家族中的最新模型名称
  static String? getLatestModelName(String family) {
    switch (family) {
      case claude4Opus:
        return 'claude-opus-4-20250514';
      case claude4Sonnet:
        return 'claude-sonnet-4-20250514';
      case claude35Sonnet:
        return 'claude-3-5-sonnet-20240620';
      case claude3Opus:
        return 'claude-3-opus-20240229';
      case claude3Haiku:
        return 'claude-3-haiku-20240307';
      case claude2:
        return 'claude-2.1';
      case claudeInstant:
        return 'claude-instant-1.2';
      default:
        return null;
    }
  }

  /// 根据能力需求推荐模型家族
  static List<String> recommendFamilies({
    bool needsVision = false,
    bool needsFunctionCalling = false,
    bool needsLargeContext = false,
    bool costOptimized = false,
  }) {
    final recommended = <String>[];

    if (costOptimized) {
      recommended.addAll([claude3Haiku, claude35Sonnet]);
    } else if (needsVision || needsFunctionCalling) {
      recommended.addAll([claude4Sonnet, claude35Sonnet, claude3Opus]);
    } else {
      recommended.addAll([claude35Sonnet, claude3Haiku]);
    }

    if (needsLargeContext) {
      // 所有模型都支持大上下文，优先推荐最新的
      recommended.insert(0, claude4Sonnet);
    }

    return recommended.toSet().toList();
  }
}
