/// Anthropic Claude integration for autogen-dart LLM module
///
/// This library provides comprehensive support for Anthropic's Claude models
/// including:
/// - Full message format conversion
/// - <PERSON><PERSON> calling support
/// - Streaming responses
/// - Error handling
/// - Model information and capabilities
///
/// Example usage:
/// ```dart
/// import 'package:llm/src/clients/clients.dart';
///
/// final client = ClientFactory.createAnthropic(
///   model: 'claude-3-5-sonnet-20240620',
///   apiKey: 'your-api-key',
/// );
///
/// final response = await client.createChatCompletion(
///   messages: [
///     UserMessage(text: 'Hello, <PERSON>!'),
///   ],
/// );
///
/// print(response.content.text);
/// ```

// Error handling provided by unified exceptions system
// Message and tool conversion
export 'message_transformer.dart';
// Streaming support
export 'streaming_handler.dart';
export 'tool_converter.dart';
