import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:http/http.dart' as http;
import 'package:llm/llm.dart';

import '../../utils/stop_reason_normalizer.dart';

/// Anthropic Chat Completions client implementation
///
/// This client extends BaseLLMClient to provide Anthropic-specific functionality
/// while leveraging the common HTTP transport, retry logic, and usage tracking
/// from the base class.
///
/// ## Features
///
/// - Full Anthropic Messages API support
/// - Streaming and non-streaming responses
/// - Too<PERSON> calling with Anthropic format
/// - R1 thinking model support with thought extraction
/// - Usage tracking and statistics
/// - Complex streaming response handling
/// - Proper error handling with Anthropic-specific exceptions
///
/// ## Usage
///
/// ```dart
/// // Basic usage
/// final client = AnthropicClient(
///   model: 'claude-3-opus-20240229',
///   apiKey: 'your-api-key',
/// );
///
/// final response = await client.createChatCompletion(
///   messages: [UserMessage(text: 'Hello!')],
/// );
///
/// print(response.content.text);
///
/// // Streaming
/// await for (final chunk in client.createChatCompletionStream(
///   messages: [UserMessage(text: 'Tell me a story')],
/// )) {
///   print(chunk.text);
/// }
///
/// // Usage tracking
/// print('Total tokens used: ${client.totalUsage.promptTokens + client.totalUsage.completionTokens}');
/// ```
class AnthropicClient extends BaseLLMClient {
  // === Anthropic Configuration ===
  final String apiKey;
  final String? customBaseUrl;

  // === Anthropic Components ===
  final ModelInfo _modelInfo;

  // Track last used tools for tool result messages (same as original)
  List<Map<String, dynamic>> _lastUsedTools = [];

  /// Create an Anthropic client
  ///
  /// Parameters:
  /// - [model]: Anthropic model identifier (e.g., 'claude-3-opus-20240229')
  /// - [apiKey]: Anthropic API key (required)
  /// - [baseUrl]: Custom base URL (defaults to https://api.anthropic.com)
  /// - [timeout]: Request timeout duration
  /// - [maxRetries]: Maximum retry attempts on failure
  /// - [retryDelay]: Delay between retry attempts
  /// - [customHeaders]: Additional headers to include
  /// - [enableLogging]: Enable request/response logging
  /// - [modelInfo]: Custom model info (auto-detected if not provided)
  /// - [httpClient]: Custom HTTP client instance
  AnthropicClient({
    required String model,
    required this.apiKey,
    String? baseUrl,
    Duration timeout = const Duration(seconds: 60),
    int maxRetries = 3,
    Duration retryDelay = const Duration(seconds: 1),
    Map<String, String> customHeaders = const {},
    bool enableLogging = false,
    ModelInfo? modelInfo,
    http.Client? httpClient,
  })  : customBaseUrl = baseUrl,
        _modelInfo = modelInfo ??
            ModelRegistry().getModelByNameOrId(model) ??
            (throw Exception('Model not found: $model')),
        super(
          model: model,
          timeout: timeout,
          maxRetries: maxRetries,
          retryDelay: retryDelay,
          customHeaders: customHeaders,
          enableLogging: enableLogging,
          httpClient: httpClient,
        ) {
    if (apiKey.isEmpty) {
      throw LLMAuthenticationException.unauthorized(
          provider: 'anthropic',
          message:
              'API key is required. Provide it via constructor or ANTHROPIC_API_KEY environment variable.');
    }

    // Validate model capabilities
    _validateModelCapabilities();
  }

  /// Create Anthropic client with environment variable API key
  ///
  /// Automatically reads the API key from ANTHROPIC_API_KEY environment variable.
  AnthropicClient.fromEnvironment({
    required String model,
    String? baseUrl,
    Duration timeout = const Duration(seconds: 60),
    int maxRetries = 3,
    Duration retryDelay = const Duration(seconds: 1),
    Map<String, String> customHeaders = const {},
    bool enableLogging = false,
    ModelInfo? modelInfo,
    http.Client? httpClient,
  }) : this(
          model: model,
          apiKey: Platform.environment['ANTHROPIC_API_KEY'] ??
              (throw ArgumentError(
                  'ANTHROPIC_API_KEY environment variable not found')),
          baseUrl: baseUrl,
          timeout: timeout,
          maxRetries: maxRetries,
          retryDelay: retryDelay,
          customHeaders: customHeaders,
          enableLogging: enableLogging,
          modelInfo: modelInfo,
          httpClient: httpClient,
        );

  // === BaseLLMClient Implementation ===

  @override
  Map<String, String> get defaultHeaders {
    final headers = <String, String>{
      'Content-Type': 'application/json',
      'anthropic-version': '2023-06-01',
      'User-Agent': 'autogen-dart/1.0.0',
      'x-api-key': apiKey,
    };

    return headers;
  }

  @override
  ModelInfo get modelInfo => _modelInfo;

  @override
  Uri buildRequestUri(RequestType type, Map<String, dynamic> params) {
    final baseUrl = customBaseUrl ?? 'https://api.anthropic.com';

    switch (type) {
      case RequestType.chatCompletion:
      case RequestType.chatCompletionStream:
        // Anthropic uses the same endpoint for both streaming and non-streaming
        return Uri.parse('$baseUrl/v1/messages');
      case RequestType.tokenCount:
        // Anthropic doesn't have a direct token count endpoint
        throw UnsupportedError(
            'Anthropic does not support direct token counting endpoint');
      case RequestType.listModels:
        // Anthropic doesn't have a models listing endpoint
        throw UnsupportedError('Anthropic does not support listing models');
      case RequestType.modelInfo:
        // Anthropic doesn't have a model info endpoint
        throw UnsupportedError(
            'Anthropic does not support model info endpoint');
      case RequestType.embedding:
      case RequestType.batchEmbedding:
        // Anthropic doesn't support embeddings
        throw UnsupportedError('Anthropic does not support embeddings');
    }
  }

  @override
  void validateRequest(
    List<LLMMessage> messages,
    List<ToolSchema>? tools,
    String? toolChoice,
  ) {
    if (messages.isEmpty) {
      throw LLMBadRequestException(
          provider: 'anthropic', message: 'Messages cannot be empty');
    }

    // Check for vision support if images are present
    if (!_modelInfo.hasCapability(ModelCapability.imageInput)) {
      for (final message in messages) {
        if (message.hasImages) {
          throw LLMCapabilityException(
              provider: 'anthropic',
              message: 'Model $model does not support image input');
        }
      }
    }

    // Validate tools if provided
    if (tools != null && tools.isNotEmpty) {
      if (!_modelInfo.hasCapability(ModelCapability.functionCalling)) {
        throw LLMCapabilityException(
            provider: 'anthropic',
            message: 'Model $model does not support function calling');
      }

      if (toolChoice != null) {
        AnthropicToolConverter.validateToolChoice(toolChoice, tools);
      }
    }
  }

  @override
  Map<String, dynamic> buildRequestBody({
    required List<LLMMessage> messages,
    double? temperature,
    int? maxTokens,
    List<String>? stopSequences,
    List<ToolSchema>? tools,
    String? toolChoice,
    Map<String, dynamic>? additionalParams,
    bool stream = false,
  }) {
    // Transform messages using existing Anthropic transformer
    final transformedMessages = AnthropicMessageTransformer.transformMessages(
        AnthropicMessageTransformer.stripLastAssistantMessage(messages));

    // Convert tools if provided
    List<Map<String, dynamic>>? anthropicTools;
    Map<String, dynamic>? anthropicToolChoice;

    if (tools != null && tools.isNotEmpty) {
      anthropicTools = AnthropicToolConverter.convertTools(tools);
      anthropicToolChoice = AnthropicToolConverter.convertToolChoice(toolChoice,
          availableTools: tools);
      _lastUsedTools = anthropicTools;
    } else if (_hasToolResults(messages)) {
      // Use last used tools if there are tool results but no new tools
      anthropicTools = _lastUsedTools;
    }

    // Build request body
    final body = <String, dynamic>{
      'model': model,
      'messages': transformedMessages['messages'],
      'max_tokens': maxTokens ?? 4096,
      'stream': stream,
    };

    if (transformedMessages['system'] != null) {
      body['system'] = transformedMessages['system'];
    }

    if (temperature != null) {
      body['temperature'] = temperature;
    }

    if (stopSequences != null && stopSequences.isNotEmpty) {
      body['stop_sequences'] = stopSequences;
    }

    if (anthropicTools != null && anthropicTools.isNotEmpty) {
      body['tools'] = anthropicTools;

      if (anthropicToolChoice != null) {
        body['tool_choice'] = anthropicToolChoice;
      }
    }

    if (additionalParams != null) {
      body.addAll(additionalParams);
    }

    return body;
  }

  @override
  ChatResponse parseResponse(Map<String, dynamic> response) {
    // Use existing Anthropic response parsing logic
    final content = response['content'] as List<dynamic>? ?? [];
    final usage = response['usage'] as Map<String, dynamic>?;
    final stopReason = response['stop_reason'] as String?;

    String? text;
    final functionCalls = <FunctionCall>[];
    String? thought;

    // Process content blocks
    for (final block in content) {
      final blockData = block as Map<String, dynamic>;
      final blockType = blockData['type'] as String;

      switch (blockType) {
        case 'text':
          final textContent = blockData['text'] as String;

          // Check if this is R1 thinking model content
          if (_hasThinkingTags(textContent)) {
            final parsed = _parseR1Content(textContent);
            thought = parsed['thought'];
            text = parsed['content'];
          } else {
            text = textContent;
          }
          break;

        case 'tool_use':
          final toolUse = blockData;
          final input = toolUse['input'];

          functionCalls.add(FunctionCall(
            id: toolUse['id'] as String,
            name: toolUse['name'] as String,
            arguments: input is String ? input : jsonEncode(input),
          ));
          break;

        default:
          // Ignore unknown block types
          break;
      }
    }

    final assistantMessage = AssistantMessage(
      text: text,
      functionCalls: functionCalls,
      metadata: {
        if (thought != null) 'thought': thought,
      },
    );

    return ChatResponse(
      content: assistantMessage,
      finishReason: StopReasonNormalizer.normalize(stopReason),
      usage: usage != null ? _parseUsage(usage) : null,
      modelInfo: _modelInfo,
    );
  }

  @override
  Stream<ChatStreamChunk> parseStreamResponse(Stream<String> lines) async* {
    // Use existing complex Anthropic streaming logic
    // Convert String stream to List<int> stream for compatibility
    final byteStream = lines.map((line) => utf8.encode(line));

    yield* AnthropicStreamingHandler.processStream(
      AnthropicStreamProcessor.processRawStream(byteStream),
      _modelInfo,
    );
  }

  @override
  Future<int> countTokens(List<LLMMessage> messages) async {
    // Simple approximation using character count (same as original)
    int totalChars = 0;

    for (final message in messages) {
      totalChars += message.estimatedTokenCount;
    }

    // Rough approximation: 4 characters per token
    return (totalChars / 4).ceil();
  }

  @override
  Exception createHttpException(
      int statusCode, String body, Map<String, dynamic>? errorData) {
    return LLMExceptionFactory.fromHttpResponse(
      provider: 'anthropic',
      statusCode: statusCode,
      responseBody: body,
      errorData: errorData,
    );
  }

  // === Anthropic-Specific Methods ===

  /// Get token count for tools
  int countToolTokens(List<ToolSchema> tools) {
    // Simple approximation for tools
    int totalTokens = 0;

    for (final tool in tools) {
      totalTokens += (tool.name.length / 4).ceil();
      if (tool.description != null) {
        totalTokens += (tool.description!.length / 4).ceil();
      }
      if (tool.parameters != null) {
        final parametersJson = jsonEncode(tool.parameters!.toJson());
        totalTokens += (parametersJson.length / 4).ceil();
      }
    }

    return totalTokens;
  }

  /// Get token limit for this model
  int getTokenLimit() {
    return _modelInfo.effectiveMaxTokens ?? 200000; // Default for Claude models
  }

  /// Validate model capabilities during initialization
  void _validateModelCapabilities() {
    if (_modelInfo.effectiveMaxTokens == null) {
      throw ArgumentError('Model info must include maxTokens for $model');
    }
  }

  /// Check if messages contain tool results
  bool _hasToolResults(List<LLMMessage> messages) {
    return messages.any((msg) => msg is FunctionExecutionResultMessage);
  }

  /// Parse usage information from API response
  RequestUsage _parseUsage(Map<String, dynamic> usage) {
    return RequestUsage(
      promptTokens: usage['input_tokens'] as int?,
      completionTokens: usage['output_tokens'] as int?,
    );
  }

  /// Check if text content has thinking tags (for R1 models)
  bool _hasThinkingTags(String content) {
    return content.contains('<thinking>') && content.contains('</thinking>');
  }

  /// Parse R1 thinking model content
  Map<String, String?> _parseR1Content(String content) {
    final thinkingStart = content.indexOf('<thinking>');
    final thinkingEnd = content.indexOf('</thinking>');

    if (thinkingStart != -1 &&
        thinkingEnd != -1 &&
        thinkingEnd > thinkingStart) {
      final thought = content.substring(thinkingStart + 10, thinkingEnd).trim();
      final remainingContent = content.substring(thinkingEnd + 11).trim();

      return {
        'thought': thought.isEmpty ? null : thought,
        'content': remainingContent.isEmpty ? null : remainingContent,
      };
    }

    return {'thought': null, 'content': content};
  }

  @override
  Future<bool> healthCheck() async {
    try {
      // Try a simple completion request
      await createChatCompletion(
        messages: [UserMessage(text: 'Hi')],
        maxTokens: 1,
      );
      return true;
    } catch (e) {
      return false;
    }
  }
}
