import 'dart:async';
import 'dart:convert';

import 'package:cancellation_token/cancellation_token.dart';
import 'package:http/http.dart' as http;

import '../../../llm.dart';
import 'gemini_message_transformer.dart';

/// Gemini LLM client implementation
///
/// This client extends BaseMultiModalClient to provide Gemini-specific functionality
/// while leveraging the common HTTP transport, retry logic, and usage tracking
/// from the base class.
///
/// ## Features
///
/// - Full Gemini API support
/// - Streaming and non-streaming responses
/// - Multi-modal support (text and images)
/// - Function/tool calling
/// - Text embedding generation (single and batch)
/// - Safety settings and generation configuration
/// - Token counting and usage tracking
/// - Proper error handling with Gemini-specific exceptions
///
/// ## Usage
///
/// ```dart
/// // Basic usage
/// final client = GeminiClient(
///   model: 'gemini-1.5-flash',
///   apiKey: 'your-api-key',
/// );
///
/// final response = await client.createChatCompletion(
///   messages: [UserMessage(text: 'Hello!')],
/// );
///
/// print(response.content.text);
/// ```
class GeminiClient extends BaseMultiModalClient {
  // === Gemini Configuration ===
  final String apiKey;
  final String? customBaseUrl;
  final String apiVersion;
  final List<GeminiSafetySetting>? defaultSafetySettings;
  final GeminiGenerationConfig? defaultGenerationConfig;

  // === Gemini Components ===
  final GeminiMessageTransformer _messageTransformer;
  final ModelInfo _modelInfo;

  /// Create a Gemini client
  ///
  /// Parameters:
  /// - [model]: Gemini model identifier (e.g., 'gemini-1.5-flash', 'gemini-pro')
  /// - [apiKey]: Gemini API key (required)
  /// - [baseUrl]: Custom base URL for Gemini API (optional)
  /// - [apiVersion]: API version (defaults to 'v1beta')
  /// - [timeout]: Request timeout duration
  /// - [maxRetries]: Maximum retry attempts on failure
  /// - [retryDelay]: Delay between retry attempts
  /// - [customHeaders]: Additional headers to include
  /// - [enableLogging]: Enable request/response logging
  /// - [modelInfo]: Custom model info (auto-detected if not provided)
  /// - [defaultSafetySettings]: Default safety settings for all requests
  /// - [defaultGenerationConfig]: Default generation configuration
  GeminiClient({
    required String model,
    required this.apiKey,
    String? baseUrl,
    this.apiVersion = 'v1beta',
    Duration timeout = const Duration(seconds: 60),
    int maxRetries = 3,
    Duration retryDelay = const Duration(seconds: 1),
    Map<String, String> customHeaders = const {},
    bool enableLogging = false,
    ModelInfo? modelInfo,
    this.defaultSafetySettings,
    this.defaultGenerationConfig,
    http.Client? httpClient,
  })  : customBaseUrl = baseUrl,
        _messageTransformer = GeminiMessageTransformer(),
        _modelInfo = modelInfo ??
            ModelRegistry().getModelByNameOrId(model) ??
            (throw Exception('Model not found: $model')),
        super(
            model: model,
            timeout: timeout,
            maxRetries: maxRetries,
            retryDelay: retryDelay,
            customHeaders: customHeaders,
            enableLogging: enableLogging,
            httpClient: httpClient) {
    if (apiKey.isEmpty) {
      throw ArgumentError('Gemini API key cannot be empty');
    }
  }

  // === BaseLLMClient Implementation ===

  @override
  Uri buildRequestUri(RequestType type, Map<String, dynamic> params) {
    final baseUrl =
        customBaseUrl ?? 'https://generativelanguage.googleapis.com';
    final modelName = model.startsWith('models/') ? model : 'models/$model';

    switch (type) {
      case RequestType.chatCompletion:
        return Uri.parse('$baseUrl/$apiVersion/$modelName:generateContent');
      case RequestType.chatCompletionStream:
        return Uri.parse(
            '$baseUrl/$apiVersion/$modelName:streamGenerateContent?alt=sse');
      case RequestType.tokenCount:
        return Uri.parse('$baseUrl/$apiVersion/$modelName:countTokens');
      case RequestType.listModels:
        return Uri.parse('$baseUrl/$apiVersion/models');
      case RequestType.modelInfo:
        final requestedModel = params['model'] ?? model;
        final requestedModelName = requestedModel.startsWith('models/')
            ? requestedModel
            : 'models/$requestedModel';
        return Uri.parse('$baseUrl/$apiVersion/$requestedModelName');
      case RequestType.embedding:
        final embedModel = params['model'] ?? 'models/embedding-001';
        final embedModelName = embedModel.startsWith('models/')
            ? embedModel
            : 'models/$embedModel';
        return Uri.parse('$baseUrl/$apiVersion/$embedModelName:embedContent');
      case RequestType.batchEmbedding:
        final embedModel = params['model'] ?? 'models/embedding-001';
        final embedModelName = embedModel.startsWith('models/')
            ? embedModel
            : 'models/$embedModel';
        return Uri.parse(
            '$baseUrl/$apiVersion/$embedModelName:batchEmbedContents');
    }
  }

  @override
  Map<String, String> get defaultHeaders {
    return {
      'x-goog-api-key': apiKey,
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };
  }

  @override
  ModelInfo get modelInfo => _modelInfo;

  @override
  void validateRequest(
    List<LLMMessage> messages,
    List<ToolSchema>? tools,
    String? toolChoice,
  ) {
    if (messages.isEmpty) {
      throw ArgumentError('Messages cannot be empty');
    }

    // Validate message types
    _messageTransformer.validateMessages(messages);

    // Validate image support
    final hasImages =
        messages.any((msg) => msg is UserMessage && msg.images.isNotEmpty);

    if (hasImages && !_modelInfo.hasCapability(ModelCapability.imageInput)) {
      throw ArgumentError('Model $model does not support image input');
    }

    // Validate tools if provided
    if (tools != null && tools.isNotEmpty) {
      if (!_modelInfo.hasCapability(ModelCapability.functionCalling)) {
        throw ArgumentError('Model $model does not support function calling');
      }
    }
  }

  @override
  Map<String, dynamic> buildRequestBody({
    required List<LLMMessage> messages,
    double? temperature,
    int? maxTokens,
    List<String>? stopSequences,
    List<ToolSchema>? tools,
    String? toolChoice,
    Map<String, dynamic>? additionalParams,
    bool stream = false,
  }) {
    // Extract system prompt if present
    String? systemInstruction;
    final systemMessages = messages.whereType<SystemMessage>().toList();
    if (systemMessages.isNotEmpty) {
      systemInstruction = systemMessages.map((m) => m.content).join('\n');
    }

    // Convert messages to Gemini format
    final contents = _messageTransformer.messagesToGeminiContents(
      messages.where((m) => m is! SystemMessage).toList(),
    );

    final body = <String, dynamic>{
      'contents': contents.map((c) => c.toJson()).toList(),
    };

    // Add system instruction if present
    if (systemInstruction != null) {
      body['system_instruction'] = {
        'parts': [
          {'text': systemInstruction}
        ]
      };
    }

    // Add generation config
    final generationConfig = <String, dynamic>{};
    if (temperature != null) generationConfig['temperature'] = temperature;
    if (maxTokens != null) generationConfig['maxOutputTokens'] = maxTokens;
    if (stopSequences != null && stopSequences.isNotEmpty) {
      generationConfig['stopSequences'] = stopSequences;
    }

    // Merge with default generation config
    if (defaultGenerationConfig != null) {
      final defaultConfig = defaultGenerationConfig!.toJson();
      defaultConfig.addAll(generationConfig);
      generationConfig.addAll(defaultConfig);
    }

    if (generationConfig.isNotEmpty) {
      body['generationConfig'] = generationConfig;
    }

    // Add safety settings
    if (defaultSafetySettings != null && defaultSafetySettings!.isNotEmpty) {
      body['safetySettings'] =
          defaultSafetySettings!.map((s) => s.toJson()).toList();
    }

    // Add tools if provided
    if (tools != null && tools.isNotEmpty) {
      body['tools'] = [
        {
          'functionDeclarations': tools
              .map((tool) => {
                    'name': tool.name,
                    'description': tool.description,
                    'parameters': tool.parameters?.toJson() ?? {},
                  })
              .toList(),
        }
      ];

      // Handle tool choice
      if (toolChoice != null) {
        body['toolConfig'] = {
          'functionCallingConfig': {
            'mode': toolChoice == 'auto'
                ? 'AUTO'
                : toolChoice == 'none'
                    ? 'NONE'
                    : 'ANY',
          }
        };
      }
    }

    // Add any additional parameters
    if (additionalParams != null) {
      body.addAll(additionalParams);
    }

    return body;
  }

  @override
  ChatResponse parseResponse(Map<String, dynamic> response) {
    final geminiResponse = GeminiResponse.fromJson(response);

    if (geminiResponse.candidates == null ||
        geminiResponse.candidates!.isEmpty) {
      throw LLMBadRequestException.fromResponse(
        provider: 'gemini',
        message: 'No candidates in response',
      );
    }

    final candidate = geminiResponse.candidates!.first;
    final assistantMessage =
        _messageTransformer.candidateToAssistantMessage(candidate);

    // Extract usage information
    RequestUsage? usage;
    if (geminiResponse.usageMetadata != null) {
      usage = RequestUsage(
        promptTokens: geminiResponse.usageMetadata!.promptTokenCount,
        completionTokens: geminiResponse.usageMetadata!.candidatesTokenCount,
        totalTokens: geminiResponse.usageMetadata!.totalTokenCount,
      );
    }

    return ChatResponse(
      content: assistantMessage,
      finishReason: candidate.finishReason,
      usage: usage,
      modelInfo: _modelInfo,
    );
  }

  @override
  Stream<ChatStreamChunk> parseStreamResponse(Stream<String> lines) async* {
    String accumulatedText = '';
    final functionCalls = <String, FunctionCall>{};
    int chunkIndex = 0;

    await for (final line in lines) {
      // Handle SSE format
      if (!line.startsWith('data: ')) continue;

      final data = line.substring(6).trim();
      if (data == '[DONE]') break;

      try {
        final json = jsonDecode(data) as Map<String, dynamic>;
        final response = GeminiResponse.fromJson(json);

        if (response.candidates != null && response.candidates!.isNotEmpty) {
          final candidate = response.candidates!.first;

          // Extract text delta
          String? deltaText;
          if (candidate.content?.parts != null) {
            final currentText = candidate.content!.parts!
                .where((p) => p.text != null)
                .map((p) => p.text!)
                .join('');

            if (currentText.length > accumulatedText.length) {
              deltaText = currentText.substring(accumulatedText.length);
              accumulatedText = currentText;
            }
          }

          // Handle function calls
          List<FunctionCall>? streamFunctionCalls;
          if (candidate.content?.parts != null) {
            for (final part in candidate.content!.parts!) {
              if (part.functionCall != null) {
                final call = part.functionCall!;
                final callId =
                    'gemini_${DateTime.now().millisecondsSinceEpoch}_${call.name}';

                functionCalls[callId] = FunctionCall(
                  id: callId,
                  name: call.name,
                  arguments: call.args != null ? jsonEncode(call.args) : '{}',
                );
              }
            }

            if (functionCalls.isNotEmpty) {
              streamFunctionCalls = functionCalls.values.toList();
            }
          }

          // Extract usage information
          RequestUsage? usage;
          if (response.usageMetadata != null) {
            usage = RequestUsage(
              promptTokens: response.usageMetadata!.promptTokenCount,
              completionTokens: response.usageMetadata!.candidatesTokenCount,
              totalTokens: response.usageMetadata!.totalTokenCount,
            );
          }

          yield ChatStreamChunk(
            text: deltaText,
            functionCalls: streamFunctionCalls,
            finishReason: candidate.finishReason,
            usage: usage,
            isFirst: chunkIndex == 0,
            isLast: candidate.finishReason != null,
            modelInfo: _modelInfo,
          );

          chunkIndex++;
        }
      } catch (e) {
        if (enableLogging) {
          final logger = LLMLoggers.gemini;
          logger.fine('Failed to parse streaming chunk: $e');
          logger.fine('Data: $data');
        }
      }
    }
  }

  @override
  Future<int> countTokens(List<LLMMessage> messages) async {
    // Convert messages to Gemini format
    final contents = _messageTransformer.messagesToGeminiContents(messages);

    final body = {
      'contents': contents.map((c) => c.toJson()).toList(),
    };

    // Build URI and headers
    final uri = buildRequestUri(RequestType.tokenCount, {'model': model});
    final headers = <String, String>{};
    headers.addAll(defaultHeaders);
    headers.addAll(customHeaders);

    // Use base class method for HTTP request with retry logic
    final response = await executeHttpRequest(
      uri: uri,
      requestBody: body,
      headers: headers,
    );

    return response['totalTokens'] as int? ?? 0;
  }

  @override
  Exception createHttpException(
      int statusCode, String body, Map<String, dynamic>? errorData) {
    return LLMExceptionFactory.fromHttpResponse(
      provider: 'gemini',
      statusCode: statusCode,
      responseBody: body,
      errorData: errorData,
    );
  }

  // === Gemini-Specific Methods ===

  /// List available Gemini models
  Future<List<GeminiModel>> listModels() async {
    // Build URI and headers
    final uri = buildRequestUri(RequestType.listModels, {});
    final headers = <String, String>{};
    headers.addAll(defaultHeaders);
    headers.addAll(customHeaders);

    // Use base class method for HTTP request with retry logic
    final response = await executeHttpRequest(
      uri: uri,
      requestBody: {},
      method: 'GET',
      headers: headers,
    );

    final modelsJson = response['models'] as List<dynamic>;

    return modelsJson
        .map((m) => GeminiModel.fromJson(m as Map<String, dynamic>))
        .toList();
  }

  /// Get model information
  Future<GeminiModel> getModelInfo(String modelName) async {
    // Build URI and headers
    final uri = buildRequestUri(RequestType.modelInfo, {'model': modelName});
    final headers = <String, String>{};
    headers.addAll(defaultHeaders);
    headers.addAll(customHeaders);

    // Use base class method for HTTP request with retry logic
    final response = await executeHttpRequest(
      uri: uri,
      requestBody: {},
      method: 'GET',
      headers: headers,
    );

    return GeminiModel.fromJson(response);
  }

  // === EmbeddingClient Implementation ===

  @override
  void validateEmbeddingRequest(
    List<String> inputs,
    String? encodingFormat,
    int? dimensions,
  ) {
    // Gemini supports embeddings through dedicated embedding models
    // We don't need to check the current model's embedding support here
    // since embeddings requests can specify their own model

    if (inputs.isEmpty) {
      throw ArgumentError('Inputs list cannot be empty');
    }

    if (inputs.length > 2048) {
      throw ArgumentError('Too many inputs. Maximum 2048 allowed per request');
    }

    for (final input in inputs) {
      if (input.isEmpty) {
        throw ArgumentError('Input text cannot be empty');
      }
      if (input.length > 60000) {
        throw ArgumentError(
            'Input text too long. Maximum 60,000 characters allowed');
      }
    }

    // Gemini doesn't support custom dimensions
    if (dimensions != null) {
      throw ArgumentError(
          'Gemini does not support custom embedding dimensions');
    }
  }

  @override
  Map<String, dynamic> buildEmbeddingRequestBody({
    required List<String> inputs,
    String? encodingFormat,
    int? dimensions,
    Map<String, dynamic>? additionalParams,
  }) {
    // For single input, use embedContent format
    if (inputs.length == 1) {
      return {
        'content': {
          'parts': [
            {'text': inputs.first}
          ]
        }
      };
    }

    // For multiple inputs, we'll handle it differently in the createEmbeddings override
    // This should not be called for multiple inputs - let's throw an error for clarity
    throw UnsupportedError(
        'Batch embeddings should be handled by overridden createEmbeddings method');
  }

  @override
  EmbeddingResponse parseEmbeddingResponse(Map<String, dynamic> response) {
    List<List<double>> embeddings = [];

    // Handle single embedding response
    if (response.containsKey('embedding')) {
      final embedding = response['embedding'] as Map<String, dynamic>?;
      final values = embedding?['values'] as List<dynamic>?;
      if (values != null) {
        final embeddingList = values.map((v) => (v as num).toDouble()).toList();
        embeddings.add(embeddingList);
      }
    }
    // Handle batch embeddings response
    else if (response.containsKey('embeddings')) {
      final embeddingsList = response['embeddings'] as List<dynamic>?;
      if (embeddingsList != null) {
        for (final e in embeddingsList) {
          final values = e['values'] as List<dynamic>?;
          if (values != null) {
            final embeddingList =
                values.map((v) => (v as num).toDouble()).toList();
            embeddings.add(embeddingList);
          }
        }
      }
    }

    return EmbeddingResponse(
      content: embeddings,
      inputs: [], // Will be set by the calling context
      usage: null, // Gemini doesn't provide usage info for embeddings
    );
  }

  @override
  int get embeddingDimension {
    // Gemini text-embedding-004 has 768 dimensions
    // text-embedding-001 has 768 dimensions
    return 768;
  }

  /// Override createEmbeddings to handle Gemini's different endpoints for single vs batch
  @override
  Future<EmbeddingResponse> createEmbeddings({
    required List<String> inputs,
    String? encodingFormat,
    int? dimensions,
    Map<String, dynamic>? additionalParams,
    CancellationToken? cancellationToken,
  }) async {
    // Step 1: Validation
    validateEmbeddingRequest(inputs, encodingFormat, dimensions);

    // Step 2: Handle single vs batch requests differently
    if (inputs.length == 1) {
      // Use single embedding endpoint
      return _createSingleEmbedding(
        inputs.first,
        additionalParams,
        cancellationToken,
      );
    } else {
      // Use batch embedding endpoint
      return _createBatchEmbeddings(
        inputs,
        additionalParams,
        cancellationToken,
      );
    }
  }

  /// Handle single embedding request
  Future<EmbeddingResponse> _createSingleEmbedding(
    String input,
    Map<String, dynamic>? additionalParams,
    CancellationToken? cancellationToken,
  ) async {
    final requestBody = {
      'content': {
        'parts': [
          {'text': input}
        ]
      }
    };

    // Build URI and execute HTTP request
    final uri = buildRequestUri(RequestType.embedding, additionalParams ?? {});
    final headers = <String, String>{};
    headers.addAll(defaultHeaders);
    headers.addAll(customHeaders);

    final responseData = await executeHttpRequest(
      uri: uri,
      requestBody: requestBody,
      headers: headers,
      cancellationToken: cancellationToken,
    );

    // Parse response
    final response = parseEmbeddingResponse(responseData);

    return EmbeddingResponse(
      content: response.content,
      inputs: [input],
      usage: response.usage,
      modelInfo: response.modelInfo,
      metadata: response.metadata,
      timestamp: response.timestamp,
    );
  }

  /// Handle batch embedding request
  Future<EmbeddingResponse> _createBatchEmbeddings(
    List<String> inputs,
    Map<String, dynamic>? additionalParams,
    CancellationToken? cancellationToken,
  ) async {
    final embedModel = additionalParams?['model'] ?? 'embedding-001';
    final modelName =
        embedModel.startsWith('models/') ? embedModel : 'models/$embedModel';

    final requestBody = {
      'requests': inputs
          .map((text) => {
                'model': modelName,
                'content': {
                  'parts': [
                    {'text': text}
                  ]
                }
              })
          .toList(),
    };

    // Build URI and execute HTTP request
    final uri =
        buildRequestUri(RequestType.batchEmbedding, additionalParams ?? {});
    final headers = <String, String>{};
    headers.addAll(defaultHeaders);
    headers.addAll(customHeaders);

    final responseData = await executeHttpRequest(
      uri: uri,
      requestBody: requestBody,
      headers: headers,
      cancellationToken: cancellationToken,
    );

    // Parse response
    final response = parseEmbeddingResponse(responseData);

    return EmbeddingResponse(
      content: response.content,
      inputs: inputs,
      usage: response.usage,
      modelInfo: response.modelInfo,
      metadata: response.metadata,
      timestamp: response.timestamp,
    );
  }
}
