import '../model_enums.dart';
import '../model_types.dart';

/// Get built-in Gemini models
List<ModelInfo> getGeminiBuiltinModels() {

  return [
    // Gemini 1.5 Flash
    ModelInfo(
      name: 'gemini-1.5-flash',
      family: 'gemini-1.5',
      version: '001',
      description:
          'Fast and versatile model for scaling across a wide range of tasks',
      capabilities: {
        ModelCapability.textGeneration,
        ModelCapability.imageInput,
        ModelCapability.audioInput,
        ModelCapability.videoInput,
        ModelCapability.functionCalling,
        ModelCapability.streaming,
        ModelCapability.systemMessages,
      },
      inputTokens: 0.00001875, // $0.01875 per 1M tokens
      outputTokens: 0.000075, // $0.075 per 1M tokens
      currency: Currency.usd,
      pricingTier: PricingTier.standard,
      maxTokens: 1048576, // 1M tokens
      maxInputTokens: 1048576,
      maxOutputTokens: 8192,
      supportedLanguages: {
        SupportedLanguage.english,
        SupportedLanguage.chineseSimplified,
        SupportedLanguage.japanese,
        SupportedLanguage.korean,
        SupportedLanguage.french,
        SupportedLanguage.german,
        SupportedLanguage.spanish,
        SupportedLanguage.italian,
        SupportedLanguage.portuguese,
        SupportedLanguage.russian,
        SupportedLanguage.arabic,
        SupportedLanguage.hindi,
      },
    ),

    // Gemini 1.5 Pro
    ModelInfo(
      name: 'gemini-1.5-pro',
      family: 'gemini-1.5',
      version: '001',
      description: 'Most capable multimodal model with best performance',
      capabilities: {
        ModelCapability.textGeneration,
        ModelCapability.imageInput,
        ModelCapability.audioInput,
        ModelCapability.videoInput,
        ModelCapability.functionCalling,
        ModelCapability.streaming,
        ModelCapability.systemMessages,
      },
      inputTokens: 0.000125, // $0.125 per 1M tokens
      outputTokens: 0.000375, // $0.375 per 1M tokens
      currency: Currency.usd,
      pricingTier: PricingTier.premium,
      maxTokens: 2097152, // 2M tokens
      maxInputTokens: 2097152,
      maxOutputTokens: 8192,
      supportedLanguages: {
        SupportedLanguage.english,
        SupportedLanguage.chineseSimplified,
        SupportedLanguage.japanese,
        SupportedLanguage.korean,
        SupportedLanguage.french,
        SupportedLanguage.german,
        SupportedLanguage.spanish,
        SupportedLanguage.italian,
        SupportedLanguage.portuguese,
        SupportedLanguage.russian,
        SupportedLanguage.arabic,
        SupportedLanguage.hindi,
      },
    ),

    // Gemini 1.0 Pro
    ModelInfo(
      name: 'gemini-1.0-pro',
      family: 'gemini-1.0',
      description: 'Previous generation model for text and function calling',
      capabilities: {
        ModelCapability.textGeneration,
        ModelCapability.functionCalling,
        ModelCapability.streaming,
        ModelCapability.systemMessages,
      },
      inputTokens: 0.0005, // $0.50 per 1M tokens
      outputTokens: 0.0015, // $1.50 per 1M tokens
      currency: Currency.usd,
      pricingTier: PricingTier.standard,
      maxTokens: 32768,
      maxInputTokens: 30720,
      maxOutputTokens: 2048,
      supportedLanguages: {
        SupportedLanguage.english,
        SupportedLanguage.chineseSimplified,
        SupportedLanguage.japanese,
        SupportedLanguage.korean,
        SupportedLanguage.french,
        SupportedLanguage.german,
        SupportedLanguage.spanish,
        SupportedLanguage.italian,
        SupportedLanguage.portuguese,
        SupportedLanguage.russian,
      },
    ),

    // Gemini 1.0 Pro Vision
    ModelInfo(
      name: 'gemini-1.0-pro-vision',
      family: 'gemini-1.0',
      description: 'Previous generation model with vision capabilities',
      capabilities: {
        ModelCapability.textGeneration,
        ModelCapability.imageInput,
        ModelCapability.streaming,
        ModelCapability.systemMessages,
      },
      inputTokens: 0.00025, // $0.25 per 1M tokens
      outputTokens: 0.0005, // $0.50 per 1M tokens
      currency: Currency.usd,
      pricingTier: PricingTier.standard,
      maxTokens: 16384,
      maxInputTokens: 12288,
      maxOutputTokens: 4096,
      supportedLanguages: {
        SupportedLanguage.english,
        SupportedLanguage.chineseSimplified,
        SupportedLanguage.japanese,
        SupportedLanguage.korean,
        SupportedLanguage.french,
        SupportedLanguage.german,
        SupportedLanguage.spanish,
        SupportedLanguage.italian,
        SupportedLanguage.portuguese,
        SupportedLanguage.russian,
      },
    ),

    // Embedding models
    ModelInfo(
      name: 'text-embedding-004',
      family: 'embedding',
      description: 'Latest text embedding model',
      capabilities: {
        ModelCapability.embeddings,
      },
      inputTokens: 0.00001, // $0.01 per 1M tokens
      currency: Currency.usd,
      pricingTier: PricingTier.standard,
      maxTokens: 2048,
      supportedLanguages: {
        SupportedLanguage.english,
        SupportedLanguage.chineseSimplified,
        SupportedLanguage.japanese,
        SupportedLanguage.korean,
        SupportedLanguage.french,
        SupportedLanguage.german,
        SupportedLanguage.spanish,
        SupportedLanguage.italian,
        SupportedLanguage.portuguese,
        SupportedLanguage.russian,
        SupportedLanguage.arabic,
        SupportedLanguage.hindi,
      },
    ),

    ModelInfo(
      name: 'embedding-001',
      family: 'embedding',
      description: 'Legacy embedding model',
      capabilities: {
        ModelCapability.embeddings,
      },
      inputTokens: 0.00001, // $0.01 per 1M tokens
      currency: Currency.usd,
      pricingTier: PricingTier.developer,
      maxTokens: 2048,
      supportedLanguages: {
        SupportedLanguage.english,
        SupportedLanguage.chineseSimplified,
        SupportedLanguage.japanese,
        SupportedLanguage.korean,
        SupportedLanguage.french,
        SupportedLanguage.german,
        SupportedLanguage.spanish,
      },
    ),
  ];
}

/// Gemini 模型家族工具类
class GeminiModelFamily {
  static const String gemini15 = 'gemini-1.5';
  static const String gemini10 = 'gemini-1.0';
  static const String embedding = 'embedding';

  static const List<String> all = [
    gemini15,
    gemini10,
    embedding,
  ];

  /// 检查家族是否支持视觉能力
  static bool supportsVision(String family) {
    return [gemini15].contains(family);
  }

  /// 检查家族是否支持函数调用
  static bool supportsFunctionCalling(String family) {
    return [gemini15, gemini10].contains(family);
  }

  /// 检查家族是否支持多模态输入
  static bool supportsMultimodal(String family) {
    return [gemini15].contains(family);
  }

  /// 检查是否支持大上下文窗口
  static bool hasLargeContext(String family) {
    return [gemini15].contains(family);
  }

  /// 获取家族中的最新模型名称
  static String? getLatestModelName(String family) {
    switch (family) {
      case gemini15:
        return 'gemini-1.5-pro';
      case gemini10:
        return 'gemini-1.0-pro';
      case embedding:
        return 'text-embedding-004';
      default:
        return null;
    }
  }

  /// 根据能力需求推荐模型家族
  static List<String> recommendFamilies({
    bool needsVision = false,
    bool needsMultimodal = false,
    bool needsFunctionCalling = false,
    bool needsLargeContext = false,
    bool needsEmbeddings = false,
    bool costOptimized = false,
  }) {
    final recommended = <String>[];

    if (needsEmbeddings) {
      recommended.add(embedding);
    }

    if (needsVision || needsMultimodal || needsLargeContext) {
      recommended.add(gemini15);
    } else if (needsFunctionCalling) {
      if (costOptimized) {
        recommended.addAll([gemini10, gemini15]);
      } else {
        recommended.addAll([gemini15, gemini10]);
      }
    } else {
      if (costOptimized) {
        recommended.addAll([gemini10, gemini15]);
      } else {
        recommended.addAll([gemini15, gemini10]);
      }
    }

    return recommended.toSet().toList();
  }
}
