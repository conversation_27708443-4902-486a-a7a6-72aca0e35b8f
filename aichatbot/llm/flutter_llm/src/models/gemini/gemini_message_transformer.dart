import 'dart:convert';
import 'dart:typed_data';

import 'package:mime/mime.dart';

import '../../model_context/chat_completion_context.dart';
import '../llm_response.dart';
import '../model_types.dart';
import 'gemini_types.dart';

/// Transforms messages between AutoGen and Gemini formats
class GeminiMessageTransformer {
  /// Validate messages for Gemini compatibility
  void validateMessages(List<LLMMessage> messages) {
    for (final message in messages) {
      if (message is UserMessage) {
        // Check for empty content
        if ((message.text == null || message.text!.isEmpty) &&
            message.images.isEmpty) {
          throw ArgumentError('User message must have text or images');
        }
      } else if (message is AssistantMessage) {
        // Check for empty content
        if ((message.text == null || message.text!.isEmpty) &&
            message.functionCalls.isEmpty) {
          throw ArgumentError(
              'Assistant message must have text or function calls');
        }
      } else if (message is SystemMessage) {
        // System messages are handled separately in Gemini
        continue;
      } else if (message is FunctionExecutionResultMessage) {
        // Function results need special handling
        if (message.content.isEmpty) {
          throw ArgumentError('Function result message must have content');
        }
      } else {
        throw ArgumentError('Unsupported message type: ${message.runtimeType}');
      }
    }
  }

  /// Convert AutoGen messages to Gemini Content format
  List<GeminiContent> messagesToGeminiContents(List<LLMMessage> messages) {
    final contents = <GeminiContent>[];

    for (final message in messages) {
      if (message is UserMessage) {
        contents.add(_userMessageToContent(message));
      } else if (message is AssistantMessage) {
        contents.add(_assistantMessageToContent(message));
      } else if (message is FunctionExecutionResultMessage) {
        contents.add(_functionResultToContent(message));
      }
      // Skip SystemMessage as it's handled separately
    }

    return contents;
  }

  /// Convert UserMessage to Gemini Content
  GeminiContent _userMessageToContent(UserMessage message) {
    final parts = <GeminiPart>[];

    // Add text part
    if (message.text != null && message.text!.isNotEmpty) {
      parts.add(GeminiPart(text: message.text!));
    }

    // Add image parts
    for (final image in message.images) {
      // Convert image data to base64 data URI
      final mimeType = image.format.mimeType;
      final base64Data = base64Encode(image.data);

      parts.add(GeminiPart(
        inlineData: GeminiInlineData(
          mimeType: mimeType,
          data: base64Data,
        ),
      ));
    }

    return GeminiContent(
      parts: parts,
      role: 'user',
    );
  }

  /// Convert AssistantMessage to Gemini Content
  GeminiContent _assistantMessageToContent(AssistantMessage message) {
    final parts = <GeminiPart>[];

    // Add text part
    if (message.text != null && message.text!.isNotEmpty) {
      parts.add(GeminiPart(text: message.text!));
    }

    // Handle function calls
    if (message.functionCalls.isNotEmpty) {
      // Gemini uses a different format for function calls
      for (final call in message.functionCalls) {
        Map<String, dynamic>? args;
        try {
          args = jsonDecode(call.arguments) as Map<String, dynamic>;
        } catch (_) {
          // If arguments aren't valid JSON, use as-is
          args = {'raw': call.arguments};
        }

        parts.add(GeminiPart(
          functionCall: GeminiFunctionCall(
            name: call.name,
            args: args,
          ),
        ));
      }
    }

    return GeminiContent(
      parts: parts,
      role: 'model',
    );
  }

  /// Convert FunctionExecutionResultMessage to Gemini Content
  GeminiContent _functionResultToContent(
      FunctionExecutionResultMessage message) {
    // Handle multiple function results
    final parts = <GeminiPart>[];

    for (final result in message.content) {
      final response = <String, dynamic>{};

      try {
        // Try to parse content as JSON
        response['result'] = jsonDecode(result.content);
      } catch (_) {
        // If not JSON, use as string
        response['result'] = result.content;
      }

      // Extract function name from callId (format: "functionName_id")
      // Find the last underscore to handle function names with underscores
      final lastUnderscore = result.callId.lastIndexOf('_');
      final functionName = lastUnderscore != -1
          ? result.callId.substring(0, lastUnderscore)
          : result.callId;

      parts.add(GeminiPart(
        functionResponse: GeminiFunctionResponse(
          name: functionName,
          response: response,
        ),
      ));
    }

    return GeminiContent(
      parts: parts,
      role: 'function',
    );
  }

  /// Convert Gemini Candidate to AssistantMessage
  AssistantMessage candidateToAssistantMessage(GeminiCandidate candidate) {
    String? text;
    final functionCalls = <FunctionCall>[];

    if (candidate.content?.parts != null) {
      final textParts = <String>[];

      for (final part in candidate.content!.parts!) {
        if (part.text != null) {
          textParts.add(part.text!);
        } else if (part.functionCall != null) {
          // Convert Gemini function call to AutoGen format
          final call = part.functionCall!;
          functionCalls.add(FunctionCall(
            id: 'gemini_${DateTime.now().millisecondsSinceEpoch}_${call.name}',
            name: call.name,
            arguments: call.args != null ? jsonEncode(call.args) : '{}',
          ));
        }
      }

      if (textParts.isNotEmpty) {
        text = textParts.join('\n');
      }
    }

    return AssistantMessage(
      text: text,
      functionCalls: functionCalls,
    );
  }

  /// Convert Gemini Candidate to ChatStreamChunk
  ChatStreamChunk candidateToStreamChunk(
    GeminiCandidate candidate,
    GeminiUsageMetadata? usageMetadata,
  ) {
    String? text;
    final functionCalls = <FunctionCall>[];

    if (candidate.content?.parts != null) {
      final textParts = <String>[];

      for (final part in candidate.content!.parts!) {
        if (part.text != null) {
          textParts.add(part.text!);
        } else if (part.functionCall != null) {
          // Convert Gemini function call to AutoGen format
          final call = part.functionCall!;
          functionCalls.add(FunctionCall(
            id: 'gemini_${DateTime.now().millisecondsSinceEpoch}_${call.name}',
            name: call.name,
            arguments: call.args != null ? jsonEncode(call.args) : '{}',
          ));
        }
      }

      if (textParts.isNotEmpty) {
        text = textParts.join();
      }
    }

    RequestUsage? usage;
    if (usageMetadata != null) {
      usage = RequestUsage(
        promptTokens: usageMetadata.promptTokenCount,
        completionTokens: usageMetadata.candidatesTokenCount,
        totalTokens: usageMetadata.totalTokenCount,
      );
    }

    return ChatStreamChunk(
      text: text,
      functionCalls: functionCalls.isNotEmpty ? functionCalls : null,
      finishReason: candidate.finishReason,
      usage: usage,
      isLast: candidate.finishReason != null,
    );
  }

  /// Convert base64 string to Uint8List
  Uint8List base64ToUint8List(String base64String) {
    // Remove data URI prefix if present
    final base64 = base64String.split(',').last;
    return base64Decode(base64);
  }

  /// Convert Uint8List to base64 data URI
  String uint8ListToDataUri(Uint8List bytes, {String? mimeType}) {
    final mime = mimeType ??
        lookupMimeType('', headerBytes: bytes) ??
        'application/octet-stream';
    final base64 = base64Encode(bytes);
    return 'data:$mime;base64,$base64';
  }
}
