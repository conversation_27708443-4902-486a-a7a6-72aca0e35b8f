/// Gemini API types and models

/// Gemini Content representation
class GeminiContent {
  final List<GeminiPart>? parts;
  final String? role;

  GeminiContent({this.parts, this.role});

  factory GeminiContent.fromJson(Map<String, dynamic> json) {
    return GeminiContent(
      parts: (json['parts'] as List<dynamic>?)
          ?.map((p) => GeminiPart.fromJson(p as Map<String, dynamic>))
          .toList(),
      role: json['role'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (parts != null) 'parts': parts!.map((p) => p.toJson()).toList(),
      if (role != null) 'role': role,
    };
  }
}

/// Gemini Part - can be text, inline data, or file data
class GeminiPart {
  final String? text;
  final GeminiInlineData? inlineData;
  final GeminiFileData? fileData;
  final GeminiFunctionCall? functionCall;
  final GeminiFunctionResponse? functionResponse;

  GeminiPart({
    this.text,
    this.inlineData,
    this.fileData,
    this.functionCall,
    this.functionResponse,
  });

  factory GeminiPart.fromJson(Map<String, dynamic> json) {
    return GeminiPart(
      text: json['text'] as String?,
      inlineData: json['inlineData'] != null
          ? GeminiInlineData.fromJson(
              json['inlineData'] as Map<String, dynamic>)
          : null,
      fileData: json['fileData'] != null
          ? GeminiFileData.fromJson(json['fileData'] as Map<String, dynamic>)
          : null,
      functionCall: json['functionCall'] != null
          ? GeminiFunctionCall.fromJson(
              json['functionCall'] as Map<String, dynamic>)
          : null,
      functionResponse: json['functionResponse'] != null
          ? GeminiFunctionResponse.fromJson(
              json['functionResponse'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (text != null) 'text': text,
      if (inlineData != null) 'inlineData': inlineData!.toJson(),
      if (fileData != null) 'fileData': fileData!.toJson(),
      if (functionCall != null) 'functionCall': functionCall!.toJson(),
      if (functionResponse != null)
        'functionResponse': functionResponse!.toJson(),
    };
  }
}

/// Inline data for images and other binary content
class GeminiInlineData {
  final String mimeType;
  final String data;

  GeminiInlineData({required this.mimeType, required this.data});

  factory GeminiInlineData.fromJson(Map<String, dynamic> json) {
    return GeminiInlineData(
      mimeType: json['mimeType'] as String,
      data: json['data'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'mimeType': mimeType,
      'data': data,
    };
  }
}

/// File data reference
class GeminiFileData {
  final String? mimeType;
  final String fileUri;

  GeminiFileData({this.mimeType, required this.fileUri});

  factory GeminiFileData.fromJson(Map<String, dynamic> json) {
    return GeminiFileData(
      mimeType: json['mimeType'] as String?,
      fileUri: json['fileUri'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (mimeType != null) 'mimeType': mimeType,
      'fileUri': fileUri,
    };
  }
}

/// Function call in Gemini format
class GeminiFunctionCall {
  final String name;
  final Map<String, dynamic>? args;

  GeminiFunctionCall({required this.name, this.args});

  factory GeminiFunctionCall.fromJson(Map<String, dynamic> json) {
    return GeminiFunctionCall(
      name: json['name'] as String,
      args: json['args'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      if (args != null) 'args': args,
    };
  }
}

/// Function response in Gemini format
class GeminiFunctionResponse {
  final String name;
  final Map<String, dynamic> response;

  GeminiFunctionResponse({required this.name, required this.response});

  factory GeminiFunctionResponse.fromJson(Map<String, dynamic> json) {
    return GeminiFunctionResponse(
      name: json['name'] as String,
      response: json['response'] as Map<String, dynamic>,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'response': response,
    };
  }
}

/// Gemini API response
class GeminiResponse {
  final List<GeminiCandidate>? candidates;
  final GeminiUsageMetadata? usageMetadata;
  final GeminiPromptFeedback? promptFeedback;

  GeminiResponse({
    this.candidates,
    this.usageMetadata,
    this.promptFeedback,
  });

  factory GeminiResponse.fromJson(Map<String, dynamic> json) {
    return GeminiResponse(
      candidates: (json['candidates'] as List<dynamic>?)
          ?.map((c) => GeminiCandidate.fromJson(c as Map<String, dynamic>))
          .toList(),
      usageMetadata: json['usageMetadata'] != null
          ? GeminiUsageMetadata.fromJson(
              json['usageMetadata'] as Map<String, dynamic>)
          : null,
      promptFeedback: json['promptFeedback'] != null
          ? GeminiPromptFeedback.fromJson(
              json['promptFeedback'] as Map<String, dynamic>)
          : null,
    );
  }
}

/// Gemini candidate response
class GeminiCandidate {
  final GeminiContent? content;
  final String? finishReason;
  final int? index;
  final List<GeminiSafetyRating>? safetyRatings;

  GeminiCandidate({
    this.content,
    this.finishReason,
    this.index,
    this.safetyRatings,
  });

  factory GeminiCandidate.fromJson(Map<String, dynamic> json) {
    return GeminiCandidate(
      content: json['content'] != null
          ? GeminiContent.fromJson(json['content'] as Map<String, dynamic>)
          : null,
      finishReason: json['finishReason'] as String?,
      index: json['index'] as int?,
      safetyRatings: (json['safetyRatings'] as List<dynamic>?)
          ?.map((r) => GeminiSafetyRating.fromJson(r as Map<String, dynamic>))
          .toList(),
    );
  }
}

/// Usage metadata
class GeminiUsageMetadata {
  final int? promptTokenCount;
  final int? candidatesTokenCount;
  final int? totalTokenCount;
  final int? cachedContentTokenCount;

  GeminiUsageMetadata({
    this.promptTokenCount,
    this.candidatesTokenCount,
    this.totalTokenCount,
    this.cachedContentTokenCount,
  });

  factory GeminiUsageMetadata.fromJson(Map<String, dynamic> json) {
    return GeminiUsageMetadata(
      promptTokenCount: json['promptTokenCount'] as int?,
      candidatesTokenCount: json['candidatesTokenCount'] as int?,
      totalTokenCount: json['totalTokenCount'] as int?,
      cachedContentTokenCount: json['cachedContentTokenCount'] as int?,
    );
  }
}

/// Prompt feedback
class GeminiPromptFeedback {
  final String? blockReason;
  final List<GeminiSafetyRating>? safetyRatings;

  GeminiPromptFeedback({
    this.blockReason,
    this.safetyRatings,
  });

  factory GeminiPromptFeedback.fromJson(Map<String, dynamic> json) {
    return GeminiPromptFeedback(
      blockReason: json['blockReason'] as String?,
      safetyRatings: (json['safetyRatings'] as List<dynamic>?)
          ?.map((r) => GeminiSafetyRating.fromJson(r as Map<String, dynamic>))
          .toList(),
    );
  }
}

/// Safety rating
class GeminiSafetyRating {
  final String category;
  final String probability;
  final bool? blocked;

  GeminiSafetyRating({
    required this.category,
    required this.probability,
    this.blocked,
  });

  factory GeminiSafetyRating.fromJson(Map<String, dynamic> json) {
    return GeminiSafetyRating(
      category: json['category'] as String,
      probability: json['probability'] as String,
      blocked: json['blocked'] as bool?,
    );
  }
}

/// Safety setting
class GeminiSafetySetting {
  final GeminiSafetyCategory category;
  final GeminiSafetyThreshold threshold;

  GeminiSafetySetting({
    required this.category,
    required this.threshold,
  });

  Map<String, dynamic> toJson() {
    return {
      'category': category.value,
      'threshold': threshold.value,
    };
  }
}

/// Safety categories
enum GeminiSafetyCategory {
  harassment('HARM_CATEGORY_HARASSMENT'),
  hateSpeech('HARM_CATEGORY_HATE_SPEECH'),
  sexuallyExplicit('HARM_CATEGORY_SEXUALLY_EXPLICIT'),
  dangerousContent('HARM_CATEGORY_DANGEROUS_CONTENT');

  final String value;
  const GeminiSafetyCategory(this.value);
}

/// Safety thresholds
enum GeminiSafetyThreshold {
  blockNone('BLOCK_NONE'),
  blockOnlyHigh('BLOCK_ONLY_HIGH'),
  blockMediumAndAbove('BLOCK_MEDIUM_AND_ABOVE'),
  blockLowAndAbove('BLOCK_LOW_AND_ABOVE'),
  unspecified('HARM_BLOCK_THRESHOLD_UNSPECIFIED');

  final String value;
  const GeminiSafetyThreshold(this.value);
}

/// Generation configuration
class GeminiGenerationConfig {
  final List<String>? stopSequences;
  final double? temperature;
  final int? maxOutputTokens;
  final double? topP;
  final int? topK;
  final int? candidateCount;
  final String? responseMimeType;

  GeminiGenerationConfig({
    this.stopSequences,
    this.temperature,
    this.maxOutputTokens,
    this.topP,
    this.topK,
    this.candidateCount,
    this.responseMimeType,
  });

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (stopSequences != null) json['stopSequences'] = stopSequences;
    if (temperature != null) json['temperature'] = temperature;
    if (maxOutputTokens != null) json['maxOutputTokens'] = maxOutputTokens;
    if (topP != null) json['topP'] = topP;
    if (topK != null) json['topK'] = topK;
    if (candidateCount != null) json['candidateCount'] = candidateCount;
    if (responseMimeType != null) json['responseMimeType'] = responseMimeType;
    return json;
  }
}

/// Gemini model information
class GeminiModel {
  final String? name;
  final String? version;
  final String? displayName;
  final String? description;
  final int? inputTokenLimit;
  final int? outputTokenLimit;
  final List<String>? supportedGenerationMethods;
  final double? temperature;
  final double? topP;
  final int? topK;

  GeminiModel({
    this.name,
    this.version,
    this.displayName,
    this.description,
    this.inputTokenLimit,
    this.outputTokenLimit,
    this.supportedGenerationMethods,
    this.temperature,
    this.topP,
    this.topK,
  });

  factory GeminiModel.fromJson(Map<String, dynamic> json) {
    return GeminiModel(
      name: json['name'] as String?,
      version: json['version'] as String?,
      displayName: json['displayName'] as String?,
      description: json['description'] as String?,
      inputTokenLimit: json['inputTokenLimit'] as int?,
      outputTokenLimit: json['outputTokenLimit'] as int?,
      supportedGenerationMethods:
          (json['supportedGenerationMethods'] as List<dynamic>?)
              ?.map((m) => m as String)
              .toList(),
      temperature: (json['temperature'] as num?)?.toDouble(),
      topP: (json['topP'] as num?)?.toDouble(),
      topK: json['topK'] as int?,
    );
  }
}
