import 'package:http/http.dart' as http;

import 'anthropic/anthropic_client.dart';
import 'base_llm_client.dart';
import 'gemini/gemini_client.dart';
import 'model_types.dart';
import 'openai/openai_client.dart';

/// Factory for creating LLM clients using the template method pattern
///
/// This factory provides convenient methods to create OpenAI, Anthropic,
/// Gemini, and OpenAI-compatible clients with various configurations while
/// maintaining a consistent API. All clients extend BaseLLMClient and share
/// common functionality.
///
/// ## Usage
///
/// ```dart
/// // Create OpenAI client
/// final openaiClient = ClientFactory.createOpenAI(
///   model: 'gpt-4',
///   apiKey: 'sk-...',
/// );
///
/// // Create Anthropic client
/// final anthropicClient = ClientFactory.createAnthropic(
///   model: 'claude-3-opus-20240229',
///   apiKey: 'sk-ant-...',
/// );
///
/// // Create Gemini client
/// final geminiClient = ClientFactory.createGemini(
///   model: 'gemini-1.5-flash',
///   apiKey: 'your-api-key',
/// );
///
/// // Create from environment variables
/// final client = ClientFactory.fromEnvironment(
///   provider: 'openai',
///   model: 'gpt-4',
/// );
///
/// // Create with auto-detection
/// final client = ClientFactory.create(
///   model: 'gpt-4', // Auto-detects OpenAI
///   apiKey: 'sk-...',
/// );
/// ```
class ClientFactory {
  /// Create an OpenAI client
  ///
  /// Parameters:
  /// - [model]: OpenAI model identifier (e.g., 'gpt-4', 'gpt-3.5-turbo')
  /// - [apiKey]: OpenAI API key
  /// - [baseUrl]: Custom base URL for OpenAI API
  /// - [timeout]: Request timeout duration
  /// - [maxRetries]: Maximum retry attempts on failure
  /// - [retryDelay]: Delay between retry attempts
  /// - [customHeaders]: Additional headers to include
  /// - [enableLogging]: Enable request/response logging
  /// - [modelInfo]: Custom model info (auto-detected if not provided)
  /// - [httpClient]: Custom HTTP client instance
  static OpenAIClient createOpenAI({
    required String model,
    required String apiKey,
    String? baseUrl,
    Duration timeout = const Duration(seconds: 60),
    int maxRetries = 3,
    Duration retryDelay = const Duration(seconds: 1),
    Map<String, String> customHeaders = const {},
    bool enableLogging = false,
    ModelInfo? modelInfo,
    http.Client? httpClient,
  }) {
    return OpenAIClient(
      model: model,
      apiKey: apiKey,
      baseUrl: baseUrl,
      timeout: timeout,
      maxRetries: maxRetries,
      retryDelay: retryDelay,
      customHeaders: customHeaders,
      enableLogging: enableLogging,
      modelInfo: modelInfo,
      httpClient: httpClient,
    );
  }

  /// Create an OpenAI client from environment variables
  ///
  /// Reads API key from OPENAI_API_KEY environment variable.
  static OpenAIClient createOpenAIFromEnvironment({
    required String model,
    String? baseUrl,
    Duration timeout = const Duration(seconds: 60),
    int maxRetries = 3,
    Duration retryDelay = const Duration(seconds: 1),
    Map<String, String> customHeaders = const {},
    bool enableLogging = false,
    ModelInfo? modelInfo,
    http.Client? httpClient,
  }) {
    return OpenAIClient.fromEnvironment(
      model: model,
      baseUrl: baseUrl,
      timeout: timeout,
      maxRetries: maxRetries,
      retryDelay: retryDelay,
      customHeaders: customHeaders,
      enableLogging: enableLogging,
      modelInfo: modelInfo,
      httpClient: httpClient,
    );
  }

  /// Create an OpenAI-compatible client
  ///
  /// For local models or other OpenAI-compatible APIs.
  static OpenAIClient createOpenAICompatible({
    required String model,
    required String baseUrl,
    String? apiKey,
    Duration timeout = const Duration(seconds: 60),
    int maxRetries = 3,
    Duration retryDelay = const Duration(seconds: 1),
    Map<String, String> customHeaders = const {},
    bool enableLogging = false,
    ModelInfo? modelInfo,
    http.Client? httpClient,
  }) {
    return OpenAIClient.compatible(
      model: model,
      baseUrl: baseUrl,
      apiKey: apiKey,
      timeout: timeout,
      maxRetries: maxRetries,
      retryDelay: retryDelay,
      customHeaders: customHeaders,
      enableLogging: enableLogging,
      modelInfo: modelInfo,
      httpClient: httpClient,
    );
  }

  /// Create an Anthropic client
  ///
  /// Parameters:
  /// - [model]: Anthropic model identifier (e.g., 'claude-3-opus-20240229')
  /// - [apiKey]: Anthropic API key
  /// - [baseUrl]: Custom base URL for Anthropic API
  /// - [timeout]: Request timeout duration
  /// - [maxRetries]: Maximum retry attempts on failure
  /// - [retryDelay]: Delay between retry attempts
  /// - [customHeaders]: Additional headers to include
  /// - [enableLogging]: Enable request/response logging
  /// - [modelInfo]: Custom model info (auto-detected if not provided)
  /// - [httpClient]: Custom HTTP client instance
  static AnthropicClient createAnthropic({
    required String model,
    required String apiKey,
    String? baseUrl,
    Duration timeout = const Duration(seconds: 60),
    int maxRetries = 3,
    Duration retryDelay = const Duration(seconds: 1),
    Map<String, String> customHeaders = const {},
    bool enableLogging = false,
    ModelInfo? modelInfo,
    http.Client? httpClient,
  }) {
    return AnthropicClient(
      model: model,
      apiKey: apiKey,
      baseUrl: baseUrl,
      timeout: timeout,
      maxRetries: maxRetries,
      retryDelay: retryDelay,
      customHeaders: customHeaders,
      enableLogging: enableLogging,
      modelInfo: modelInfo,
      httpClient: httpClient,
    );
  }

  /// Create an Anthropic client from environment variables
  ///
  /// Reads API key from ANTHROPIC_API_KEY environment variable.
  static AnthropicClient createAnthropicFromEnvironment({
    required String model,
    String? baseUrl,
    Duration timeout = const Duration(seconds: 60),
    int maxRetries = 3,
    Duration retryDelay = const Duration(seconds: 1),
    Map<String, String> customHeaders = const {},
    bool enableLogging = false,
    ModelInfo? modelInfo,
    http.Client? httpClient,
  }) {
    return AnthropicClient.fromEnvironment(
      model: model,
      baseUrl: baseUrl,
      timeout: timeout,
      maxRetries: maxRetries,
      retryDelay: retryDelay,
      customHeaders: customHeaders,
      enableLogging: enableLogging,
      modelInfo: modelInfo,
      httpClient: httpClient,
    );
  }

  /// Create a Gemini client
  ///
  /// Parameters:
  /// - [model]: Gemini model identifier (e.g., 'gemini-1.5-flash', 'gemini-pro')
  /// - [apiKey]: Gemini API key
  /// - [baseUrl]: Custom base URL for Gemini API
  /// - [apiVersion]: API version (defaults to 'v1beta')
  /// - [timeout]: Request timeout duration
  /// - [maxRetries]: Maximum retry attempts on failure
  /// - [retryDelay]: Delay between retry attempts
  /// - [customHeaders]: Additional headers to include
  /// - [enableLogging]: Enable request/response logging
  /// - [modelInfo]: Custom model info (auto-detected if not provided)
  /// - [httpClient]: Custom HTTP client instance
  static GeminiClient createGemini({
    required String model,
    required String apiKey,
    String? baseUrl,
    String apiVersion = 'v1beta',
    Duration timeout = const Duration(seconds: 60),
    int maxRetries = 3,
    Duration retryDelay = const Duration(seconds: 1),
    Map<String, String> customHeaders = const {},
    bool enableLogging = false,
    ModelInfo? modelInfo,
    http.Client? httpClient,
  }) {
    return GeminiClient(
      model: model,
      apiKey: apiKey,
      baseUrl: baseUrl,
      apiVersion: apiVersion,
      timeout: timeout,
      maxRetries: maxRetries,
      retryDelay: retryDelay,
      customHeaders: customHeaders,
      enableLogging: enableLogging,
      modelInfo: modelInfo,
      httpClient: httpClient,
    );
  }

  /// Create a Gemini client from environment variables
  ///
  /// Reads API key from GEMINI_API_KEY environment variable.
  static GeminiClient createGeminiFromEnvironment({
    required String model,
    String? baseUrl,
    String apiVersion = 'v1beta',
    Duration timeout = const Duration(seconds: 60),
    int maxRetries = 3,
    Duration retryDelay = const Duration(seconds: 1),
    Map<String, String> customHeaders = const {},
    bool enableLogging = false,
    ModelInfo? modelInfo,
    http.Client? httpClient,
  }) {
    const apiKey = String.fromEnvironment('GEMINI_API_KEY');
    if (apiKey.isEmpty) {
      throw ArgumentError(
          'GEMINI_API_KEY environment variable is not set or empty');
    }

    return GeminiClient(
      model: model,
      apiKey: apiKey,
      baseUrl: baseUrl,
      apiVersion: apiVersion,
      timeout: timeout,
      maxRetries: maxRetries,
      retryDelay: retryDelay,
      customHeaders: customHeaders,
      enableLogging: enableLogging,
      modelInfo: modelInfo,
      httpClient: httpClient,
    );
  }

  /// Create a client with automatic provider detection
  ///
  /// Automatically detects the provider based on the model name and creates
  /// the appropriate client. Supports both explicit API keys and environment
  /// variable detection.
  ///
  /// Parameters:
  /// - [model]: Model identifier (provider will be auto-detected)
  /// - [apiKey]: API key (optional if using environment variables)
  /// - [baseUrl]: Custom base URL (optional)
  /// - [timeout]: Request timeout duration
  /// - [maxRetries]: Maximum retry attempts on failure
  /// - [retryDelay]: Delay between retry attempts
  /// - [customHeaders]: Additional headers to include
  /// - [enableLogging]: Enable request/response logging
  /// - [modelInfo]: Custom model info (auto-detected if not provided)
  /// - [httpClient]: Custom HTTP client instance
  static BaseLLMClient create({
    required String model,
    String? apiKey,
    String? baseUrl,
    Duration timeout = const Duration(seconds: 60),
    int maxRetries = 3,
    Duration retryDelay = const Duration(seconds: 1),
    Map<String, String> customHeaders = const {},
    bool enableLogging = false,
    ModelInfo? modelInfo,
    http.Client? httpClient,
  }) {
    final provider = _detectProvider(model);

    switch (provider) {
      case 'openai':
        if (apiKey != null) {
          return createOpenAI(
            model: model,
            apiKey: apiKey,
            baseUrl: baseUrl,
            timeout: timeout,
            maxRetries: maxRetries,
            retryDelay: retryDelay,
            customHeaders: customHeaders,
            enableLogging: enableLogging,
            modelInfo: modelInfo,
            httpClient: httpClient,
          );
        } else {
          return createOpenAIFromEnvironment(
            model: model,
            baseUrl: baseUrl,
            timeout: timeout,
            maxRetries: maxRetries,
            retryDelay: retryDelay,
            customHeaders: customHeaders,
            enableLogging: enableLogging,
            modelInfo: modelInfo,
            httpClient: httpClient,
          );
        }

      case 'anthropic':
        if (apiKey != null) {
          return createAnthropic(
            model: model,
            apiKey: apiKey,
            baseUrl: baseUrl,
            timeout: timeout,
            maxRetries: maxRetries,
            retryDelay: retryDelay,
            customHeaders: customHeaders,
            enableLogging: enableLogging,
            modelInfo: modelInfo,
            httpClient: httpClient,
          );
        } else {
          return createAnthropicFromEnvironment(
            model: model,
            baseUrl: baseUrl,
            timeout: timeout,
            maxRetries: maxRetries,
            retryDelay: retryDelay,
            customHeaders: customHeaders,
            enableLogging: enableLogging,
            modelInfo: modelInfo,
            httpClient: httpClient,
          );
        }

      case 'gemini':
        if (apiKey != null) {
          return createGemini(
            model: model,
            apiKey: apiKey,
            baseUrl: baseUrl,
            timeout: timeout,
            maxRetries: maxRetries,
            retryDelay: retryDelay,
            customHeaders: customHeaders,
            enableLogging: enableLogging,
            modelInfo: modelInfo,
            httpClient: httpClient,
          );
        } else {
          return createGeminiFromEnvironment(
            model: model,
            baseUrl: baseUrl,
            timeout: timeout,
            maxRetries: maxRetries,
            retryDelay: retryDelay,
            customHeaders: customHeaders,
            enableLogging: enableLogging,
            modelInfo: modelInfo,
            httpClient: httpClient,
          );
        }

      default:
        throw ArgumentError('Unknown provider for model: $model. '
            'Supported providers: openai, anthropic, gemini. '
            'Please specify the provider explicitly or use a recognized model name.');
    }
  }

  /// Create a client from environment variables with provider detection
  ///
  /// Automatically detects the provider and reads API keys from environment
  /// variables (OPENAI_API_KEY or ANTHROPIC_API_KEY).
  static BaseLLMClient fromEnvironment({
    String? provider,
    required String model,
    String? baseUrl,
    Duration timeout = const Duration(seconds: 60),
    int maxRetries = 3,
    Duration retryDelay = const Duration(seconds: 1),
    Map<String, String> customHeaders = const {},
    bool enableLogging = false,
    ModelInfo? modelInfo,
    http.Client? httpClient,
  }) {
    final effectiveProvider = provider ?? _detectProvider(model);

    switch (effectiveProvider) {
      case 'openai':
        return createOpenAIFromEnvironment(
          model: model,
          baseUrl: baseUrl,
          timeout: timeout,
          maxRetries: maxRetries,
          retryDelay: retryDelay,
          customHeaders: customHeaders,
          enableLogging: enableLogging,
          modelInfo: modelInfo,
          httpClient: httpClient,
        );

      case 'anthropic':
        return createAnthropicFromEnvironment(
          model: model,
          baseUrl: baseUrl,
          timeout: timeout,
          maxRetries: maxRetries,
          retryDelay: retryDelay,
          customHeaders: customHeaders,
          enableLogging: enableLogging,
          modelInfo: modelInfo,
          httpClient: httpClient,
        );

      case 'gemini':
        return createGeminiFromEnvironment(
          model: model,
          baseUrl: baseUrl,
          timeout: timeout,
          maxRetries: maxRetries,
          retryDelay: retryDelay,
          customHeaders: customHeaders,
          enableLogging: enableLogging,
          modelInfo: modelInfo,
          httpClient: httpClient,
        );

      default:
        throw ArgumentError('Unknown provider: $effectiveProvider. '
            'Supported providers: openai, anthropic, gemini');
    }
  }

  /// Create multiple clients at once
  ///
  /// Useful for applications that need to work with multiple providers
  /// simultaneously.
  static Map<String, BaseLLMClient> createMultiple({
    required Map<String, Map<String, dynamic>> configurations,
  }) {
    final clients = <String, BaseLLMClient>{};

    for (final entry in configurations.entries) {
      final name = entry.key;
      final config = entry.value;

      final model = config['model'] as String;
      final apiKey = config['apiKey'] as String?;

      clients[name] = create(
        model: model,
        apiKey: apiKey,
        baseUrl: config['baseUrl'] as String?,
        timeout: Duration(seconds: config['timeout'] as int? ?? 60),
        maxRetries: config['maxRetries'] as int? ?? 3,
        retryDelay: Duration(seconds: config['retryDelay'] as int? ?? 1),
        customHeaders:
            config['customHeaders'] as Map<String, String>? ?? const {},
        enableLogging: config['enableLogging'] as bool? ?? false,
        modelInfo: config['modelInfo'] as ModelInfo?,
        httpClient: config['httpClient'] as http.Client?,
      );
    }

    return clients;
  }

  // === Private Helper Methods ===

  /// Detect provider from model name
  static String _detectProvider(String model) {
    // OpenAI model patterns
    if (model.startsWith('gpt-') ||
        model.startsWith('text-') ||
        model.startsWith('davinci') ||
        model.startsWith('curie') ||
        model.startsWith('babbage') ||
        model.startsWith('ada') ||
        model.contains('turbo') ||
        model.contains('instruct')) {
      return 'openai';
    }

    // Anthropic model patterns
    if (model.startsWith('claude-') ||
        model.contains('claude') ||
        model.startsWith('anthropic/')) {
      return 'anthropic';
    }

    // Gemini model patterns
    if (model.startsWith('gemini-') ||
        model.startsWith('models/gemini-') ||
        model.contains('gemini') ||
        model.startsWith('google/')) {
      return 'gemini';
    }

    // If model contains specific provider prefixes
    if (model.contains('openai/') || model.contains('gpt')) {
      return 'openai';
    }

    if (model.contains('anthropic/') || model.contains('claude')) {
      return 'anthropic';
    }

    if (model.contains('google/') || model.contains('gemini')) {
      return 'gemini';
    }

    // Default to unknown - will throw error in create()
    return 'unknown';
  }

  /// Get supported providers
  static List<String> getSupportedProviders() {
    return ['openai', 'openai_compatible', 'anthropic', 'gemini'];
  }

  /// Check if a provider is supported
  static bool isProviderSupported(String provider) {
    return getSupportedProviders().contains(provider.toLowerCase());
  }
}
