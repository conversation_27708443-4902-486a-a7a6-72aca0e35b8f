import 'dart:convert';
import 'dart:io';

import 'package:yaml/yaml.dart';

import 'anthropic/anthropic_models.dart';
import 'gemini/gemini_models.dart';
import 'model_types.dart';
import 'openai/openai_models.dart';

/// Unified model registry that supports JSON/YAML configuration loading
class ModelRegistry extends CoreModelRegistry {
  static final ModelRegistry _instance = ModelRegistry._internal();

  factory ModelRegistry({bool loadBuiltins = true}) => _instance;

  ModelRegistry._internal() {
    loadBuiltinModels();
  }

  /// Load models from JSON configuration
  Future<void> loadFromJson(String jsonData) async {
    try {
      final data = json.decode(jsonData);
      await _loadFromData(data);
    } catch (e) {
      throw ModelRegistryException('Failed to parse JSON configuration: $e');
    }
  }

  /// Load models from JSON file
  Future<void> loadFromJsonFile(String filePath) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        throw ModelRegistryException('JSON file not found: $filePath');
      }
      final content = await file.readAsString();
      await loadFromJson(content);
    } catch (e) {
      throw ModelRegistryException('Failed to load JSON file: $e');
    }
  }

  /// Load models from YAML configuration
  Future<void> loadFromYaml(String yamlData) async {
    try {
      final yamlDoc = loadYaml(yamlData);
      final data = _convertYamlToMap(yamlDoc);
      await _loadFromData(data);
    } catch (e) {
      throw ModelRegistryException('Failed to parse YAML configuration: $e');
    }
  }

  /// Load models from YAML file
  Future<void> loadFromYamlFile(String filePath) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        throw ModelRegistryException('YAML file not found: $filePath');
      }
      final content = await file.readAsString();
      await loadFromYaml(content);
    } catch (e) {
      throw ModelRegistryException('Failed to load YAML file: $e');
    }
  }

  /// Load models from configuration directory
  Future<void> loadFromDirectory(String directoryPath) async {
    final directory = Directory(directoryPath);
    if (!await directory.exists()) {
      throw ModelRegistryException(
          'Configuration directory not found: $directoryPath');
    }

    final files = await directory.list().toList();
    final configFiles = files
        .whereType<File>()
        .where((file) =>
            file.path.endsWith('.json') ||
            file.path.endsWith('.yaml') ||
            file.path.endsWith('.yml'))
        .toList();

    for (final file in configFiles) {
      if (file.path.endsWith('.json')) {
        await loadFromJsonFile(file.path);
      } else if (file.path.endsWith('.yaml') || file.path.endsWith('.yml')) {
        await loadFromYamlFile(file.path);
      }
    }
  }

  /// Load default built-in models from provider registries
  void loadBuiltinModels() {
    // Load OpenAI models
    for (final model in getOpenAIBuiltinModels()) {
      registerModel(model);
    }

    // Load Anthropic models
    for (final model in getAnthropicBuiltinModels()) {
      registerModel(model);
    }

    // Load Gemini models
    for (final model in getGeminiBuiltinModels()) {
      registerModel(model);
    }
  }

  /// Export current registry to JSON
  String exportToJson({bool pretty = true}) {
    final data = _exportToData();
    if (pretty) {
      const encoder = JsonEncoder.withIndent('  ');
      return encoder.convert(data);
    }
    return json.encode(data);
  }

  /// Export current registry to YAML
  String exportToYaml() {
    final data = _exportToData();
    return _toYamlString(data);
  }

  /// Save current registry to JSON file
  Future<void> saveToJsonFile(String filePath, {bool pretty = true}) async {
    final content = exportToJson(pretty: pretty);
    final file = File(filePath);
    await file.writeAsString(content);
  }

  /// Save current registry to YAML file
  Future<void> saveToYamlFile(String filePath) async {
    final content = exportToYaml();
    final file = File(filePath);
    await file.writeAsString(content);
  }

  /// Validate configuration format
  bool validateConfiguration(Map<String, dynamic> config) {
    try {
      if (!config.containsKey('models')) {
        return false;
      }

      final models = config['models'];
      if (models is! List) {
        return false;
      }

      for (final model in models) {
        if (model is! Map<String, dynamic>) {
          return false;
        }

        // Check required fields
        final requiredFields = [
          'name',
          'capabilities'
        ];
        for (final field in requiredFields) {
          if (!model.containsKey(field)) {
            return false;
          }
        }

        // Validate capabilities structure
        final capabilities = model['capabilities'];
        if (capabilities is! List) {
          return false;
        }
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  /// Convert YAML document to Map
  Map<String, dynamic> _convertYamlToMap(dynamic yamlDoc) {
    if (yamlDoc is Map) {
      final result = <String, dynamic>{};
      for (final entry in yamlDoc.entries) {
        final key = entry.key.toString();
        final value = entry.value;

        if (value is Map) {
          result[key] = _convertYamlToMap(value);
        } else if (value is List) {
          result[key] = value
              .map((item) => item is Map ? _convertYamlToMap(item) : item)
              .toList();
        } else {
          result[key] = value;
        }
      }
      return result;
    }
    return yamlDoc as Map<String, dynamic>;
  }

  /// Internal method to load from parsed data
  Future<void> _loadFromData(dynamic data) async {
    if (data is! Map<String, dynamic>) {
      throw ModelRegistryException(
          'Invalid configuration format: expected object');
    }

    if (!validateConfiguration(data)) {
      throw ModelRegistryException('Invalid configuration structure');
    }

    // Load metadata if present
    if (data.containsKey('metadata')) {
      final metadata = data['metadata'] as Map<String, dynamic>;
      _processMetadata(metadata);
    }

    // Load models
    final models = data['models'] as List<dynamic>;
    for (final modelData in models) {
      try {
        final model = ModelInfo.fromJson(modelData as Map<String, dynamic>);
        registerModel(model);
      } catch (e) {
        throw ModelRegistryException(
            'Failed to load model ${modelData['name']}: $e');
      }
    }
  }

  /// Export current registry to data structure
  Map<String, dynamic> _exportToData() {
    final models = getAllModels();
    return {
      'metadata': {
        'version': '1.0',
        'generated_at': DateTime.now().toIso8601String(),
        'total_models': models.length,
      },
      'models': models.map((model) => model.toJson()).toList(),
    };
  }

  /// Process metadata from configuration
  void _processMetadata(Map<String, dynamic> metadata) {
    // Can be extended to handle version compatibility, warnings, etc.
    if (metadata.containsKey('version')) {
      final version = metadata['version'] as String;
      // Handle version-specific logic if needed
    }
  }

  /// Convert data to YAML string
  String _toYamlString(Map<String, dynamic> data, {int indent = 0}) {
    final buffer = StringBuffer();

    for (final entry in data.entries) {
      buffer.write('  ' * indent);
      buffer.write('${entry.key}:');

      if (entry.value is Map) {
        buffer.writeln();
        buffer.write(_toYamlString(entry.value as Map<String, dynamic>,
            indent: indent + 1));
      } else if (entry.value is List) {
        buffer.writeln();
        for (final item in entry.value as List) {
          buffer.write('  ' * (indent + 1));
          buffer.write('- ');
          if (item is Map) {
            buffer.writeln();
            buffer.write(_toYamlString(item as Map<String, dynamic>,
                indent: indent + 2));
          } else {
            buffer.writeln(item.toString());
          }
        }
      } else {
        buffer.write(' ${entry.value}\n');
      }
    }

    return buffer.toString();
  }
}

/// Exception for model registry operations
class ModelRegistryException implements Exception {
  const ModelRegistryException(this.message);

  final String message;

  @override
  String toString() => 'ModelRegistryException: $message';
}

/// Configuration loader utility
class ModelConfigurationLoader {
  /// Auto-detect and load configuration from file
  static Future<ModelRegistry> loadFromFile(String filePath) async {
    final registry = ModelRegistry();

    if (filePath.endsWith('.json')) {
      await registry.loadFromJsonFile(filePath);
    } else if (filePath.endsWith('.yaml') || filePath.endsWith('.yml')) {
      await registry.loadFromYamlFile(filePath);
    } else {
      throw ModelRegistryException('Unsupported file format: $filePath');
    }

    return registry;
  }

  /// Create sample configuration files
  static Future<void> createSampleConfigurations(String outputDir) async {
    final directory = Directory(outputDir);
    if (!await directory.exists()) {
      await directory.create(recursive: true);
    }

    // Create sample JSON configuration
    const sampleJson = '''
{
  "metadata": {
    "version": "1.0",
    "description": "Sample model configuration"
  },
  "models": [
    {
      "name": "custom-model-1",
      "version": "1.0",
      "family": "custom",
      "description": "A custom model example",
      "capabilities": [
        "text_generation",
        "function_calling",
        "streaming",
        "system_messages"
      ],
      "inputTokens": 0.001,
      "outputTokens": 0.002,
      "currency": "USD",
      "pricingTier": "standard",
      "maxTokens": 8192,
      "maxInputTokens": 8192,
      "maxOutputTokens": 4096,
      "supportedLanguages": ["en", "zh-CN"]
    }
  ]
}
''';

    // Create sample YAML configuration
    const sampleYaml = '''
metadata:
  version: "1.0"
  description: "Sample model configuration"

models:
  - name: "custom-model-2"
    version: "1.0"
    family: "custom"
    description: "A custom model example in YAML"
    capabilities:
      - "text_generation"
      - "function_calling"
      - "streaming"
      - "system_messages"
    inputTokens: 0.001
    outputTokens: 0.002
    currency: "USD"
    pricingTier: "standard"
    maxTokens: 8192
    maxInputTokens: 8192
    maxOutputTokens: 4096
    supportedLanguages:
      - "en"
      - "zh-CN"
''';

    await File('$outputDir/sample_models.json').writeAsString(sampleJson);
    await File('$outputDir/sample_models.yaml').writeAsString(sampleYaml);
  }
}
