import 'dart:async';

import 'package:cancellation_token/cancellation_token.dart';

import '../model_context/chat_completion_context.dart';
import 'llm_response.dart';
import 'model_types.dart';
import 'tool_schema.dart';

/// Extension to convert ToolSchema to OpenAI format
extension ToolSchemaToOpenAI on ToolSchema {
  Map<String, dynamic> toOpenAIFormat() {
    return {
      'type': 'function',
      'function': {
        'name': name,
        'description': description,
        'parameters':
            parameters?.toJson() ?? {'type': 'object', 'properties': {}},
      },
    };
  }
}

/// Abstract base class for model clients
abstract class ChatCompletionClient {
  /// Get model information
  ModelInfo get modelInfo;

  /// Create a chat completion
  Future<ChatResponse> createChatCompletion({
    required List<LLMMessage> messages,
    double? temperature,
    int? maxTokens,
    List<String>? stopSequences,
    List<ToolSchema>? tools,
    String? toolChoice,
    Map<String, dynamic>? additionalParams,
    CancellationToken? cancellationToken,
  });

  /// Create a streaming chat completion
  Stream<ChatStreamChunk> createChatCompletionStream({
    required List<LLMMessage> messages,
    double? temperature,
    int? maxTokens,
    List<String>? stopSequences,
    List<ToolSchema>? tools,
    String? toolChoice,
    Map<String, dynamic>? additionalParams,
    CancellationToken? cancellationToken,
  });

  /// Count tokens for messages
  Future<int> countTokens(List<LLMMessage> messages);

  /// Get remaining context length
  Future<int> getRemainingTokens(List<LLMMessage> messages) async {
    final used = await countTokens(messages);
    final max = modelInfo.effectiveMaxInputTokens;
    return max != null ? max - used : 0;
  }

  /// Check if messages fit within context window
  Future<bool> canFitMessages(List<LLMMessage> messages) async {
    final remaining = await getRemainingTokens(messages);
    return remaining >= 0;
  }

  /// Dispose client resources
  Future<void> dispose() async {}
}

/// Embedding client interface
abstract class EmbeddingClient {
  /// Get model information
  ModelInfo get modelInfo;

  /// Create embeddings for text inputs
  Future<EmbeddingResponse> createEmbeddings({
    required List<String> inputs,
    String? encodingFormat,
    int? dimensions,
    Map<String, dynamic>? additionalParams,
    CancellationToken? cancellationToken,
  });

  /// Get embedding dimension
  int get embeddingDimension;

  /// Dispose client resources
  Future<void> dispose() async {}
}
