/// Base exception class for all LLM provider errors
///
/// This provides a common interface for all LLM provider exceptions
/// to ensure consistent error handling across different providers.
abstract class BaseLLMException implements Exception {
  const BaseLLMException(
    this.message, {
    this.type,
    this.code,
    this.statusCode,
    this.details,
    required this.provider,
  });

  /// Human-readable error message
  final String message;

  /// Provider-specific error type (e.g., 'rate_limit_error', 'invalid_request_error')
  final String? type;

  /// Provider-specific error code (e.g., 'insufficient_quota', 'model_not_found')
  final String? code;

  /// HTTP status code if applicable
  final int? statusCode;

  /// Additional error details from the provider
  final Map<String, dynamic>? details;

  /// Provider identifier (e.g., 'openai', 'anthropic')
  final String provider;

  @override
  String toString() {
    final parts = ['${provider.toUpperCase()} Error: $message'];
    if (statusCode != null) parts.add('status: $statusCode');
    if (type != null) parts.add('type: $type');
    if (code != null) parts.add('code: $code');
    return parts.join(', ');
  }

  /// Convert to JSON representation
  Map<String, dynamic> toJson() {
    return {
      'provider': provider,
      'message': message,
      'type': type,
      'code': code,
      'statusCode': statusCode,
      'details': details,
    };
  }

  /// Check if this is an authentication error
  bool get isAuthenticationError =>
      statusCode == 401 ||
      type?.contains('authentication') == true ||
      type?.contains('auth') == true;

  /// Check if this is a rate limit error
  bool get isRateLimitError =>
      statusCode == 429 ||
      type?.contains('rate_limit') == true ||
      code?.contains('rate_limit') == true;

  /// Check if this is a permission error
  bool get isPermissionError =>
      statusCode == 403 ||
      type?.contains('permission') == true ||
      type?.contains('forbidden') == true;

  /// Check if this is a client error (4xx)
  bool get isClientError =>
      statusCode != null && statusCode! >= 400 && statusCode! < 500;

  /// Check if this is a server error (5xx)
  bool get isServerError => statusCode != null && statusCode! >= 500;

  /// Check if this is a transient error that might succeed on retry
  bool get isTransientError => isRateLimitError || isServerError;
}
