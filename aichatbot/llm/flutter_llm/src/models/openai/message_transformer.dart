import 'dart:convert';

import '../../../llm.dart';
import '../model_enums.dart';

/// Transforms autogen LLMMessage objects to OpenAI API format
///
/// Handles model-specific adaptations and multimodal content conversion
class MessageTransformer {
  /// Transform a list of LLMMessage objects to OpenAI API format
  List<Map<String, dynamic>> transformMessages(
    List<LLMMessage> messages,
    String modelFamily,
  ) {
    final result = <Map<String, dynamic>>[];

    for (final message in messages) {
      final transformed = _transformSingleMessage(message, modelFamily);
      result.addAll(transformed);
    }

    return result;
  }

  /// Transform a single message to OpenAI format
  /// Returns a list because some message types may need to be split
  List<Map<String, dynamic>> _transformSingleMessage(
    LLMMessage message,
    String modelFamily,
  ) {
    if (message is SystemMessage) {
      return [_transformSystemMessage(message, modelFamily)];
    } else if (message is UserMessage) {
      return [_transformUserMessage(message, modelFamily)];
    } else if (message is AssistantMessage) {
      return [_transformAssistantMessage(message, modelFamily)];
    } else if (message is FunctionExecutionResultMessage) {
      return _transformFunctionExecutionResultMessage(message, modelFamily);
    } else {
      throw ArgumentError('Unsupported message type: ${message.runtimeType}');
    }
  }

  /// Transform SystemMessage to OpenAI format
  Map<String, dynamic> _transformSystemMessage(
    SystemMessage message,
    String modelFamily,
  ) {
    final result = <String, dynamic>{
      'role': 'system',
      'content': message.content,
    };

    // Add metadata if present
    if (message.metadata.isNotEmpty) {
      result['metadata'] = message.metadata;
    }

    return result;
  }

  /// Transform UserMessage to OpenAI format
  Map<String, dynamic> _transformUserMessage(
    UserMessage message,
    String modelFamily,
  ) {
    final result = <String, dynamic>{
      'role': 'user',
    };

    // Handle multimodal content (text + images)
    if (message.hasImages) {
      final content = <Map<String, dynamic>>[];

      // Add text content if present
      if (message.text != null && message.text!.isNotEmpty) {
        content.add({
          'type': 'text',
          'text': message.text,
        });
      }

      // Add images
      for (final image in message.images) {
        content.add({
          'type': 'image_url',
          'image_url': {
            'url': image.toDataUrl(),
            'detail': _getImageDetail(modelFamily),
          },
        });
      }

      result['content'] = content;
    } else {
      // Text-only content
      result['content'] = message.text ?? '';
    }

    // Add metadata if present
    if (message.metadata.isNotEmpty) {
      result['metadata'] = message.metadata;
    }

    return result;
  }

  /// Transform AssistantMessage to OpenAI format
  Map<String, dynamic> _transformAssistantMessage(
    AssistantMessage message,
    String modelFamily,
  ) {
    final result = <String, dynamic>{
      'role': 'assistant',
    };

    // Handle text content
    if (message.text != null && message.text!.isNotEmpty) {
      result['content'] = message.text;
    }

    // Handle function calls
    if (message.functionCalls.isNotEmpty) {
      result['tool_calls'] = message.functionCalls
          .map((call) => {
                'id': call.id,
                'type': 'function',
                'function': {
                  'name': call.name,
                  'arguments': call.arguments,
                },
              })
          .toList();
    }

    // Add metadata if present
    if (message.metadata.isNotEmpty) {
      result['metadata'] = message.metadata;
    }

    return result;
  }

  /// Transform FunctionExecutionResultMessage to OpenAI format
  List<Map<String, dynamic>> _transformFunctionExecutionResultMessage(
    FunctionExecutionResultMessage message,
    String modelFamily,
  ) {
    return message.content
        .map((result) => <String, dynamic>{
              'role': 'tool',
              'tool_call_id': result.callId,
              'content': _formatFunctionResult(result.content, result.isError),
            })
        .toList();
  }

  /// Format function execution result for OpenAI
  String _formatFunctionResult(dynamic result, bool isError) {
    if (isError) {
      return jsonEncode({
        'error': true,
        'message': result.toString(),
      });
    } else {
      // If result is already a string, use it directly
      if (result is String) {
        return result;
      }

      // Otherwise, JSON encode it
      try {
        return jsonEncode(result);
      } catch (e) {
        // If JSON encoding fails, convert to string
        return result.toString();
      }
    }
  }

  /// Get appropriate image detail level based on model family
  String _getImageDetail(String modelFamily) {
    switch (modelFamily.toLowerCase()) {
      case 'gpt-4o':
      case 'gpt-4-vision':
        return 'high';
      case 'gemini':
        return 'auto';
      default:
        return 'auto';
    }
  }

  /// Validate messages before transformation
  void validateMessages(
      List<LLMMessage> messages, Set<ModelCapability> capabilities) {
    if (messages.isEmpty) {
      throw ArgumentError('Messages list cannot be empty');
    }

    // Check for vision support
    if (!capabilities.supportsImageInput) {
      for (final message in messages) {
        if (message.hasImages) {
          throw ArgumentError(
              'Model does not support image input, but images were found in messages');
        }
      }
    }

    // Check for function calling support
    bool hasFunctionCalls = false;
    for (final message in messages) {
      if (message is AssistantMessage && message.functionCalls.isNotEmpty) {
        hasFunctionCalls = true;
        break;
      }
      if (message is FunctionExecutionResultMessage) {
        hasFunctionCalls = true;
        break;
      }
    }

    if (hasFunctionCalls && !capabilities.supportsFunctionCalling) {
      throw ArgumentError(
          'Model does not support function calling, but function calls were found in messages');
    }

    // Validate message sequence
    _validateMessageSequence(messages);
  }

  /// Validate the sequence of messages follows OpenAI conventions
  void _validateMessageSequence(List<LLMMessage> messages) {
    for (int i = 0; i < messages.length; i++) {
      final message = messages[i];

      // Tool results must follow assistant messages with tool calls
      if (message is FunctionExecutionResultMessage) {
        if (i == 0) {
          throw ArgumentError(
              'FunctionExecutionResult cannot be the first message');
        }

        // Find the corresponding assistant message for each result
        for (final result in message.content) {
          bool hasCorrespondingCall = false;
          for (int j = i - 1; j >= 0; j--) {
            final prevMessage = messages[j];
            if (prevMessage is AssistantMessage) {
              for (final call in prevMessage.functionCalls) {
                if (call.id == result.callId) {
                  hasCorrespondingCall = true;
                  break;
                }
              }
              if (hasCorrespondingCall) break;
            }
          }

          if (!hasCorrespondingCall) {
            throw ArgumentError(
                'FunctionExecutionResult with call_id "${result.callId}" has no corresponding function call');
          }
        }
      }
    }
  }

  /// Apply model-specific transformations
  List<Map<String, dynamic>> applyModelSpecificTransformations(
    List<Map<String, dynamic>> messages,
    String modelFamily,
  ) {
    switch (modelFamily.toLowerCase()) {
      case 'claude':
        return _applyClaudeTransformations(messages);
      case 'gemini':
        return _applyGeminiTransformations(messages);
      default:
        return messages;
    }
  }

  /// Apply Claude-specific transformations
  List<Map<String, dynamic>> _applyClaudeTransformations(
    List<Map<String, dynamic>> messages,
  ) {
    // Claude-specific rules:
    // 1. Cannot end with assistant message that has trailing whitespace
    // 2. System messages should be merged if consecutive

    final result = <Map<String, dynamic>>[];

    for (int i = 0; i < messages.length; i++) {
      final message = Map<String, dynamic>.from(messages[i]);

      // Trim trailing whitespace from assistant messages
      if (message['role'] == 'assistant' && message['content'] is String) {
        message['content'] = (message['content'] as String).trimRight();
      }

      result.add(message);
    }

    return result;
  }

  /// Apply Gemini-specific transformations
  List<Map<String, dynamic>> _applyGeminiTransformations(
    List<Map<String, dynamic>> messages,
  ) {
    // Gemini-specific rules:
    // 1. May have different content structure requirements
    // 2. Different image handling

    final result = <Map<String, dynamic>>[];

    for (final message in messages) {
      final transformed = Map<String, dynamic>.from(message);

      // Gemini may need different image detail handling
      if (transformed['content'] is List) {
        final content = transformed['content'] as List;
        for (final item in content) {
          if (item is Map && item['type'] == 'image_url') {
            final imageUrl = item['image_url'] as Map<String, dynamic>;
            imageUrl['detail'] = 'auto'; // Gemini prefers auto
          }
        }
      }

      result.add(transformed);
    }

    return result;
  }

  /// Get message statistics for debugging
  Map<String, dynamic> getTransformationStats(List<LLMMessage> messages) {
    final stats = <String, int>{};
    int totalImages = 0;
    int totalFunctionCalls = 0;

    for (final message in messages) {
      final type = message.runtimeType.toString();
      stats[type] = (stats[type] ?? 0) + 1;

      totalImages += message.images.length;

      if (message is AssistantMessage) {
        totalFunctionCalls += message.functionCalls.length;
      }
    }

    return {
      'messageCount': messages.length,
      'messagesByType': stats,
      'totalImages': totalImages,
      'totalFunctionCalls': totalFunctionCalls,
    };
  }
}

/// Utility functions for message transformation
class MessageTransformationUtils {
  /// Check if a message sequence is valid for OpenAI
  static bool isValidMessageSequence(List<LLMMessage> messages) {
    try {
      final transformer = MessageTransformer();
      transformer._validateMessageSequence(messages);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Get estimated token count for transformed messages
  static int estimateTokenCount(
      List<Map<String, dynamic>> transformedMessages) {
    int totalTokens = 0;

    for (final message in transformedMessages) {
      // Basic estimation - can be improved with actual tokenizer
      final content = message['content'];
      if (content is String) {
        totalTokens += (content.length / 4).ceil();
      } else if (content is List) {
        for (final item in content) {
          if (item is Map && item['type'] == 'text') {
            final text = item['text'] as String? ?? '';
            totalTokens += (text.length / 4).ceil();
          } else if (item is Map && item['type'] == 'image_url') {
            totalTokens += 85; // Base vision token cost
          }
        }
      }

      // Add tokens for tool calls
      if (message['tool_calls'] != null) {
        final toolCalls = message['tool_calls'] as List;
        totalTokens += toolCalls.length * 20; // Rough estimate
      }
    }

    return totalTokens + 10; // Add overhead
  }

  /// Convert back from OpenAI format to autogen format (for testing)
  static List<LLMMessage> fromOpenAIFormat(
      List<Map<String, dynamic>> messages) {
    final result = <LLMMessage>[];

    for (final message in messages) {
      final role = message['role'] as String;

      switch (role) {
        case 'system':
          result.add(SystemMessage(
            message['content'] as String,
            metadata: message['metadata'] as Map<String, dynamic>? ?? {},
          ));
          break;

        case 'user':
          final content = message['content'];
          if (content is String) {
            result.add(UserMessage(
              text: content,
              metadata: message['metadata'] as Map<String, dynamic>? ?? {},
            ));
          } else if (content is List) {
            String? text;
            final images = <Image>[];

            for (final item in content) {
              if (item is Map) {
                if (item['type'] == 'text') {
                  text = item['text'] as String?;
                } else if (item['type'] == 'image_url') {
                  // Would need to reconstruct Image object
                  // This is a simplified version
                }
              }
            }

            result.add(UserMessage(
              text: text,
              images: images,
              metadata: message['metadata'] as Map<String, dynamic>? ?? {},
            ));
          }
          break;

        case 'assistant':
          final toolCalls = message['tool_calls'] as List<dynamic>?;
          final functionCalls = <FunctionCall>[];

          if (toolCalls != null) {
            for (final call in toolCalls) {
              if (call is Map) {
                final function = call['function'] as Map<String, dynamic>;
                functionCalls.add(FunctionCall(
                  id: call['id'] as String,
                  name: function['name'] as String,
                  arguments: function['arguments'] as String,
                ));
              }
            }
          }

          result.add(AssistantMessage(
            text: message['content'] as String?,
            functionCalls: functionCalls,
            metadata: message['metadata'] as Map<String, dynamic>? ?? {},
          ));
          break;

        case 'tool':
          // Would need to parse function execution result
          // This is a placeholder
          break;
      }
    }

    return result;
  }
}
