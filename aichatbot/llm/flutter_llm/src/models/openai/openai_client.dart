import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:http/http.dart' as http;
import 'package:llm/llm.dart';

/// OpenAI Chat Completions client implementation
///
/// This client extends BaseLLMClient to provide OpenAI-specific functionality
/// while leveraging the common HTTP transport, retry logic, and usage tracking
/// from the base class.
///
/// ## Features
///
/// - Full OpenAI Chat Completions API support
/// - Streaming and non-streaming responses
/// - Function/tool calling with OpenAI format
/// - Token counting using tiktoken
/// - Usage tracking and statistics
/// - Proper error handling with OpenAI-specific exceptions
/// - Support for various OpenAI configurations (standard, Azure, compatible)
///
/// ## Usage
///
/// ```dart
/// // Basic usage
/// final client = OpenAIClient(
///   model: 'gpt-4',
///   apiKey: 'your-api-key',
/// );
///
/// final response = await client.createChatCompletion(
///   messages: [UserMessage(text: 'Hello!')],
/// );
///
/// print(response.content.text);
///
/// // Streaming
/// await for (final chunk in client.createChatCompletionStream(
///   messages: [UserMessage(text: 'Tell me a story')],
/// )) {
///   print(chunk.text);
/// }
///
/// // Usage tracking
/// print('Total tokens used: ${client.totalUsage.promptTokens + client.totalUsage.completionTokens}');
/// ```
class OpenAIClient extends BaseLLMClient {
  // === OpenAI Configuration ===
  final String apiKey;
  final String? customBaseUrl;

  // === OpenAI Components ===
  final MessageTransformer _messageTransformer;
  final OpenAIToolConverter _toolConverter;
  final TokenCounter _tokenCounter;
  final ModelInfo _modelInfo;

  /// Create an OpenAI client
  ///
  /// Parameters:
  /// - [model]: OpenAI model identifier (e.g., 'gpt-4', 'gpt-3.5-turbo')
  /// - [apiKey]: OpenAI API key (required)
  /// - [baseUrl]: Custom base URL (defaults to https://api.openai.com/v1)
  /// - [timeout]: Request timeout duration
  /// - [maxRetries]: Maximum retry attempts on failure
  /// - [retryDelay]: Delay between retry attempts
  /// - [customHeaders]: Additional headers to include
  /// - [enableLogging]: Enable request/response logging
  /// - [modelInfo]: Custom model info (auto-detected if not provided)
  /// - [httpClient]: Custom HTTP client instance
  OpenAIClient({
    required String model,
    required this.apiKey,
    String? baseUrl,
    Duration timeout = const Duration(seconds: 60),
    int maxRetries = 3,
    Duration retryDelay = const Duration(seconds: 1),
    Map<String, String> customHeaders = const {},
    bool enableLogging = false,
    ModelInfo? modelInfo,
    http.Client? httpClient,
  })  : customBaseUrl = baseUrl,
        _messageTransformer = MessageTransformer(),
        _toolConverter = const OpenAIToolConverter(),
        _tokenCounter = TokenCounterFactory.create(model),
        _modelInfo = modelInfo ??
            ModelRegistry().getModelByNameOrId(model) ??
            (throw Exception('Model not found: $model')),
        super(
          model: model,
          timeout: timeout,
          maxRetries: maxRetries,
          retryDelay: retryDelay,
          customHeaders: customHeaders,
          enableLogging: enableLogging,
          httpClient: httpClient,
        ) {
    if (apiKey.isEmpty) {
      throw ArgumentError('OpenAI API key cannot be empty');
    }

    // Validate model capabilities
    _validateModelCapabilities();
  }

  /// Create OpenAI client with environment variable API key
  ///
  /// Automatically reads the API key from OPENAI_API_KEY environment variable.
  OpenAIClient.fromEnvironment({
    required String model,
    String? baseUrl,
    Duration timeout = const Duration(seconds: 60),
    int maxRetries = 3,
    Duration retryDelay = const Duration(seconds: 1),
    Map<String, String> customHeaders = const {},
    bool enableLogging = false,
    ModelInfo? modelInfo,
    http.Client? httpClient,
  }) : this(
          model: model,
          apiKey: Platform.environment['OPENAI_API_KEY'] ??
              (throw ArgumentError(
                  'OPENAI_API_KEY environment variable not found')),
          baseUrl: baseUrl,
          timeout: timeout,
          maxRetries: maxRetries,
          retryDelay: retryDelay,
          customHeaders: customHeaders,
          enableLogging: enableLogging,
          modelInfo: modelInfo,
          httpClient: httpClient,
        );

  /// Create Azure OpenAI client
  ///
  /// Factory constructor for Azure OpenAI Service with specific configuration.
  factory OpenAIClient.azure({
    required String model,
    required String endpoint,
    required String deployment,
    required String apiVersion,
    String? apiKey,
    Duration timeout = const Duration(seconds: 60),
    int maxRetries = 3,
    Duration retryDelay = const Duration(seconds: 1),
    Map<String, String> customHeaders = const {},
    bool enableLogging = false,
    ModelInfo? modelInfo,
    http.Client? httpClient,
  }) {
    final effectiveApiKey =
        apiKey ?? Platform.environment['AZURE_OPENAI_API_KEY'];
    if (effectiveApiKey == null || effectiveApiKey.isEmpty) {
      throw ArgumentError(
          'Azure OpenAI API key is required. Provide via apiKey parameter or AZURE_OPENAI_API_KEY environment variable.');
    }

    // Azure OpenAI uses different URL structure
    final baseUrl = '$endpoint/openai/deployments/$deployment';

    // Azure uses api-key header instead of Authorization
    final azureHeaders = <String, String>{
      'api-version': apiVersion,
      ...customHeaders,
    };

    return OpenAIClient(
      model: model,
      apiKey: effectiveApiKey,
      baseUrl: baseUrl,
      timeout: timeout,
      maxRetries: maxRetries,
      retryDelay: retryDelay,
      customHeaders: azureHeaders,
      enableLogging: enableLogging,
      modelInfo: modelInfo,
      httpClient: httpClient,
    );
  }

  /// Create client for OpenAI-compatible endpoints
  ///
  /// Factory constructor for OpenAI-compatible APIs like local models.
  factory OpenAIClient.compatible({
    required String model,
    required String baseUrl,
    String? apiKey,
    Duration timeout = const Duration(seconds: 60),
    int maxRetries = 3,
    Duration retryDelay = const Duration(seconds: 1),
    Map<String, String> customHeaders = const {},
    bool enableLogging = false,
    ModelInfo? modelInfo,
    http.Client? httpClient,
  }) {
    return OpenAIClient(
      model: model,
      apiKey:
          apiKey ?? 'not-needed', // Some compatible APIs don't need API keys
      baseUrl: baseUrl,
      timeout: timeout,
      maxRetries: maxRetries,
      retryDelay: retryDelay,
      customHeaders: customHeaders,
      enableLogging: enableLogging,
      modelInfo: modelInfo,
      httpClient: httpClient,
    );
  }

  // === BaseLLMClient Implementation ===

  @override
  Map<String, String> get defaultHeaders {
    final headers = <String, String>{
      'Content-Type': 'application/json',
      'User-Agent': 'autogen-dart/1.0.0',
      'Accept': 'application/json',
    };

    // Azure uses api-key header, standard OpenAI uses Authorization
    if (customHeaders.containsKey('api-version')) {
      // Azure configuration detected
      headers['api-key'] = apiKey;
    } else {
      // Standard OpenAI configuration
      headers['Authorization'] = 'Bearer $apiKey';
    }

    return headers;
  }

  @override
  ModelInfo get modelInfo => _modelInfo;

  @override
  Uri buildRequestUri(RequestType type, Map<String, dynamic> params) {
    final baseUrl = customBaseUrl ?? 'https://api.openai.com/v1';

    switch (type) {
      case RequestType.chatCompletion:
      case RequestType.chatCompletionStream:
        return Uri.parse('$baseUrl/chat/completions');
      case RequestType.tokenCount:
        // OpenAI doesn't have a direct token count endpoint
        // This is handled internally by the token counter
        throw UnsupportedError(
            'OpenAI does not support direct token counting endpoint');
      case RequestType.listModels:
        return Uri.parse('$baseUrl/models');
      case RequestType.modelInfo:
        final modelId = params['model'] ?? model;
        return Uri.parse('$baseUrl/models/$modelId');
      case RequestType.embedding:
        return Uri.parse('$baseUrl/embeddings');
      case RequestType.batchEmbedding:
        // OpenAI uses the same endpoint for batch embeddings
        return Uri.parse('$baseUrl/embeddings');
    }
  }

  @override
  void validateRequest(
    List<LLMMessage> messages,
    List<ToolSchema>? tools,
    String? toolChoice,
  ) {
    if (messages.isEmpty) {
      throw ArgumentError('Messages cannot be empty');
    }

    // Use existing message transformer validation
    _messageTransformer.validateMessages(messages, _modelInfo.capabilities);

    // Validate tools if provided
    if (tools != null && tools.isNotEmpty) {
      if (!_modelInfo.hasCapability(ModelCapability.functionCalling)) {
        throw ArgumentError('Model $model does not support function calling');
      }

      if (toolChoice != null) {
        _toolConverter.validateToolChoice(toolChoice, tools);
      }
    }
  }

  @override
  Map<String, dynamic> buildRequestBody({
    required List<LLMMessage> messages,
    double? temperature,
    int? maxTokens,
    List<String>? stopSequences,
    List<ToolSchema>? tools,
    String? toolChoice,
    Map<String, dynamic>? additionalParams,
    bool stream = false,
  }) {
    // Transform messages using existing transformer
    final openaiMessages = _messageTransformer.transformMessages(
        messages, _modelInfo.family ?? '');

    final body = <String, dynamic>{
      'model': model,
      'messages': openaiMessages,
      'stream': stream,
    };

    // Add optional parameters
    if (temperature != null) body['temperature'] = temperature;
    if (maxTokens != null) body['max_tokens'] = maxTokens;
    if (stopSequences != null && stopSequences.isNotEmpty) {
      body['stop'] = stopSequences;
    }

    // Handle tools using existing converter
    if (tools != null && tools.isNotEmpty) {
      body['tools'] = _toolConverter.convertTools(tools);
      if (toolChoice != null) {
        body['tool_choice'] = _toolConverter.convertToolChoice(toolChoice);
      }
    }

    // Add any additional parameters
    if (additionalParams != null) {
      body.addAll(additionalParams);
    }

    return body;
  }

  @override
  ChatResponse parseResponse(Map<String, dynamic> response) {
    Map<String, dynamic> message;
    String? finishReason;
    
    // Handle both standard OpenAI format and alternative format
    if (response.containsKey('choices')) {
      // Standard OpenAI format
      final choices = response['choices'] as List<dynamic>;
      final choice = choices.first as Map<String, dynamic>;
      message = choice['message'] as Map<String, dynamic>;
      finishReason = choice['finish_reason'] as String?;
    } else {
      // Alternative format (direct message and finish_reason fields)
      message = response['message'] as Map<String, dynamic>;
      finishReason = response['finish_reason'] as String?;
    }
    
    final usage = response['usage'] as Map<String, dynamic>?;

    // Parse content and function calls
    String? text;
    List<FunctionCall> functionCalls = [];

    if (message['content'] != null) {
      final content = message['content'];
      if (content is String) {
        // Standard string content
        text = content;
      } else if (content is List) {
        // Array format content: [{"type": "text", "text": "..."}]
        final textParts = <String>[];
        for (final part in content) {
          if (part is Map<String, dynamic> && 
              part['type'] == 'text' && 
              part['text'] is String) {
            textParts.add(part['text'] as String);
          }
        }
        text = textParts.join();
      }
    }

    if (message['tool_calls'] != null) {
      final toolCalls = message['tool_calls'] as List<dynamic>;
      functionCalls = toolCalls.map((call) {
        final callMap = call as Map<String, dynamic>;
        final function = callMap['function'] as Map<String, dynamic>;

        return FunctionCall(
          id: callMap['id'] as String,
          name: function['name'] as String,
          arguments: function['arguments'] as String,
        );
      }).toList();
    }

    final assistantMessage = AssistantMessage(
      text: text,
      functionCalls: functionCalls,
    );

    return ChatResponse(
      content: assistantMessage,
      finishReason: finishReason,
      usage: usage != null ? RequestUsage.fromJson(usage) : null,
      modelInfo: _modelInfo,
    );
  }

  @override
  Stream<ChatStreamChunk> parseStreamResponse(Stream<String> lines) async* {
    final chunks = <String>[];
    final functionCalls = <int, FunctionCall>{};

    await for (final line in lines) {
      if (!line.startsWith('data: ')) continue;

      final data = line.substring(6).trim();
      if (data == '[DONE]') break;

      try {
        final json = jsonDecode(data) as Map<String, dynamic>;
        final choices = json['choices'] as List<dynamic>?;

        if (choices == null || choices.isEmpty) continue;

        final choice = choices.first as Map<String, dynamic>;
        final delta = choice['delta'] as Map<String, dynamic>?;

        if (delta == null) continue;

        // Handle content delta
        if (delta['content'] != null) {
          final content = delta['content'] as String;
          chunks.add(content);

          yield ChatStreamChunk(
            id: json['id'] as String?,
            text: content,
            isFirst: chunks.length == 1,
            modelInfo: _modelInfo,
          );
        }

        // Handle tool calls delta (simplified version)
        if (delta['tool_calls'] != null) {
          final toolCalls = delta['tool_calls'] as List<dynamic>;

          for (final toolCall in toolCalls) {
            final toolCallMap = toolCall as Map<String, dynamic>;
            final index = toolCallMap['index'] as int;

            if (!functionCalls.containsKey(index)) {
              functionCalls[index] = FunctionCall(
                id: toolCallMap['id'] as String? ?? '',
                name: '',
                arguments: '',
              );
            }

            final function = toolCallMap['function'] as Map<String, dynamic>?;
            if (function != null) {
              if (function['name'] != null) {
                final currentCall = functionCalls[index]!;
                functionCalls[index] = FunctionCall(
                  id: currentCall.id,
                  name: currentCall.name + (function['name'] as String),
                  arguments: currentCall.arguments,
                );
              }

              if (function['arguments'] != null) {
                final currentCall = functionCalls[index]!;
                functionCalls[index] = FunctionCall(
                  id: currentCall.id,
                  name: currentCall.name,
                  arguments:
                      currentCall.arguments + (function['arguments'] as String),
                );
              }
            }
          }
        }

        // Check for finish reason
        final finishReason = choice['finish_reason'] as String?;
        if (finishReason != null) {
          final usage = json['usage'] != null
              ? RequestUsage.fromJson(json['usage'] as Map<String, dynamic>)
              : null;

          final isToolCall = functionCalls.isNotEmpty;

          yield ChatStreamChunk(
            id: json['id'] as String?,
            text: isToolCall ? null : chunks.join(),
            functionCalls: isToolCall ? functionCalls.values.toList() : null,
            finishReason: finishReason,
            usage: usage,
            isLast: true,
            modelInfo: _modelInfo,
          );

          break;
        }
      } catch (e) {
        // Skip malformed JSON chunks
        continue;
      }
    }
  }

  @override
  Future<int> countTokens(List<LLMMessage> messages) async {
    return _tokenCounter.countTokens(messages, model);
  }

  @override
  Exception createHttpException(
      int statusCode, String body, Map<String, dynamic>? errorData) {
    return LLMExceptionFactory.fromHttpResponse(
      provider: 'openai',
      statusCode: statusCode,
      responseBody: body,
      errorData: errorData,
    );
  }

  // === OpenAI-Specific Methods ===

  /// Get token count for tools
  int countToolTokens(List<ToolSchema> tools) {
    return _tokenCounter.countToolTokens(tools, model);
  }

  /// Get token limit for this model
  int getTokenLimit() {
    return _tokenCounter.getTokenLimit(model);
  }

  /// Validate model capabilities during initialization
  void _validateModelCapabilities() {
    if (_modelInfo.effectiveMaxTokens == null) {
      throw ArgumentError('Model info must include maxTokens for $model');
    }
  }

  @override
  Future<bool> healthCheck() async {
    try {
      // Try a simple completion request
      await createChatCompletion(
        messages: [UserMessage(text: 'test')],
        maxTokens: 1,
      );
      return true;
    } catch (e) {
      return false;
    }
  }
}
