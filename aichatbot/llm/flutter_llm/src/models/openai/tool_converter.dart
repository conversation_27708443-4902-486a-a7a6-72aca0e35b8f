import 'dart:convert';

import '../tool_schema.dart';
import '../llm_auth_exceptions.dart';

/// Converts autogen ToolSchema objects to OpenAI API format
///
/// Handles tool validation, conversion, and tool choice processing
class OpenAIToolConverter {
  /// Create a new OpenAI tool converter
  const OpenAIToolConverter();

  /// Convert a list of ToolSchema objects to OpenAI tools format
  List<Map<String, dynamic>> convertTools(List<ToolSchema> tools) {
    if (tools.isEmpty) return [];

    final converted = <Map<String, dynamic>>[];
    final usedNames = <String>{};

    for (final tool in tools) {
      final convertedTool = _convertSingleTool(tool);
      final name = convertedTool['function']['name'] as String;

      // Check for duplicate names
      if (usedNames.contains(name)) {
        throw LLMBadRequestException(
          provider: 'openai',
          message: 'Duplicate tool name: $name. Tool names must be unique.',
        );
      }
      usedNames.add(name);

      converted.add(convertedTool);
    }

    return converted;
  }

  /// Convert a single ToolSchema to OpenAI format
  Map<String, dynamic> _convertSingleTool(ToolSchema tool) {
    _validateToolSchema(tool);

    final function = <String, dynamic>{
      'name': tool.name,
    };

    // Add description if present
    if (tool.description != null && tool.description!.isNotEmpty) {
      function['description'] = tool.description;
    }

    // Add parameters if present
    if (tool.parameters != null) {
      function['parameters'] = _convertParameters(tool.parameters!);
    } else {
      // OpenAI requires parameters object even if empty
      function['parameters'] = {
        'type': 'object',
        'properties': <String, dynamic>{},
      };
    }

    // Add strict mode if specified
    if (tool.strict == true) {
      function['strict'] = true;
    }

    return {
      'type': 'function',
      'function': function,
    };
  }

  /// Convert parameters schema to OpenAI format
  Map<String, dynamic> _convertParameters(ParametersSchema parameters) {
    // Validate the parameters schema
    _validateParametersSchema(parameters);

    // Convert ParametersSchema to JSON format
    return parameters.toJson();
  }

  /// Convert tool choice parameter to OpenAI format
  dynamic convertToolChoice(String? toolChoice) {
    if (toolChoice == null) return null;

    switch (toolChoice.toLowerCase()) {
      case 'auto':
        return 'auto'; // Return string, not object
      case 'none':
        return 'none'; // Return string, not object
      case 'required':
      case 'any':
        return 'required'; // Return string, not object
      default:
        // Assume it's a specific tool name
        return {
          'type': 'function',
          'function': {'name': toolChoice},
        };
    }
  }

  /// Convert specific tool choice to OpenAI format
  Map<String, dynamic> convertSpecificToolChoice(ToolSchema tool) {
    return {
      'type': 'function',
      'function': {'name': tool.name},
    };
  }

  /// Validate a tool schema
  void _validateToolSchema(ToolSchema tool) {
    // Validate name
    if (tool.name.isEmpty) {
      throw LLMBadRequestException(
          provider: 'openai', 
          message: 'Tool name cannot be empty');
    }

    if (!_isValidToolName(tool.name)) {
      throw LLMBadRequestException(
          provider: 'openai',
          message: 'Invalid tool name: ${tool.name}. Tool names must match ^[a-zA-Z0-9_-]+\$ and be ≤ 64 characters.');
    }

    // Validate description length
    if (tool.description != null && tool.description!.length > 1024) {
      throw LLMBadRequestException(
          provider: 'openai',
          message: 'Tool description too long: ${tool.description!.length} characters. Maximum is 1024.');
    }
  }

  /// Validate parameters schema
  void _validateParametersSchema(ParametersSchema parameters) {
    // Basic JSON Schema validation
    if (parameters.type != 'object') {
      throw LLMBadRequestException(
          provider: 'openai',
          message: 'Parameters schema type must be "object", got: ${parameters.type}');
    }

    // Validate properties - convert PropertySchema to Map for validation
    final propertiesMap = <String, dynamic>{};
    parameters.properties.forEach((key, propertySchema) {
      propertiesMap[key] = propertySchema.toJson();
    });

    _validateProperties(propertiesMap);

    // Validate required if present
    if (parameters.required != null) {
      for (final field in parameters.required!) {
        if (!parameters.properties.containsKey(field)) {
          throw LLMBadRequestException(
              provider: 'openai',
              message: 'Required field "$field" not found in properties');
        }
      }
    }
  }

  /// Validate properties in parameters schema
  void _validateProperties(Map<String, dynamic> properties) {
    for (final entry in properties.entries) {
      final propertyName = entry.key;
      final propertySchema = entry.value;

      if (propertySchema is! Map<String, dynamic>) {
        throw LLMBadRequestException(
            provider: 'openai',
            message: 'Property "$propertyName" schema must be an object');
      }

      // Validate property has type
      if (!propertySchema.containsKey('type')) {
        throw LLMBadRequestException(
            provider: 'openai',
            message: 'Property "$propertyName" must have a "type" field');
      }

      final propertyType = propertySchema['type'];
      if (!_isValidPropertyType(propertyType)) {
        throw LLMBadRequestException(
            provider: 'openai',
            message: 'Property "$propertyName" has invalid type: $propertyType');
      }
    }
  }

  /// Check if tool name is valid according to OpenAI rules
  bool _isValidToolName(String name) {
    // OpenAI tool names must match ^[a-zA-Z0-9_-]+$ and be ≤ 64 characters
    if (name.length > 64) return false;

    final pattern = RegExp(r'^[a-zA-Z0-9_-]+$');
    return pattern.hasMatch(name);
  }

  /// Check if property type is valid
  bool _isValidPropertyType(dynamic type) {
    const validTypes = {
      'string',
      'number',
      'integer',
      'boolean',
      'array',
      'object',
      'null'
    };

    return type is String && validTypes.contains(type);
  }

  /// Get tool conversion statistics
  Map<String, dynamic> getConversionStats(List<ToolSchema> tools) {
    final stats = {
      'totalTools': tools.length,
      'toolsWithDescription': 0,
      'toolsWithParameters': 0,
      'toolsWithStrictMode': 0,
      'totalParameterProperties': 0,
      'averageNameLength': 0.0,
    };

    if (tools.isEmpty) return stats;

    int totalNameLength = 0;
    int totalProperties = 0;

    for (final tool in tools) {
      totalNameLength += tool.name.length;

      if (tool.description != null && tool.description!.isNotEmpty) {
        stats['toolsWithDescription'] =
            (stats['toolsWithDescription'] as int) + 1;
      }

      if (tool.parameters != null) {
        stats['toolsWithParameters'] =
            (stats['toolsWithParameters'] as int) + 1;

        final properties = tool.parameters!.properties;
        totalProperties += properties.length;
      }

      if (tool.strict == true) {
        stats['toolsWithStrictMode'] =
            (stats['toolsWithStrictMode'] as int) + 1;
      }
    }

    stats['averageNameLength'] = totalNameLength / tools.length;
    stats['totalParameterProperties'] = totalProperties;

    return stats;
  }

  /// Validate tool choice against available tools
  void validateToolChoice(String? toolChoice, List<ToolSchema> availableTools) {
    if (toolChoice == null) return;

    // Check for standard choices
    const standardChoices = {'auto', 'none', 'required', 'any'};
    if (standardChoices.contains(toolChoice.toLowerCase())) {
      return;
    }

    // Check if it's a specific tool name
    final toolExists = availableTools.any((tool) => tool.name == toolChoice);
    if (!toolExists) {
      throw LLMBadRequestException(
          provider: 'openai',
          message: 'Tool choice "$toolChoice" not found in available tools: ${availableTools.map((t) => t.name).toList()}');
    }
  }

  /// Create a tool schema from OpenAI format (for testing/reverse conversion)
  static ToolSchema fromOpenAIFormat(Map<String, dynamic> openAITool) {
    final function = openAITool['function'] as Map<String, dynamic>;

    return ToolSchema(
      name: function['name'] as String,
      description: function['description'] as String?,
      parameters: function['parameters'] != null
          ? ParametersSchema.fromJson(
              function['parameters'] as Map<String, dynamic>)
          : null,
      strict: function['strict'] as bool?,
    );
  }

  /// Normalize tool name according to OpenAI rules
  static String normalizeToolName(String name) {
    // Replace invalid characters with underscores
    String normalized = name.replaceAll(RegExp(r'[^a-zA-Z0-9_-]'), '_');

    // Truncate if too long
    if (normalized.length > 64) {
      normalized = normalized.substring(0, 64);
    }

    // Ensure it's not empty
    if (normalized.isEmpty) {
      normalized = 'unnamed_tool';
    }

    return normalized;
  }

  /// Generate example tool schema for testing
  static ToolSchema generateExampleTool({
    String name = 'example_tool',
    String? description,
    Map<String, dynamic>? parameters,
  }) {
    return ToolSchema(
      name: name,
      description: description ?? 'An example tool for testing',
      parameters: parameters != null
          ? ParametersSchema.fromJson(parameters)
          : ParametersSchema.fromJson({
              'type': 'object',
              'properties': {
                'input': {
                  'type': 'string',
                  'description': 'Input parameter',
                },
              },
              'required': ['input'],
            }),
    );
  }
}

/// Utility functions for tool conversion
class ToolConversionUtils {
  /// Check if a tool schema is valid for OpenAI
  static bool isValidForOpenAI(ToolSchema tool) {
    try {
      final converter = OpenAIToolConverter();
      converter._validateToolSchema(tool);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Get tool validation errors
  static List<String> getValidationErrors(ToolSchema tool) {
    final errors = <String>[];

    try {
      final converter = OpenAIToolConverter();
      converter._validateToolSchema(tool);
    } catch (e) {
      if (e is LLMBadRequestException) {
        errors.add(e.message);
      } else {
        errors.add(e.toString());
      }
    }

    return errors;
  }

  /// Estimate token count for tools
  static int estimateToolTokens(List<ToolSchema> tools) {
    int totalTokens = 0;

    for (final tool in tools) {
      // Name tokens
      totalTokens += (tool.name.length / 4).ceil();

      // Description tokens
      if (tool.description != null) {
        totalTokens += (tool.description!.length / 4).ceil();
      }

      // Parameters tokens
      if (tool.parameters != null) {
        final parametersJson = jsonEncode(tool.parameters);
        totalTokens += (parametersJson.length / 4).ceil();
      }

      // OpenAI overhead per tool
      totalTokens += 10;
    }

    return totalTokens;
  }

  /// Convert tool list to JSON for debugging
  static String toolsToJson(List<Map<String, dynamic>> openAITools) {
    return const JsonEncoder.withIndent('  ').convert(openAITools);
  }

  /// Create tool from function signature (simplified)
  static ToolSchema createToolFromSignature(
    String name,
    String description,
    Map<String, String> parameters, // parameter name -> type
  ) {
    final properties = <String, dynamic>{};
    final required = <String>[];

    for (final entry in parameters.entries) {
      properties[entry.key] = {
        'type': entry.value,
        'description': 'Parameter ${entry.key}',
      };
      required.add(entry.key);
    }

    return ToolSchema(
      name: name,
      description: description,
      parameters: ParametersSchema.fromJson({
        'type': 'object',
        'properties': properties,
        'required': required,
      }),
    );
  }
}
