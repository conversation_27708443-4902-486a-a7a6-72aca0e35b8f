/// OpenAI integration for autogen-dart LLM module
///
/// This library provides comprehensive support for OpenAI's GPT models
/// including:
/// - Full message format conversion
/// - Function/tool calling support
/// - Token counting with tiktoken
/// - Error handling
/// - Model information and capabilities
///
/// Example usage:
/// ```dart
/// import 'package:llm/src/clients/clients.dart';
///
/// final client = ClientFactory.createOpenAI(
///   model: 'gpt-4',
///   apiKey: 'your-api-key',
/// );
///
/// final response = await client.createChatCompletion(
///   messages: [
///     UserMessage(text: 'Hello, GPT!'),
///   ],
/// );
///
/// print(response.content.text);
/// ```

// Message and tool conversion
export 'message_transformer.dart';
// Error handling provided by unified exceptions system
// Token counting
export 'token_counter.dart';
export 'tool_converter.dart';
