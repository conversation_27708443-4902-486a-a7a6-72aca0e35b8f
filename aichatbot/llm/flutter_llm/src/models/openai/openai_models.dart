import '../model_enums.dart';
import '../model_types.dart';

/// Get all built-in OpenAI model definitions
List<ModelInfo> getOpenAIBuiltinModels() {

  return [
    // GPT-4o models
    ModelInfo(
      name: 'gpt-4o',
      family: 'gpt-4o',
      description: 'GPT-4 Omni - most advanced multimodal model',
      capabilities: {
        ModelCapability.textGeneration,
        ModelCapability.imageInput,
        ModelCapability.functionCalling,
        ModelCapability.streaming,
        ModelCapability.systemMessages,
      },
      inputTokens: 0.005, // per 1K tokens
      outputTokens: 0.015,
      currency: Currency.usd,
      pricingTier: PricingTier.premium,
      maxTokens: 128000,
      maxInputTokens: 128000,
      maxOutputTokens: 4096,
      supportedLanguages: {
        SupportedLanguage.english,
        SupportedLanguage.chineseSimplified,
        SupportedLanguage.japanese,
        SupportedLanguage.korean,
        SupportedLanguage.french,
        SupportedLanguage.german,
        SupportedLanguage.spanish,
      },
    ),

    ModelInfo(
      name: 'gpt-4o-mini',
      family: 'gpt-4o',
      description: 'GPT-4 Omni Mini - cost-effective multimodal model',
      capabilities: {
        ModelCapability.textGeneration,
        ModelCapability.imageInput,
        ModelCapability.functionCalling,
        ModelCapability.streaming,
        ModelCapability.systemMessages,
      },
      inputTokens: 0.00015,
      outputTokens: 0.0006,
      currency: Currency.usd,
      pricingTier: PricingTier.standard,
      maxTokens: 128000,
      maxInputTokens: 128000,
      maxOutputTokens: 16384,
      supportedLanguages: {
        SupportedLanguage.english,
        SupportedLanguage.chineseSimplified,
        SupportedLanguage.japanese,
        SupportedLanguage.korean,
        SupportedLanguage.french,
        SupportedLanguage.german,
        SupportedLanguage.spanish,
      },
    ),

    // GPT-4 Turbo models
    ModelInfo(
      name: 'gpt-4-turbo',
      family: 'gpt-4-turbo',
      description: 'GPT-4 Turbo with vision capabilities',
      capabilities: {
        ModelCapability.textGeneration,
        ModelCapability.imageInput,
        ModelCapability.functionCalling,
        ModelCapability.streaming,
        ModelCapability.systemMessages,
      },
      inputTokens: 0.01,
      outputTokens: 0.03,
      currency: Currency.usd,
      pricingTier: PricingTier.premium,
      maxTokens: 128000,
      maxInputTokens: 128000,
      maxOutputTokens: 4096,
      supportedLanguages: {
        SupportedLanguage.english,
        SupportedLanguage.chineseSimplified,
        SupportedLanguage.japanese,
        SupportedLanguage.korean,
        SupportedLanguage.french,
        SupportedLanguage.german,
      },
    ),

    ModelInfo(
      name: 'gpt-4-turbo-preview',
      family: 'gpt-4-turbo',
      description: 'GPT-4 Turbo Preview - text only version',
      capabilities: {
        ModelCapability.textGeneration,
        ModelCapability.functionCalling,
        ModelCapability.streaming,
        ModelCapability.systemMessages,
      },
      inputTokens: 0.01,
      outputTokens: 0.03,
      currency: Currency.usd,
      pricingTier: PricingTier.premium,
      maxTokens: 128000,
      maxInputTokens: 128000,
      maxOutputTokens: 4096,
      supportedLanguages: {
        SupportedLanguage.english,
        SupportedLanguage.chineseSimplified,
        SupportedLanguage.japanese,
        SupportedLanguage.korean,
        SupportedLanguage.french,
        SupportedLanguage.german,
      },
    ),

    // GPT-4 models
    ModelInfo(
      name: 'gpt-4',
      family: 'gpt-4',
      description: 'GPT-4 - highly capable language model',
      capabilities: {
        ModelCapability.textGeneration,
        ModelCapability.functionCalling,
        ModelCapability.streaming,
        ModelCapability.systemMessages,
      },
      inputTokens: 0.03,
      outputTokens: 0.06,
      currency: Currency.usd,
      pricingTier: PricingTier.premium,
      maxTokens: 8192,
      maxInputTokens: 8192,
      maxOutputTokens: 4096,
      supportedLanguages: {
        SupportedLanguage.english,
        SupportedLanguage.chineseSimplified,
        SupportedLanguage.japanese,
        SupportedLanguage.korean,
        SupportedLanguage.french,
        SupportedLanguage.german,
      },
    ),

    ModelInfo(
      name: 'gpt-4-32k',
      family: 'gpt-4',
      description: 'GPT-4 with extended 32K context window',
      capabilities: {
        ModelCapability.textGeneration,
        ModelCapability.functionCalling,
        ModelCapability.streaming,
        ModelCapability.systemMessages,
      },
      inputTokens: 0.06,
      outputTokens: 0.12,
      currency: Currency.usd,
      pricingTier: PricingTier.enterprise,
      maxTokens: 32768,
      maxInputTokens: 32768,
      maxOutputTokens: 4096,
      supportedLanguages: {
        SupportedLanguage.english,
        SupportedLanguage.chineseSimplified,
        SupportedLanguage.japanese,
        SupportedLanguage.korean,
        SupportedLanguage.french,
        SupportedLanguage.german,
      },
    ),

    // GPT-3.5 Turbo models
    ModelInfo(
      name: 'gpt-3.5-turbo',
      family: 'gpt-3.5-turbo',
      description: 'GPT-3.5 Turbo - fast and cost-effective',
      capabilities: {
        ModelCapability.textGeneration,
        ModelCapability.functionCalling,
        ModelCapability.streaming,
        ModelCapability.systemMessages,
      },
      inputTokens: 0.0015,
      outputTokens: 0.002,
      currency: Currency.usd,
      pricingTier: PricingTier.standard,
      maxTokens: 16385,
      maxInputTokens: 16385,
      maxOutputTokens: 4096,
      supportedLanguages: {
        SupportedLanguage.english,
        SupportedLanguage.chineseSimplified,
        SupportedLanguage.japanese,
        SupportedLanguage.korean,
        SupportedLanguage.french,
        SupportedLanguage.german,
        SupportedLanguage.spanish,
      },
    ),

    ModelInfo(
      name: 'gpt-3.5-turbo-16k',
      family: 'gpt-3.5-turbo',
      description: 'GPT-3.5 Turbo with extended context window',
      capabilities: {
        ModelCapability.textGeneration,
        ModelCapability.functionCalling,
        ModelCapability.streaming,
        ModelCapability.systemMessages,
      },
      inputTokens: 0.003,
      outputTokens: 0.004,
      currency: Currency.usd,
      pricingTier: PricingTier.standard,
      maxTokens: 16385,
      maxInputTokens: 16385,
      maxOutputTokens: 4096,
      supportedLanguages: {
        SupportedLanguage.english,
        SupportedLanguage.chineseSimplified,
        SupportedLanguage.japanese,
        SupportedLanguage.korean,
        SupportedLanguage.french,
        SupportedLanguage.german,
        SupportedLanguage.spanish,
      },
    ),

    // DALL-E models
    ModelInfo(
      name: 'dall-e-3',
      family: 'dall-e',
      description: 'DALL-E 3 - advanced image generation model',
      capabilities: {
        ModelCapability.imageGeneration,
      },
      totalTokens: 0.040, // Base pricing per image
      currency: Currency.usd,
      pricingTier: PricingTier.premium,
      maxTokens: 4000, // For prompts
      supportedLanguages: {
        SupportedLanguage.english,
        SupportedLanguage.chineseSimplified,
        SupportedLanguage.japanese,
        SupportedLanguage.korean,
        SupportedLanguage.french,
        SupportedLanguage.german,
        SupportedLanguage.spanish,
      },
    ),

    ModelInfo(
      name: 'dall-e-2',
      family: 'dall-e',
      description: 'DALL-E 2 - cost-effective image generation',
      capabilities: {
        ModelCapability.imageGeneration,
      },
      totalTokens: 0.020, // Base pricing per image
      currency: Currency.usd,
      pricingTier: PricingTier.standard,
      maxTokens: 1000,
      supportedLanguages: {
        SupportedLanguage.english,
        SupportedLanguage.chineseSimplified,
        SupportedLanguage.japanese,
        SupportedLanguage.korean,
        SupportedLanguage.french,
        SupportedLanguage.german,
        SupportedLanguage.spanish,
      },
    ),

    // Embedding models
    ModelInfo(
      name: 'text-embedding-3-large',
      family: 'embedding',
      description: 'Text Embedding 3 Large - high-quality embeddings',
      capabilities: {
        ModelCapability.embeddings,
      },
      inputTokens: 0.00013, // per 1K tokens
      currency: Currency.usd,
      pricingTier: PricingTier.standard,
      maxTokens: 8191,
      supportedLanguages: {
        SupportedLanguage.english,
        SupportedLanguage.chineseSimplified,
        SupportedLanguage.japanese,
        SupportedLanguage.korean,
        SupportedLanguage.french,
        SupportedLanguage.german,
        SupportedLanguage.spanish,
        SupportedLanguage.russian,
        SupportedLanguage.arabic,
        SupportedLanguage.portuguese,
        SupportedLanguage.italian,
      },
    ),

    ModelInfo(
      name: 'text-embedding-3-small',
      family: 'embedding',
      description: 'Text Embedding 3 Small - efficient embeddings',
      capabilities: {
        ModelCapability.embeddings,
      },
      inputTokens: 0.00002,
      currency: Currency.usd,
      pricingTier: PricingTier.standard,
      maxTokens: 8191,
      supportedLanguages: {
        SupportedLanguage.english,
        SupportedLanguage.chineseSimplified,
        SupportedLanguage.japanese,
        SupportedLanguage.korean,
        SupportedLanguage.french,
        SupportedLanguage.german,
        SupportedLanguage.spanish,
        SupportedLanguage.russian,
        SupportedLanguage.arabic,
        SupportedLanguage.portuguese,
        SupportedLanguage.italian,
      },
    ),

    ModelInfo(
      name: 'text-embedding-ada-002',
      family: 'embedding',
      description: 'Text Embedding Ada 002 - legacy embedding model',
      capabilities: {
        ModelCapability.embeddings,
      },
      inputTokens: 0.0001,
      currency: Currency.usd,
      pricingTier: PricingTier.standard,
      maxTokens: 8191,
      supportedLanguages: {
        SupportedLanguage.english,
        SupportedLanguage.chineseSimplified,
        SupportedLanguage.japanese,
        SupportedLanguage.korean,
        SupportedLanguage.french,
        SupportedLanguage.german,
        SupportedLanguage.spanish,
      },
    ),

    // Whisper model
    ModelInfo(
      name: 'whisper-1',
      family: 'whisper',
      description: 'Whisper - speech-to-text model',
      capabilities: {
        ModelCapability.audioInput,
      },
      totalTokens: 0.006, // per minute
      currency: Currency.usd,
      pricingTier: PricingTier.standard,
      maxTokens: 448, // 25MB file limit
      supportedLanguages: {
        SupportedLanguage.english,
        SupportedLanguage.chineseSimplified,
        SupportedLanguage.japanese,
        SupportedLanguage.korean,
        SupportedLanguage.french,
        SupportedLanguage.german,
        SupportedLanguage.spanish,
        SupportedLanguage.russian,
        SupportedLanguage.arabic,
        SupportedLanguage.portuguese,
        SupportedLanguage.italian,
        SupportedLanguage.hindi,
        SupportedLanguage.turkish,
        SupportedLanguage.polish,
        SupportedLanguage.dutch,
      },
    ),

    // TTS models
    ModelInfo(
      name: 'tts-1',
      family: 'tts',
      description: 'TTS 1 - text-to-speech model',
      capabilities: {
        ModelCapability.audioGeneration,
      },
      totalTokens: 0.000015, // per character
      currency: Currency.usd,
      pricingTier: PricingTier.standard,
      maxTokens: 4096,
      supportedLanguages: {
        SupportedLanguage.english,
        SupportedLanguage.chineseSimplified,
        SupportedLanguage.japanese,
        SupportedLanguage.korean,
        SupportedLanguage.french,
        SupportedLanguage.german,
        SupportedLanguage.spanish,
        SupportedLanguage.portuguese,
        SupportedLanguage.italian,
      },
    ),

    ModelInfo(
      name: 'tts-1-hd',
      family: 'tts',
      description: 'TTS 1 HD - high-definition text-to-speech',
      capabilities: {
        ModelCapability.audioGeneration,
      },
      totalTokens: 0.00003, // per character
      currency: Currency.usd,
      pricingTier: PricingTier.premium,
      maxTokens: 4096,
      supportedLanguages: {
        SupportedLanguage.english,
        SupportedLanguage.chineseSimplified,
        SupportedLanguage.japanese,
        SupportedLanguage.korean,
        SupportedLanguage.french,
        SupportedLanguage.german,
        SupportedLanguage.spanish,
        SupportedLanguage.portuguese,
        SupportedLanguage.italian,
      },
    ),
  ];
}

/// OpenAI 模型家族工具类
class OpenAIModelFamily {
  static const String gpt4o = 'gpt-4o';
  static const String gpt4Turbo = 'gpt-4-turbo';
  static const String gpt4 = 'gpt-4';
  static const String gpt35Turbo = 'gpt-3.5-turbo';
  static const String dalle = 'dall-e';
  static const String embedding = 'embedding';
  static const String whisper = 'whisper';
  static const String tts = 'tts';

  static const List<String> all = [
    gpt4o,
    gpt4Turbo,
    gpt4,
    gpt35Turbo,
    dalle,
    embedding,
    whisper,
    tts,
  ];

  /// 检查家族是否支持视觉能力
  static bool supportsVision(String family) {
    return [gpt4o, gpt4Turbo].contains(family);
  }

  /// 检查家族是否支持函数调用
  static bool supportsFunctionCalling(String family) {
    return [gpt4o, gpt4Turbo, gpt4, gpt35Turbo].contains(family);
  }

  /// 检查是否为对话模型
  static bool isChatModel(String family) {
    return [gpt4o, gpt4Turbo, gpt4, gpt35Turbo].contains(family);
  }

  /// 检查是否为多模态模型
  static bool isMultimodal(String family) {
    return [gpt4o, gpt4Turbo, dalle, whisper, tts].contains(family);
  }

  /// 获取家族中的最新模型名称
  static String? getLatestModelName(String family) {
    switch (family) {
      case gpt4o:
        return 'gpt-4o';
      case gpt4Turbo:
        return 'gpt-4-turbo';
      case gpt4:
        return 'gpt-4';
      case gpt35Turbo:
        return 'gpt-3.5-turbo';
      case dalle:
        return 'dall-e-3';
      case embedding:
        return 'text-embedding-3-large';
      case whisper:
        return 'whisper-1';
      case tts:
        return 'tts-1-hd';
      default:
        return null;
    }
  }

  /// 根据能力需求推荐模型家族
  static List<String> recommendFamilies({
    bool needsVision = false,
    bool needsFunctionCalling = false,
    bool needsAudioInput = false,
    bool needsAudioGeneration = false,
    bool needsImageGeneration = false,
    bool needsEmbeddings = false,
  }) {
    final recommended = <String>[];

    if (needsVision) {
      recommended.addAll([gpt4o, gpt4Turbo]);
    } else if (needsFunctionCalling) {
      recommended.addAll([gpt4o, gpt4Turbo, gpt4, gpt35Turbo]);
    } else {
      recommended.addAll([gpt35Turbo, gpt4]);
    }

    if (needsAudioInput) {
      recommended.add(whisper);
    }

    if (needsAudioGeneration) {
      recommended.add(tts);
    }

    if (needsImageGeneration) {
      recommended.add(dalle);
    }

    if (needsEmbeddings) {
      recommended.add(embedding);
    }

    return recommended.toSet().toList();
  }
}
