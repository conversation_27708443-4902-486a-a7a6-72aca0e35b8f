/// OpenAI-specific types and enumerations
///
/// Defines types used specifically for OpenAI API interactions

/// OpenAI model families
enum OpenAIModelFamily {
  gpt4o('gpt-4o'),
  gpt4('gpt-4'),
  gpt35('gpt-3.5'),
  davin<PERSON>('davinci'),
  curie('curie'),
  babbage('babbage'),
  ada('ada'),
  unknown('unknown');

  const OpenAIModelFamily(this.value);
  final String value;

  static OpenAIModelFamily fromString(String value) {
    for (final family in OpenAIModelFamily.values) {
      if (family.value == value || value.startsWith(family.value)) {
        return family;
      }
    }
    return OpenAIModelFamily.unknown;
  }
}

/// OpenAI API endpoints
enum OpenAIEndpoint {
  chatCompletions('/chat/completions'),
  completions('/completions'),
  edits('/edits'),
  images('/images'),
  embeddings('/embeddings'),
  files('/files'),
  fineTunes('/fine-tunes'),
  models('/models'),
  moderations('/moderations');

  const OpenAIEndpoint(this.path);
  final String path;
}

/// OpenAI response format types
enum OpenAIResponseFormat {
  text('text'),
  jsonObject('json_object'),
  jsonSchema('json_schema');

  const OpenAIResponseFormat(this.value);
  final String value;

  Map<String, dynamic> toJson() {
    switch (this) {
      case OpenAIResponseFormat.text:
        return {'type': 'text'};
      case OpenAIResponseFormat.jsonObject:
        return {'type': 'json_object'};
      case OpenAIResponseFormat.jsonSchema:
        return {'type': 'json_schema'};
    }
  }
}

/// OpenAI tool choice types
enum OpenAIToolChoice {
  auto('auto'),
  none('none'),
  required('required');

  const OpenAIToolChoice(this.value);
  final String value;

  Map<String, dynamic> toJson() {
    return {'type': value};
  }
}

/// OpenAI finish reasons
enum OpenAIFinishReason {
  stop('stop'),
  length('length'),
  functionCall('function_call'),
  toolCalls('tool_calls'),
  contentFilter('content_filter'),
  unknown('unknown');

  const OpenAIFinishReason(this.value);
  final String value;

  static OpenAIFinishReason fromString(String? value) {
    if (value == null) return OpenAIFinishReason.unknown;

    for (final reason in OpenAIFinishReason.values) {
      if (reason.value == value) {
        return reason;
      }
    }
    return OpenAIFinishReason.unknown;
  }
}

/// OpenAI message roles
enum OpenAIRole {
  system('system'),
  user('user'),
  assistant('assistant'),
  tool('tool'),
  function('function'); // Deprecated but still supported

  const OpenAIRole(this.value);
  final String value;

  static OpenAIRole fromString(String value) {
    for (final role in OpenAIRole.values) {
      if (role.value == value) {
        return role;
      }
    }
    throw ArgumentError('Unknown OpenAI role: $value');
  }
}

/// OpenAI content types
enum OpenAIContentType {
  text('text'),
  imageUrl('image_url');

  const OpenAIContentType(this.value);
  final String value;
}

/// OpenAI image detail levels
enum OpenAIImageDetail {
  low('low'),
  high('high'),
  auto('auto');

  const OpenAIImageDetail(this.value);
  final String value;
}

/// OpenAI streaming event types
enum OpenAIStreamEventType {
  chunk('chunk'),
  error('error'),
  done('done');

  const OpenAIStreamEventType(this.value);
  final String value;

  static OpenAIStreamEventType fromString(String value) {
    for (final type in OpenAIStreamEventType.values) {
      if (type.value == value) {
        return type;
      }
    }
    return OpenAIStreamEventType.chunk;
  }
}

/// OpenAI API configuration
class OpenAIConfig {
  final String apiKey;
  final String baseUrl;
  final Map<String, String> headers;
  final Duration timeout;
  final int maxRetries;
  final Duration retryDelay;
  final bool enableTelemetry;

  const OpenAIConfig({
    required this.apiKey,
    this.baseUrl = 'https://api.openai.com/v1',
    this.headers = const {},
    this.timeout = const Duration(seconds: 60),
    this.maxRetries = 3,
    this.retryDelay = const Duration(seconds: 1),
    this.enableTelemetry = true,
  });

  OpenAIConfig copyWith({
    String? apiKey,
    String? baseUrl,
    Map<String, String>? headers,
    Duration? timeout,
    int? maxRetries,
    Duration? retryDelay,
    bool? enableTelemetry,
  }) {
    return OpenAIConfig(
      apiKey: apiKey ?? this.apiKey,
      baseUrl: baseUrl ?? this.baseUrl,
      headers: headers ?? this.headers,
      timeout: timeout ?? this.timeout,
      maxRetries: maxRetries ?? this.maxRetries,
      retryDelay: retryDelay ?? this.retryDelay,
      enableTelemetry: enableTelemetry ?? this.enableTelemetry,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'baseUrl': baseUrl,
      'headers': headers,
      'timeout': timeout.inMilliseconds,
      'maxRetries': maxRetries,
      'retryDelay': retryDelay.inMilliseconds,
      'enableTelemetry': enableTelemetry,
    };
  }
}

/// Azure OpenAI specific configuration
class AzureOpenAIConfig extends OpenAIConfig {
  final String endpoint;
  final String deployment;
  final String apiVersion;

  const AzureOpenAIConfig({
    required super.apiKey,
    required this.endpoint,
    required this.deployment,
    required this.apiVersion,
    super.headers,
    super.timeout,
    super.maxRetries,
    super.retryDelay,
    super.enableTelemetry,
  }) : super(baseUrl: endpoint);

  String get deploymentUrl => '$endpoint/openai/deployments/$deployment';

  @override
  AzureOpenAIConfig copyWith({
    String? apiKey,
    String? baseUrl,
    Map<String, String>? headers,
    Duration? timeout,
    int? maxRetries,
    Duration? retryDelay,
    bool? enableTelemetry,
    String? endpoint,
    String? deployment,
    String? apiVersion,
  }) {
    return AzureOpenAIConfig(
      apiKey: apiKey ?? this.apiKey,
      endpoint: endpoint ?? this.endpoint,
      deployment: deployment ?? this.deployment,
      apiVersion: apiVersion ?? this.apiVersion,
      headers: headers ?? this.headers,
      timeout: timeout ?? this.timeout,
      maxRetries: maxRetries ?? this.maxRetries,
      retryDelay: retryDelay ?? this.retryDelay,
      enableTelemetry: enableTelemetry ?? this.enableTelemetry,
    );
  }

  @override
  Map<String, dynamic> toJson() {
    final json = super.toJson();
    json.addAll({
      'endpoint': endpoint,
      'deployment': deployment,
      'apiVersion': apiVersion,
      'deploymentUrl': deploymentUrl,
    });
    return json;
  }
}

/// OpenAI request parameters
class OpenAIChatCompletionParams {
  final String model;
  final List<Map<String, dynamic>> messages;
  final double? temperature;
  final double? topP;
  final int? n;
  final bool stream;
  final List<String>? stop;
  final int? maxTokens;
  final double? presencePenalty;
  final double? frequencyPenalty;
  final Map<String, int>? logitBias;
  final String? user;
  final List<Map<String, dynamic>>? tools;
  final dynamic toolChoice;
  final Map<String, dynamic>? responseFormat;
  final int? seed;
  final bool? logprobs;
  final int? topLogprobs;

  const OpenAIChatCompletionParams({
    required this.model,
    required this.messages,
    this.temperature,
    this.topP,
    this.n,
    this.stream = false,
    this.stop,
    this.maxTokens,
    this.presencePenalty,
    this.frequencyPenalty,
    this.logitBias,
    this.user,
    this.tools,
    this.toolChoice,
    this.responseFormat,
    this.seed,
    this.logprobs,
    this.topLogprobs,
  });

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{
      'model': model,
      'messages': messages,
      'stream': stream,
    };

    if (temperature != null) json['temperature'] = temperature;
    if (topP != null) json['top_p'] = topP;
    if (n != null) json['n'] = n;
    if (stop != null) json['stop'] = stop;
    if (maxTokens != null) json['max_tokens'] = maxTokens;
    if (presencePenalty != null) json['presence_penalty'] = presencePenalty;
    if (frequencyPenalty != null) json['frequency_penalty'] = frequencyPenalty;
    if (logitBias != null) json['logit_bias'] = logitBias;
    if (user != null) json['user'] = user;
    if (tools != null) json['tools'] = tools;
    if (toolChoice != null) json['tool_choice'] = toolChoice;
    if (responseFormat != null) json['response_format'] = responseFormat;
    if (seed != null) json['seed'] = seed;
    if (logprobs != null) json['logprobs'] = logprobs;
    if (topLogprobs != null) json['top_logprobs'] = topLogprobs;

    return json;
  }

  OpenAIChatCompletionParams copyWith({
    String? model,
    List<Map<String, dynamic>>? messages,
    double? temperature,
    double? topP,
    int? n,
    bool? stream,
    List<String>? stop,
    int? maxTokens,
    double? presencePenalty,
    double? frequencyPenalty,
    Map<String, int>? logitBias,
    String? user,
    List<Map<String, dynamic>>? tools,
    dynamic toolChoice,
    Map<String, dynamic>? responseFormat,
    int? seed,
    bool? logprobs,
    int? topLogprobs,
  }) {
    return OpenAIChatCompletionParams(
      model: model ?? this.model,
      messages: messages ?? this.messages,
      temperature: temperature ?? this.temperature,
      topP: topP ?? this.topP,
      n: n ?? this.n,
      stream: stream ?? this.stream,
      stop: stop ?? this.stop,
      maxTokens: maxTokens ?? this.maxTokens,
      presencePenalty: presencePenalty ?? this.presencePenalty,
      frequencyPenalty: frequencyPenalty ?? this.frequencyPenalty,
      logitBias: logitBias ?? this.logitBias,
      user: user ?? this.user,
      tools: tools ?? this.tools,
      toolChoice: toolChoice ?? this.toolChoice,
      responseFormat: responseFormat ?? this.responseFormat,
      seed: seed ?? this.seed,
      logprobs: logprobs ?? this.logprobs,
      topLogprobs: topLogprobs ?? this.topLogprobs,
    );
  }
}

/// OpenAI usage information
class OpenAIUsage {
  final int promptTokens;
  final int completionTokens;
  final int totalTokens;
  final Map<String, dynamic>? promptTokensDetails;
  final Map<String, dynamic>? completionTokensDetails;

  const OpenAIUsage({
    required this.promptTokens,
    required this.completionTokens,
    required this.totalTokens,
    this.promptTokensDetails,
    this.completionTokensDetails,
  });

  factory OpenAIUsage.fromJson(Map<String, dynamic> json) {
    return OpenAIUsage(
      promptTokens: json['prompt_tokens'] as int,
      completionTokens: json['completion_tokens'] as int,
      totalTokens: json['total_tokens'] as int,
      promptTokensDetails:
          json['prompt_tokens_details'] as Map<String, dynamic>?,
      completionTokensDetails:
          json['completion_tokens_details'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'prompt_tokens': promptTokens,
      'completion_tokens': completionTokens,
      'total_tokens': totalTokens,
      if (promptTokensDetails != null)
        'prompt_tokens_details': promptTokensDetails,
      if (completionTokensDetails != null)
        'completion_tokens_details': completionTokensDetails,
    };
  }
}

/// OpenAI choice object
class OpenAIChoice {
  final int index;
  final Map<String, dynamic> message;
  final String? finishReason;
  final Map<String, dynamic>? logprobs;

  const OpenAIChoice({
    required this.index,
    required this.message,
    this.finishReason,
    this.logprobs,
  });

  factory OpenAIChoice.fromJson(Map<String, dynamic> json) {
    return OpenAIChoice(
      index: json['index'] as int,
      message: json['message'] as Map<String, dynamic>,
      finishReason: json['finish_reason'] as String?,
      logprobs: json['logprobs'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'index': index,
      'message': message,
      'finish_reason': finishReason,
      'logprobs': logprobs,
    };
  }
}

/// OpenAI delta object for streaming
class OpenAIDelta {
  final String? role;
  final String? content;
  final List<Map<String, dynamic>>? toolCalls;

  const OpenAIDelta({
    this.role,
    this.content,
    this.toolCalls,
  });

  factory OpenAIDelta.fromJson(Map<String, dynamic> json) {
    return OpenAIDelta(
      role: json['role'] as String?,
      content: json['content'] as String?,
      toolCalls: (json['tool_calls'] as List<dynamic>?)
          ?.map((e) => e as Map<String, dynamic>)
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (role != null) 'role': role,
      if (content != null) 'content': content,
      if (toolCalls != null) 'tool_calls': toolCalls,
    };
  }
}

/// OpenAI streaming choice
class OpenAIStreamingChoice {
  final int index;
  final OpenAIDelta delta;
  final String? finishReason;
  final Map<String, dynamic>? logprobs;

  const OpenAIStreamingChoice({
    required this.index,
    required this.delta,
    this.finishReason,
    this.logprobs,
  });

  factory OpenAIStreamingChoice.fromJson(Map<String, dynamic> json) {
    return OpenAIStreamingChoice(
      index: json['index'] as int,
      delta: OpenAIDelta.fromJson(json['delta'] as Map<String, dynamic>),
      finishReason: json['finish_reason'] as String?,
      logprobs: json['logprobs'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'index': index,
      'delta': delta.toJson(),
      'finish_reason': finishReason,
      'logprobs': logprobs,
    };
  }
}

/// OpenAI model information
class OpenAIModel {
  final String id;
  final String object;
  final int created;
  final String ownedBy;
  final List<Map<String, dynamic>>? permission;
  final String? root;
  final String? parent;

  const OpenAIModel({
    required this.id,
    required this.object,
    required this.created,
    required this.ownedBy,
    this.permission,
    this.root,
    this.parent,
  });

  factory OpenAIModel.fromJson(Map<String, dynamic> json) {
    return OpenAIModel(
      id: json['id'] as String,
      object: json['object'] as String,
      created: json['created'] as int,
      ownedBy: json['owned_by'] as String,
      permission: (json['permission'] as List<dynamic>?)
          ?.map((e) => e as Map<String, dynamic>)
          .toList(),
      root: json['root'] as String?,
      parent: json['parent'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'object': object,
      'created': created,
      'owned_by': ownedBy,
      if (permission != null) 'permission': permission,
      if (root != null) 'root': root,
      if (parent != null) 'parent': parent,
    };
  }
}

/// Utility functions for OpenAI types
class OpenAITypeUtils {
  /// Validate OpenAI model name format
  static bool isValidModelName(String model) {
    // OpenAI model names typically follow specific patterns
    final patterns = [
      RegExp(r'^gpt-[34](\.[05])?(-turbo)?(-\d{4}-\d{2}-\d{2})?$'),
      RegExp(r'^gpt-4o(-mini)?(-\d{4}-\d{2}-\d{2})?$'),
      RegExp(r'^text-davinci-\d{3}$'),
      RegExp(r'^code-davinci-\d{3}$'),
      RegExp(r'^text-curie-\d{3}$'),
      RegExp(r'^text-babbage-\d{3}$'),
      RegExp(r'^text-ada-\d{3}$'),
    ];

    return patterns.any((pattern) => pattern.hasMatch(model));
  }

  /// Get model family from model name
  static OpenAIModelFamily getModelFamily(String model) {
    if (model.startsWith('gpt-4o')) {
      return OpenAIModelFamily.gpt4o;
    } else if (model.startsWith('gpt-4')) {
      return OpenAIModelFamily.gpt4;
    } else if (model.startsWith('gpt-3.5') || model.startsWith('gpt-35')) {
      return OpenAIModelFamily.gpt35;
    } else if (model.contains('davinci')) {
      return OpenAIModelFamily.davinci;
    } else if (model.contains('curie')) {
      return OpenAIModelFamily.curie;
    } else if (model.contains('babbage')) {
      return OpenAIModelFamily.babbage;
    } else if (model.contains('ada')) {
      return OpenAIModelFamily.ada;
    }

    return OpenAIModelFamily.unknown;
  }

  /// Check if model supports specific feature
  static bool modelSupportsFeature(String model, String feature) {
    final family = getModelFamily(model);

    switch (feature.toLowerCase()) {
      case 'function_calling':
      case 'tools':
        return family == OpenAIModelFamily.gpt4o ||
            family == OpenAIModelFamily.gpt4 ||
            family == OpenAIModelFamily.gpt35;

      case 'vision':
      case 'image_input':
        return model.contains('vision') ||
            family == OpenAIModelFamily.gpt4o ||
            (family == OpenAIModelFamily.gpt4 && model.contains('turbo'));

      case 'json_mode':
        return family == OpenAIModelFamily.gpt4o ||
            family == OpenAIModelFamily.gpt4 ||
            family == OpenAIModelFamily.gpt35;

      default:
        return false;
    }
  }

  /// Normalize finish reason
  static OpenAIFinishReason normalizeFinishReason(String? reason) {
    return OpenAIFinishReason.fromString(reason);
  }

  /// Convert tool choice to OpenAI format
  static dynamic convertToolChoice(dynamic toolChoice) {
    if (toolChoice == null) return null;

    if (toolChoice is String) {
      switch (toolChoice.toLowerCase()) {
        case 'auto':
          return 'auto';
        case 'none':
          return 'none';
        case 'required':
        case 'any':
          return 'required';
        default:
          return {
            'type': 'function',
            'function': {'name': toolChoice}
          };
      }
    }

    return toolChoice;
  }

  /// Estimate token count (basic approximation)
  static int estimateTokenCount(String text) {
    // Very basic estimation: ~4 characters per token
    return (text.length / 4).ceil();
  }

  /// Check if response format is valid
  static bool isValidResponseFormat(Map<String, dynamic>? format) {
    if (format == null) return true;

    final type = format['type'] as String?;
    if (type == null) return false;

    switch (type) {
      case 'text':
        return true;
      case 'json_object':
        return true;
      case 'json_schema':
        return format.containsKey('json_schema');
      default:
        return false;
    }
  }
}
