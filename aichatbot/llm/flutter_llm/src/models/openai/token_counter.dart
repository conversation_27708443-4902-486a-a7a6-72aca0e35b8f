import 'dart:convert';
import 'dart:math' as math;

import '../../image.dart';
import '../../model_context/chat_completion_context.dart';
import '../tool_schema.dart';

/// Abstract base class for token counting implementations
abstract class TokenCounter {
  /// Count tokens for a list of messages
  int countTokens(List<LLMMessage> messages, String model);

  /// Count tokens for tools
  int countToolTokens(List<ToolSchema> tools, String model);

  /// Get the model's token limit
  int getTokenLimit(String model);
}

/// Factory for creating appropriate token counters
class TokenCounterFactory {
  static TokenCounter create(String model) {
    // For now, we'll use approximation
    // In the future, this could return TiktokenCounter for exact counting
    return ApproximateTokenCounter();
  }
}

/// Approximate token counter based on character count and heuristics
///
/// Provides reasonably accurate token estimates without requiring external dependencies
class ApproximateTokenCounter extends TokenCounter {
  // Model-specific token limits
  static const Map<String, int> _tokenLimits = {
    'gpt-4o': 128000,
    'gpt-4o-mini': 128000,
    'gpt-4-turbo': 128000,
    'gpt-4': 8192,
    'gpt-3.5-turbo': 16385,
    'gpt-3.5-turbo-16k': 16385,
    'claude-3-haiku': 200000,
    'claude-3-sonnet': 200000,
    'claude-3-opus': 200000,
    'claude-3-5-haiku': 200000,
    'claude-3-5-sonnet': 200000,
    'gemini-1.5-pro': 2000000,
    'gemini-1.5-flash': 1000000,
  };

  // Model-specific character-to-token ratios
  static const Map<String, double> _charToTokenRatios = {
    'gpt-4o': 3.8,
    'gpt-4o-mini': 3.8,
    'gpt-4-turbo': 3.8,
    'gpt-4': 3.8,
    'gpt-3.5-turbo': 3.8,
    'claude-3-haiku': 4.2,
    'claude-3-sonnet': 4.2,
    'claude-3-opus': 4.2,
    'claude-3-5-haiku': 4.2,
    'claude-3-5-sonnet': 4.2,
    'gemini-1.5-pro': 4.0,
    'gemini-1.5-flash': 4.0,
  };

  @override
  int countTokens(List<LLMMessage> messages, String model) {
    final ratio = _getCharToTokenRatio(model);
    int totalTokens = 0;

    for (final message in messages) {
      totalTokens += _countMessageTokens(message, model, ratio);
    }

    // Add OpenAI message formatting overhead
    totalTokens += _getMessageOverhead(messages.length, model);

    return totalTokens;
  }

  @override
  int countToolTokens(List<ToolSchema> tools, String model) {
    if (tools.isEmpty) return 0;

    final ratio = _getCharToTokenRatio(model);
    int totalTokens = 0;

    for (final tool in tools) {
      totalTokens += _countToolTokens(tool, model, ratio);
    }

    // Add tool formatting overhead
    totalTokens += _getToolOverhead(tools.length, model);

    return totalTokens;
  }

  @override
  int getTokenLimit(String model) {
    // Check exact match first
    if (_tokenLimits.containsKey(model)) {
      return _tokenLimits[model]!;
    }

    // Check partial matches for model families
    for (final entry in _tokenLimits.entries) {
      if (model.toLowerCase().startsWith(entry.key.toLowerCase())) {
        return entry.value;
      }
    }

    // Default fallback
    return 4096;
  }

  /// Count tokens for a single message
  int _countMessageTokens(LLMMessage message, String model, double ratio) {
    int tokens = 0;

    if (message is SystemMessage) {
      tokens += (message.content.length / ratio).ceil();
    } else if (message is UserMessage) {
      if (message.text != null) {
        tokens += (message.text!.length / ratio).ceil();
      }

      // Add vision tokens for images
      for (final image in message.images) {
        tokens += _calculateVisionTokens(image, model);
      }
    } else if (message is AssistantMessage) {
      if (message.text != null) {
        tokens += (message.text!.length / ratio).ceil();
      }

      // Add tokens for function calls
      for (final call in message.functionCalls) {
        tokens += _countFunctionCallTokens(call, ratio);
      }
    } else if (message is FunctionExecutionResultMessage) {
      for (final result in message.content) {
        final resultStr = _formatFunctionResult(result.content);
        tokens += (resultStr.length / 4).ceil();
        // Add some overhead for function result formatting
        tokens += 10;
      }
    }

    return tokens;
  }

  /// Count tokens for a single tool
  int _countToolTokens(ToolSchema tool, String model, double ratio) {
    int tokens = 0;

    // Tool name
    tokens += (tool.name.length / ratio).ceil();

    // Tool description
    if (tool.description != null) {
      tokens += (tool.description!.length / ratio).ceil();
    }

    // Tool parameters
    if (tool.parameters != null) {
      final parametersJson = jsonEncode(tool.parameters);
      tokens += (parametersJson.length / ratio).ceil();
    }

    return tokens;
  }

  /// Count tokens for a function call
  int _countFunctionCallTokens(FunctionCall call, double ratio) {
    int tokens = 0;

    tokens += (call.name.length / ratio).ceil();
    tokens += (call.arguments.length / ratio).ceil();

    return tokens + 5; // Overhead for function call structure
  }

  /// Calculate tokens for vision/image content
  int _calculateVisionTokens(Image image, String model) {
    // Simplified OpenAI vision token calculation
    // This is an approximation of the actual algorithm

    const int baseTokens = 85;
    const int tokensPerTile = 170;
    const int tileSize = 512;
    const int maxLongEdge = 2048;
    const int maxShortEdge = 768;

    try {
      // Get image dimensions (this would need actual image processing)
      // For now, we'll use a reasonable default
      int width = 1024; // Default assumption
      int height = 1024;

      // Scale down if too large
      if (width > maxLongEdge || height > maxLongEdge) {
        final aspectRatio = width / height;
        if (aspectRatio > 1) {
          width = maxLongEdge;
          height = (maxLongEdge / aspectRatio).round();
        } else {
          height = maxLongEdge;
          width = (maxLongEdge * aspectRatio).round();
        }
      }

      // Scale to fit within short edge constraint
      if (width > maxShortEdge && height > maxShortEdge) {
        final aspectRatio = width / height;
        if (aspectRatio > 1) {
          height = maxShortEdge;
          width = (maxShortEdge * aspectRatio).round();
        } else {
          width = maxShortEdge;
          height = (maxShortEdge / aspectRatio).round();
        }
      }

      // Calculate tiles
      final tilesWidth = (width / tileSize).ceil();
      final tilesHeight = (height / tileSize).ceil();
      final totalTiles = tilesWidth * tilesHeight;

      return baseTokens + (tokensPerTile * totalTiles);
    } catch (e) {
      // Fallback to base tokens if calculation fails
      return baseTokens;
    }
  }

  /// Get character-to-token ratio for a model
  double _getCharToTokenRatio(String model) {
    // Check exact match
    if (_charToTokenRatios.containsKey(model)) {
      return _charToTokenRatios[model]!;
    }

    // Check partial matches
    for (final entry in _charToTokenRatios.entries) {
      if (model.toLowerCase().startsWith(entry.key.toLowerCase())) {
        return entry.value;
      }
    }

    // Default ratio for unknown models
    return 4.0;
  }

  /// Get message formatting overhead
  int _getMessageOverhead(int messageCount, String model) {
    // OpenAI adds ~3 tokens per message + 3 tokens for assistant response priming
    return (messageCount * 3) + 3;
  }

  /// Get tool formatting overhead
  int _getToolOverhead(int toolCount, String model) {
    // Tools add overhead for formatting and schema validation
    return toolCount * 12 + 12;
  }

  /// Format function result for token counting
  String _formatFunctionResult(dynamic result) {
    if (result is String) {
      return result;
    }

    try {
      return jsonEncode(result);
    } catch (e) {
      return result.toString();
    }
  }

  /// Get detailed token breakdown for debugging
  Map<String, dynamic> getTokenBreakdown(
    List<LLMMessage> messages,
    List<ToolSchema> tools,
    String model,
  ) {
    final ratio = _getCharToTokenRatio(model);
    final breakdown = <String, dynamic>{
      'model': model,
      'charToTokenRatio': ratio,
      'messages': <Map<String, dynamic>>[],
      'tools': <Map<String, dynamic>>[],
      'totals': <String, int>{},
    };

    int messageTokens = 0;
    for (final message in messages) {
      final tokens = _countMessageTokens(message, model, ratio);
      messageTokens += tokens;

      breakdown['messages'].add({
        'type': message.runtimeType.toString(),
        'tokens': tokens,
        'hasImages': message.hasImages,
        'imageCount': message.images.length,
      });
    }

    int toolTokens = 0;
    for (final tool in tools) {
      final tokens = _countToolTokens(tool, model, ratio);
      toolTokens += tokens;

      breakdown['tools'].add({
        'name': tool.name,
        'tokens': tokens,
        'hasDescription': tool.description != null,
        'hasParameters': tool.parameters != null,
      });
    }

    final messageOverhead = _getMessageOverhead(messages.length, model);
    final toolOverhead = _getToolOverhead(tools.length, model);
    final totalTokens =
        messageTokens + toolTokens + messageOverhead + toolOverhead;

    breakdown['totals'] = {
      'messageTokens': messageTokens,
      'toolTokens': toolTokens,
      'messageOverhead': messageOverhead,
      'toolOverhead': toolOverhead,
      'totalTokens': totalTokens,
    };

    return breakdown;
  }
}

/// Tiktoken-based token counter (placeholder for future implementation)
///
/// This would use actual tiktoken encoding for precise token counts
class TiktokenCounter extends TokenCounter {
  // This would require tiktoken WASM or native implementation

  @override
  int countTokens(List<LLMMessage> messages, String model) {
    // Placeholder - would use actual tiktoken encoding
    throw UnimplementedError(
        'TiktokenCounter requires tiktoken implementation');
  }

  @override
  int countToolTokens(List<ToolSchema> tools, String model) {
    // Placeholder - would use actual tiktoken encoding
    throw UnimplementedError(
        'TiktokenCounter requires tiktoken implementation');
  }

  @override
  int getTokenLimit(String model) {
    return ApproximateTokenCounter._tokenLimits[model] ?? 4096;
  }
}

/// HTTP-based token counter using OpenAI API
///
/// Uses OpenAI's tokenization endpoint for exact counts (costs API calls)
class RemoteTokenCounter extends TokenCounter {
  final String apiKey;
  final String baseUrl;

  RemoteTokenCounter({
    required this.apiKey,
    this.baseUrl = 'https://api.openai.com/v1',
  });

  @override
  int countTokens(List<LLMMessage> messages, String model) {
    // This would make HTTP calls to OpenAI's tokenization endpoint
    // For now, fall back to approximation
    return ApproximateTokenCounter().countTokens(messages, model);
  }

  @override
  int countToolTokens(List<ToolSchema> tools, String model) {
    return ApproximateTokenCounter().countToolTokens(tools, model);
  }

  @override
  int getTokenLimit(String model) {
    return ApproximateTokenCounter._tokenLimits[model] ?? 4096;
  }
}

/// Utility functions for token counting
class TokenCountingUtils {
  /// Estimate cost based on token usage
  static double estimateCost(
    int inputTokens,
    int outputTokens,
    String model,
  ) {
    // Simplified pricing (prices as of 2024, subject to change)
    const pricing = {
      'gpt-4o': {'input': 0.005, 'output': 0.015}, // per 1K tokens
      'gpt-4o-mini': {'input': 0.00015, 'output': 0.0006},
      'gpt-4-turbo': {'input': 0.01, 'output': 0.03},
      'gpt-4': {'input': 0.03, 'output': 0.06},
      'gpt-3.5-turbo': {'input': 0.001, 'output': 0.002},
    };

    final modelPricing = pricing[model];
    if (modelPricing == null) return 0.0;

    final inputCost = (inputTokens / 1000) * modelPricing['input']!;
    final outputCost = (outputTokens / 1000) * modelPricing['output']!;

    return inputCost + outputCost;
  }

  /// Check if messages fit within token limit
  static bool fitsWithinLimit(
      List<LLMMessage> messages, List<ToolSchema> tools, String model,
      {int? reservedTokens}) {
    final counter = TokenCounterFactory.create(model);
    final usedTokens = counter.countTokens(messages, model) +
        counter.countToolTokens(tools, model);
    final limit = counter.getTokenLimit(model);
    final reserved = reservedTokens ?? 1000; // Reserve for response

    return usedTokens + reserved <= limit;
  }

  /// Truncate messages to fit within token limit
  static List<LLMMessage> truncateToFit(List<LLMMessage> messages, String model,
      {int? reservedTokens}) {
    final counter = TokenCounterFactory.create(model);
    final limit = counter.getTokenLimit(model);
    final reserved = reservedTokens ?? 1000;
    final maxTokens = limit - reserved;

    final result = <LLMMessage>[];
    int currentTokens = 0;

    // Add messages from the end (most recent first)
    for (int i = messages.length - 1; i >= 0; i--) {
      final message = messages[i];
      final messageTokens = (counter as ApproximateTokenCounter)
          ._countMessageTokens(message, model,
              (counter as ApproximateTokenCounter)._getCharToTokenRatio(model));

      if (currentTokens + messageTokens <= maxTokens) {
        result.insert(0, message);
        currentTokens += messageTokens;
      } else {
        break;
      }
    }

    return result;
  }

  /// Get token usage statistics
  static Map<String, dynamic> getUsageStats(
    List<LLMMessage> messages,
    List<ToolSchema> tools,
    String model,
  ) {
    final counter = TokenCounterFactory.create(model);
    final messageTokens = counter.countTokens(messages, model);
    final toolTokens = counter.countToolTokens(tools, model);
    final totalTokens = messageTokens + toolTokens;
    final limit = counter.getTokenLimit(model);

    return {
      'messageTokens': messageTokens,
      'toolTokens': toolTokens,
      'totalTokens': totalTokens,
      'tokenLimit': limit,
      'utilization': totalTokens / limit,
      'remaining': limit - totalTokens,
      'model': model,
    };
  }
}
