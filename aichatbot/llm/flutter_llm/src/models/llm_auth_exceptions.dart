/// Unified exception system for all LLM providers
/// 
/// This replaces the 33+ separate exception classes with 8 core exception types
/// that handle all common LLM provider error scenarios.

import 'base_llm_exception.dart';

/// Authentication and authorization errors (401, 403)
class LLMAuthenticationException extends BaseLLMException {
  const LLMAuthenticationException({
    required String message,
    required String provider,
    String? type,
    String? code,
    int? statusCode,
    Map<String, dynamic>? details,
  }) : super(
          message,
          type: type,
          code: code,
          statusCode: statusCode,
          details: details,
          provider: provider,
        );

  /// Factory for 401 authentication errors
  factory LLMAuthenticationException.unauthorized({
    required String provider,
    String? message,
    String? type,
    String? code,
    Map<String, dynamic>? details,
  }) =>
      LLMAuthenticationException(
        message: message ?? 'Authentication failed. Please check your API key.',
        provider: provider,
        type: type ?? 'authentication_error',
        code: code,
        statusCode: 401,
        details: details,
      );

  /// Factory for 403 permission errors
  factory LLMAuthenticationException.forbidden({
    required String provider,
    String? message,
    String? type,
    String? code,
    Map<String, dynamic>? details,
  }) =>
      LLMAuthenticationException(
        message: message ?? 'Access forbidden. Insufficient permissions.',
        provider: provider,
        type: type ?? 'permission_error',
        code: code,
        statusCode: 403,
        details: details,
      );
}

/// Rate limiting and quota errors (429)
class LLMRateLimitException extends BaseLLMException {
  const LLMRateLimitException({
    required String message,
    required String provider,
    String? type,
    String? code,
    int? statusCode,
    Map<String, dynamic>? details,
    this.retryAfter,
    this.quotaType,
  }) : super(
          message,
          type: type,
          code: code,
          statusCode: statusCode,
          details: details,
          provider: provider,
        );

  /// Time to wait before retrying (in seconds)
  final int? retryAfter;

  /// Type of quota exceeded (tokens, requests, etc.)
  final String? quotaType;

  factory LLMRateLimitException.fromResponse({
    required String provider,
    String? message,
    String? type,
    String? code,
    Map<String, dynamic>? details,
    int? retryAfter,
    String? quotaType,
  }) =>
      LLMRateLimitException(
        message: message ?? 'Rate limit exceeded. Please try again later.',
        provider: provider,
        type: type ?? 'rate_limit_error',
        code: code,
        statusCode: 429,
        details: details,
        retryAfter: retryAfter,
        quotaType: quotaType,
      );

  @override
  Map<String, dynamic> toJson() {
    final json = super.toJson();
    json.addAll({
      'retryAfter': retryAfter,
      'quotaType': quotaType,
    });
    return json;
  }
}

/// Bad request and validation errors (400, 422)
class LLMBadRequestException extends BaseLLMException {
  const LLMBadRequestException({
    required String message,
    required String provider,
    String? type,
    String? code,
    int? statusCode,
    Map<String, dynamic>? details,
    this.validationErrors,
  }) : super(
          message,
          type: type,
          code: code,
          statusCode: statusCode,
          details: details,
          provider: provider,
        );

  /// Specific validation errors if available
  final List<String>? validationErrors;

  factory LLMBadRequestException.fromResponse({
    required String provider,
    String? message,
    String? type,
    String? code,
    int? statusCode,
    Map<String, dynamic>? details,
    List<String>? validationErrors,
  }) =>
      LLMBadRequestException(
        message: message ?? 'Invalid request parameters.',
        provider: provider,
        type: type ?? 'invalid_request_error',
        code: code,
        statusCode: statusCode ?? 400,
        details: details,
        validationErrors: validationErrors,
      );

  @override
  Map<String, dynamic> toJson() {
    final json = super.toJson();
    json['validationErrors'] = validationErrors;
    return json;
  }
}

/// Resource not found errors (404)
class LLMNotFoundException extends BaseLLMException {
  const LLMNotFoundException({
    required String message,
    required String provider,
    String? type,
    String? code,
    Map<String, dynamic>? details,
    this.resourceType,
    this.resourceId,
  }) : super(
          message,
          type: type,
          code: code,
          statusCode: 404,
          details: details,
          provider: provider,
        );

  /// Type of resource that was not found
  final String? resourceType;

  /// ID of the resource that was not found
  final String? resourceId;

  factory LLMNotFoundException.model({
    required String provider,
    required String modelId,
    String? message,
  }) =>
      LLMNotFoundException(
        message: message ?? 'Model "$modelId" not found.',
        provider: provider,
        type: 'model_not_found',
        resourceType: 'model',
        resourceId: modelId,
      );

  @override
  Map<String, dynamic> toJson() {
    final json = super.toJson();
    json.addAll({
      'resourceType': resourceType,
      'resourceId': resourceId,
    });
    return json;
  }
}

/// Server errors (500, 502, 503, 504)
class LLMServerException extends BaseLLMException {
  const LLMServerException({
    required String message,
    required String provider,
    String? type,
    String? code,
    int? statusCode,
    Map<String, dynamic>? details,
    this.isServiceUnavailable = false,
  }) : super(
          message,
          type: type,
          code: code,
          statusCode: statusCode,
          details: details,
          provider: provider,
        );

  /// Whether this is a temporary service unavailability
  final bool isServiceUnavailable;

  factory LLMServerException.fromStatusCode({
    required String provider,
    required int statusCode,
    String? message,
    String? type,
    String? code,
    Map<String, dynamic>? details,
  }) {
    String defaultMessage;
    bool serviceUnavailable = false;

    switch (statusCode) {
      case 500:
        defaultMessage = 'Internal server error occurred.';
        break;
      case 502:
        defaultMessage = 'Bad gateway error. Service temporarily unavailable.';
        serviceUnavailable = true;
        break;
      case 503:
        defaultMessage = 'Service temporarily unavailable.';
        serviceUnavailable = true;
        break;
      case 504:
        defaultMessage = 'Gateway timeout. Service took too long to respond.';
        serviceUnavailable = true;
        break;
      default:
        defaultMessage = 'Server error occurred.';
    }

    return LLMServerException(
      message: message ?? defaultMessage,
      provider: provider,
      type: type ?? 'server_error',
      code: code,
      statusCode: statusCode,
      details: details,
      isServiceUnavailable: serviceUnavailable,
    );
  }

  @override
  Map<String, dynamic> toJson() {
    final json = super.toJson();
    json['isServiceUnavailable'] = isServiceUnavailable;
    return json;
  }
}

/// Network connectivity and timeout errors
class LLMNetworkException extends BaseLLMException {
  const LLMNetworkException({
    required String message,
    required String provider,
    String? type,
    String? code,
    Map<String, dynamic>? details,
    this.timeoutDuration,
    this.isTimeout = false,
  }) : super(
          message,
          type: type,
          code: code,
          statusCode: null, // Network errors don't have HTTP status codes
          details: details,
          provider: provider,
        );

  /// Timeout duration if this is a timeout error
  final Duration? timeoutDuration;

  /// Whether this is specifically a timeout error
  final bool isTimeout;

  factory LLMNetworkException.timeout({
    required String provider,
    required Duration timeout,
    String? message,
  }) =>
      LLMNetworkException(
        message: message ?? 'Request timed out after ${timeout.inSeconds}s.',
        provider: provider,
        type: 'timeout_error',
        timeoutDuration: timeout,
        isTimeout: true,
      );

  factory LLMNetworkException.connectionFailed({
    required String provider,
    String? message,
    String? details,
  }) =>
      LLMNetworkException(
        message: message ?? 'Failed to connect to service.',
        provider: provider,
        type: 'connection_error',
        details: details != null ? {'details': details} : null,
      );

  @override
  Map<String, dynamic> toJson() {
    final json = super.toJson();
    json.addAll({
      'timeoutDuration': timeoutDuration?.inMilliseconds,
      'isTimeout': isTimeout,
    });
    return json;
  }
}

/// Content safety and moderation errors
class LLMContentException extends BaseLLMException {
  const LLMContentException({
    required String message,
    required String provider,
    String? type,
    String? code,
    int? statusCode,
    Map<String, dynamic>? details,
    this.contentType,
    this.categories,
  }) : super(
          message,
          type: type,
          code: code,
          statusCode: statusCode,
          details: details,
          provider: provider,
        );

  /// Type of content that was flagged (input, output)
  final String? contentType;

  /// Safety categories that were triggered
  final List<String>? categories;

  factory LLMContentException.contentFiltered({
    required String provider,
    String? message,
    String? contentType,
    List<String>? categories,
    Map<String, dynamic>? details,
  }) =>
      LLMContentException(
        message: message ?? 'Content was filtered due to safety policies.',
        provider: provider,
        type: 'content_filter_error',
        statusCode: 400,
        contentType: contentType,
        categories: categories,
        details: details,
      );

  @override
  Map<String, dynamic> toJson() {
    final json = super.toJson();
    json.addAll({
      'contentType': contentType,
      'categories': categories,
    });
    return json;
  }
}

/// Model capability and feature errors
class LLMCapabilityException extends BaseLLMException {
  const LLMCapabilityException({
    required String message,
    required String provider,
    String? type,
    String? code,
    int? statusCode,
    Map<String, dynamic>? details,
    this.feature,
    this.modelId,
  }) : super(
          message,
          type: type,
          code: code,
          statusCode: statusCode,
          details: details,
          provider: provider,
        );

  /// Feature that is not supported
  final String? feature;

  /// Model that doesn't support the feature
  final String? modelId;

  factory LLMCapabilityException.unsupportedFeature({
    required String provider,
    required String feature,
    String? modelId,
    String? message,
  }) =>
      LLMCapabilityException(
        message: message ??
            'Feature "$feature" is not supported${modelId != null ? ' by model "$modelId"' : ''}.',
        provider: provider,
        type: 'unsupported_feature',
        statusCode: 400,
        feature: feature,
        modelId: modelId,
      );

  @override
  Map<String, dynamic> toJson() {
    final json = super.toJson();
    json.addAll({
      'feature': feature,
      'modelId': modelId,
    });
    return json;
  }
}

/// Utility class to create exceptions from HTTP responses
class LLMExceptionFactory {
  /// Create appropriate exception from HTTP status code and response
  static BaseLLMException fromHttpResponse({
    required String provider,
    required int statusCode,
    required String responseBody,
    Map<String, dynamic>? errorData,
  }) {
    final message = errorData?['message'] as String? ??
        errorData?['error']?['message'] as String? ??
        'Unknown error occurred';
    final type = errorData?['type'] as String? ?? errorData?['error']?['type'] as String?;
    final code = errorData?['code'] as String? ?? errorData?['error']?['code'] as String?;

    switch (statusCode) {
      case 401:
        return LLMAuthenticationException.unauthorized(
          provider: provider,
          message: message,
          type: type,
          code: code,
          details: errorData,
        );
      case 403:
        return LLMAuthenticationException.forbidden(
          provider: provider,
          message: message,
          type: type,
          code: code,
          details: errorData,
        );
      case 400:
      case 422:
        return LLMBadRequestException.fromResponse(
          provider: provider,
          message: message,
          type: type,
          code: code,
          statusCode: statusCode,
          details: errorData,
        );
      case 404:
        return LLMNotFoundException(
          message: message,
          provider: provider,
          type: type,
          code: code,
          details: errorData,
        );
      case 429:
        return LLMRateLimitException.fromResponse(
          provider: provider,
          message: message,
          type: type,
          code: code,
          details: errorData,
        );
      default:
        if (statusCode >= 500) {
          return LLMServerException.fromStatusCode(
            provider: provider,
            statusCode: statusCode,
            message: message,
            type: type,
            code: code,
            details: errorData,
          );
        } else {
          // Fallback for unknown client errors
          return LLMBadRequestException.fromResponse(
            provider: provider,
            message: message,
            type: type,
            code: code,
            statusCode: statusCode,
            details: errorData,
          );
        }
    }
  }
}