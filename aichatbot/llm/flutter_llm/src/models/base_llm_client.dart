import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:cancellation_token/cancellation_token.dart';
import 'package:http/http.dart' as http;
import 'package:meta/meta.dart';

import '../model_context/chat_completion_context.dart';
import '../utils/logger.dart';
import 'llm_response.dart';
import 'model_client.dart';
import 'model_types.dart';
import 'request_types.dart';
import 'tool_schema.dart';

/// Abstract base class for all LLM clients using Template Method pattern
///
/// This class implements the Template Method design pattern to provide a common
/// structure for all LLM clients while allowing each provider to implement their
/// specific logic. The base class handles:
///
/// - Common HTTP transport layer with retry logic
/// - Request/response flow control
/// - Usage tracking and statistics
/// - Error handling patterns
/// - Resource management
///
/// Subclasses must implement provider-specific methods:
/// - Request building and transformation
/// - Response parsing
/// - Authentication
/// - Streaming response handling
///
/// ## Usage
///
/// This class is not meant to be used directly. Instead, use concrete
/// implementations like OpenAIClient or AnthropicClient.
///
/// ## Template Method Flow
///
/// ```
/// 1. validateRequest() - Validate input parameters
/// 2. buildRequest() - Transform to provider format
/// 3. executeRequest() - HTTP transport (common)
/// 4. parseResponse() - Parse provider response
/// 5. updateUsage() - Track usage statistics (common)
/// ```
abstract class BaseLLMClient implements ChatCompletionClient {
  // === Configuration ===
  final String model;
  final Duration timeout;
  final int maxRetries;
  final Duration retryDelay;
  final Map<String, String> customHeaders;
  final bool enableLogging;

  // === HTTP Client ===
  final http.Client _httpClient;

  // === Usage Tracking ===
  RequestUsage _totalUsage = RequestUsage(promptTokens: 0, completionTokens: 0);
  RequestUsage _actualUsage =
      RequestUsage(promptTokens: 0, completionTokens: 0);

  /// Create a base LLM client with common configuration
  ///
  /// Parameters:
  /// - [model]: Model identifier for the LLM
  /// - [timeout]: Request timeout duration
  /// - [maxRetries]: Maximum number of retry attempts
  /// - [retryDelay]: Delay between retry attempts
  /// - [customHeaders]: Additional headers to include
  /// - [enableLogging]: Enable request/response logging
  /// - [httpClient]: Custom HTTP client (optional)
  BaseLLMClient({
    required this.model,
    this.timeout = const Duration(seconds: 60),
    this.maxRetries = 3,
    this.retryDelay = const Duration(seconds: 1),
    this.customHeaders = const {},
    this.enableLogging = false,
    http.Client? httpClient,
  }) : _httpClient = httpClient ?? http.Client();

  // === Abstract Methods (Template Method Pattern) ===

  /// Get default headers required by the provider
  ///
  /// Each provider returns their required headers (auth, content-type, etc).
  Map<String, String> get defaultHeaders;

  /// Build the complete URI for a specific request type
  ///
  /// Each provider implements this to return the appropriate URI
  /// for different types of requests (completion, streaming, etc).
  ///
  /// Parameters:
  /// - [type]: The type of request being made
  /// - [params]: Additional parameters that might affect the URI
  ///              (e.g., model name for Gemini)
  Uri buildRequestUri(RequestType type, Map<String, dynamic> params);

  /// Get model information for this client
  ///
  /// Each provider returns their model capabilities and metadata.
  @override
  ModelInfo get modelInfo;

  /// Validate request parameters before processing
  ///
  /// Each provider implements their specific validation rules.
  /// Should throw ArgumentError for invalid requests.
  void validateRequest(
    List<LLMMessage> messages,
    List<ToolSchema>? tools,
    String? toolChoice,
  );

  /// Build HTTP request body in provider-specific format
  ///
  /// Each provider transforms the universal parameters into their API format.
  Map<String, dynamic> buildRequestBody({
    required List<LLMMessage> messages,
    double? temperature,
    int? maxTokens,
    List<String>? stopSequences,
    List<ToolSchema>? tools,
    String? toolChoice,
    Map<String, dynamic>? additionalParams,
    bool stream = false,
  });

  /// Parse HTTP response into ChatResponse
  ///
  /// Each provider parses their API response format.
  ChatResponse parseResponse(Map<String, dynamic> response);

  /// Parse streaming response line into ChatStreamChunk
  ///
  /// Each provider handles their streaming format (SSE, JSON lines, etc).
  Stream<ChatStreamChunk> parseStreamResponse(Stream<String> lines);

  /// Count tokens for the given messages
  ///
  /// Each provider implements their token counting logic.
  @override
  Future<int> countTokens(List<LLMMessage> messages);

  /// Create provider-specific exception from HTTP error
  ///
  /// Each provider maps HTTP errors to their exception types.
  Exception createHttpException(
      int statusCode, String body, Map<String, dynamic>? errorData);

  // === Template Methods (Common Implementation) ===

  /// Template method for chat completion requests
  ///
  /// This method defines the standard flow that all providers follow:
  /// 1. Validate request parameters
  /// 2. Build provider-specific request
  /// 3. Execute HTTP request with retry logic
  /// 4. Parse response
  /// 5. Update usage tracking
  @override
  Future<ChatResponse> createChatCompletion({
    required List<LLMMessage> messages,
    double? temperature,
    int? maxTokens,
    List<String>? stopSequences,
    List<ToolSchema>? tools,
    String? toolChoice,
    Map<String, dynamic>? additionalParams,
    CancellationToken? cancellationToken,
  }) async {
    // Step 1: Validation
    validateRequest(messages, tools, toolChoice);

    // Step 2: Build request
    final requestBody = buildRequestBody(
      messages: messages,
      temperature: temperature,
      maxTokens: maxTokens,
      stopSequences: stopSequences,
      tools: tools,
      toolChoice: toolChoice,
      additionalParams: additionalParams,
      stream: false,
    );

    // Step 3: Build URI and execute HTTP request
    final uri = buildRequestUri(RequestType.chatCompletion, requestBody);
    final headers = <String, String>{};
    headers.addAll(defaultHeaders);
    headers.addAll(customHeaders);

    final responseData = await _executeHttpRequest(
      uri: uri,
      requestBody: requestBody,
      headers: headers,
      cancellationToken: cancellationToken,
    );

    // Step 4: Parse response
    final response = parseResponse(responseData);

    // Step 5: Update usage tracking
    _updateUsageTracking(response.usage);

    return response;
  }

  /// Template method for streaming chat completion requests
  @override
  Stream<ChatStreamChunk> createChatCompletionStream({
    required List<LLMMessage> messages,
    double? temperature,
    int? maxTokens,
    List<String>? stopSequences,
    List<ToolSchema>? tools,
    String? toolChoice,
    Map<String, dynamic>? additionalParams,
    CancellationToken? cancellationToken,
  }) async* {
    // Step 1: Validation
    validateRequest(messages, tools, toolChoice);

    // Step 2: Build streaming request
    final requestBody = buildRequestBody(
      messages: messages,
      temperature: temperature,
      maxTokens: maxTokens,
      stopSequences: stopSequences,
      tools: tools,
      toolChoice: toolChoice,
      additionalParams: additionalParams,
      stream: true,
    );

    // Step 3: Build URI and execute streaming HTTP request
    final uri = buildRequestUri(RequestType.chatCompletionStream, requestBody);
    final headers = <String, String>{};
    headers.addAll(defaultHeaders);
    headers.addAll(customHeaders);

    final rawStream = _executeStreamingHttpRequest(
      uri: uri,
      requestBody: requestBody,
      headers: headers,
      cancellationToken: cancellationToken,
    );

    // Step 4: Parse streaming response
    await for (final chunk in parseStreamResponse(rawStream)) {
      // Step 5: Update usage tracking for final chunk
      if (chunk.isLast && chunk.usage != null) {
        _updateUsageTracking(chunk.usage);
      }

      yield chunk;
    }
  }

  @override
  Future<int> getRemainingTokens(List<LLMMessage> messages) async {
    final used = await countTokens(messages);
    final max = modelInfo.effectiveMaxInputTokens;
    return max != null ? max - used : 0;
  }

  @override
  Future<bool> canFitMessages(List<LLMMessage> messages) async {
    final remaining = await getRemainingTokens(messages);
    return remaining >= 0;
  }

  @override
  Future<void> dispose() async {
    _httpClient.close();
  }

  // === Common HTTP Implementation ===

  /// Execute HTTP request with retry logic and error handling
  ///
  /// This is a protected method that subclasses can use for their custom endpoints.
  /// It provides retry logic, timeout handling, error mapping, and logging.
  ///
  /// Parameters:
  /// - [uri]: The complete URI for the request
  /// - [requestBody]: The request body (will be JSON encoded for non-GET methods)
  /// - [method]: HTTP method (GET, POST, etc.)
  /// - [headers]: Complete headers for the request (including auth)
  /// - [cancellationToken]: Token to cancel the request
  @protected
  Future<Map<String, dynamic>> executeHttpRequest({
    required Uri uri,
    required Map<String, dynamic> requestBody,
    String method = 'POST',
    Map<String, String>? headers,
    CancellationToken? cancellationToken,
  }) async {
    return _executeHttpRequest(
      uri: uri,
      requestBody: requestBody,
      method: method,
      headers: headers,
      cancellationToken: cancellationToken,
    );
  }

  /// Internal implementation of HTTP request execution
  Future<Map<String, dynamic>> _executeHttpRequest({
    required Uri uri,
    required Map<String, dynamic> requestBody,
    String method = 'POST',
    Map<String, String>? headers,
    CancellationToken? cancellationToken,
  }) async {
    Exception? lastException;

    for (int attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        // Create HTTP request
        final request = http.Request(method, uri);

        // Set headers
        if (headers != null) {
          request.headers.addAll(headers);
        }

        // Set body (only for methods that support body)
        if (method != 'GET' && method != 'HEAD' && method != 'DELETE') {
          request.body = jsonEncode(requestBody);
        }

        // Log request if enabled
        if (enableLogging) {
          _logRequest(request);
        }

        // Check cancellation
        if (cancellationToken?.isCancelled ?? false) {
          throw const CancellationException('Request was cancelled');
        }

        // Execute request
        final streamedResponse =
            await _httpClient.send(request).timeout(timeout);
        final response = await http.Response.fromStream(streamedResponse);

        // Log response if enabled
        if (enableLogging) {
          _logResponse(response);
        }

        // Handle HTTP errors
        if (response.statusCode >= 400) {
          Map<String, dynamic>? errorData;
          try {
            errorData = jsonDecode(response.body) as Map<String, dynamic>?;
          } catch (_) {
            // Ignore JSON parse errors
          }

          final exception = createHttpException(
              response.statusCode, response.body, errorData);

          // Don't retry on client errors (4xx)
          if (response.statusCode >= 400 && response.statusCode < 500) {
            throw exception;
          }

          lastException = exception;
        } else {
          // Success - parse and return response
          return jsonDecode(response.body) as Map<String, dynamic>;
        }
      } on TimeoutException {
        lastException = TimeoutException(
            'Request timed out after ${timeout.inSeconds}s', timeout);
      } on SocketException catch (e) {
        lastException = SocketException('Network error: ${e.message}');
      } on CancellationException {
        rethrow; // Don't retry cancellation
      } catch (e) {
        lastException = Exception('Unexpected error: $e');
      }

      // Wait before retry (except on last attempt)
      if (attempt < maxRetries) {
        await Future.delayed(retryDelay);
      }
    }

    // All attempts failed
    throw lastException ?? Exception('All retry attempts failed');
  }

  /// Execute streaming HTTP request
  ///
  /// This is a protected method that subclasses can use for their custom streaming endpoints.
  /// It provides timeout handling, error mapping, and proper stream processing.
  ///
  /// Parameters:
  /// - [uri]: The complete URI for the request
  /// - [requestBody]: The request body (will be JSON encoded)
  /// - [headers]: Complete headers for the request (including auth)
  /// - [cancellationToken]: Token to cancel the request
  @protected
  Stream<String> executeStreamingHttpRequest({
    required Uri uri,
    required Map<String, dynamic> requestBody,
    String method = 'POST',
    Map<String, String>? headers,
    CancellationToken? cancellationToken,
  }) {
    return _executeStreamingHttpRequest(
      uri: uri,
      requestBody: requestBody,
      method: method,
      headers: headers,
      cancellationToken: cancellationToken,
    );
  }

  /// Internal implementation of streaming HTTP request
  Stream<String> _executeStreamingHttpRequest({
    required Uri uri,
    required Map<String, dynamic> requestBody,
    String method = 'POST',
    Map<String, String>? headers,
    CancellationToken? cancellationToken,
  }) async* {
    // Create HTTP request
    final request = http.Request('POST', uri);
    // Set body (only for methods that support body)
    if (method != 'GET' && method != 'HEAD' && method != 'DELETE') {
      request.body = jsonEncode(requestBody);
    }
    // Set headers
    if (headers != null) {
      request.headers.addAll(headers);
    }
    // Always add streaming-specific headers
    request.headers['Accept'] = 'text/event-stream';
    request.headers['Cache-Control'] = 'no-cache';

    // Log request if enabled
    if (enableLogging) {
      _logRequest(request);
    }

    // Check cancellation
    if (cancellationToken?.isCancelled ?? false) {
      throw const CancellationException('Request was cancelled');
    }

    // Execute request
    final streamedResponse = await _httpClient.send(request).timeout(timeout);

    // Handle HTTP errors
    if (streamedResponse.statusCode >= 400) {
      final responseBody = await streamedResponse.stream.bytesToString();
      Map<String, dynamic>? errorData;
      try {
        errorData = jsonDecode(responseBody) as Map<String, dynamic>?;
      } catch (_) {
        // Ignore JSON parse errors
      }

      throw createHttpException(
          streamedResponse.statusCode, responseBody, errorData);
    }

    // Process streaming response
    String buffer = '';
    await for (final chunk in streamedResponse.stream.map(utf8.decode)) {
      // Check cancellation
      if (cancellationToken?.isCancelled ?? false) {
        throw const CancellationException('Request was cancelled');
      }

      buffer += chunk;

      // Process complete lines
      while (buffer.contains('\n')) {
        final lineEnd = buffer.indexOf('\n');
        final line = buffer.substring(0, lineEnd).trim();
        buffer = buffer.substring(lineEnd + 1);

        if (line.isNotEmpty) {
          yield line;
        }
      }
    }

    // Process remaining buffer
    if (buffer.trim().isNotEmpty) {
      yield buffer.trim();
    }
  }

  // === Usage Tracking ===

  /// Update usage statistics
  void _updateUsageTracking(RequestUsage? usage) {
    if (usage != null) {
      _totalUsage = RequestUsage(
        promptTokens:
            (_totalUsage.promptTokens ?? 0) + (usage.promptTokens ?? 0),
        completionTokens:
            (_totalUsage.completionTokens ?? 0) + (usage.completionTokens ?? 0),
      );
      _actualUsage = _totalUsage;
    }
  }

  /// Get total usage statistics across all requests
  RequestUsage get totalUsage => _totalUsage;

  /// Get actual usage statistics (alias for totalUsage)
  RequestUsage get actualUsage => _actualUsage;

  /// Reset usage statistics
  void resetUsage() {
    _totalUsage = RequestUsage(promptTokens: 0, completionTokens: 0);
    _actualUsage = RequestUsage(promptTokens: 0, completionTokens: 0);
  }

  // === Utility Methods ===

  /// Log HTTP request for debugging
  void _logRequest(http.Request request) {
    if (!enableLogging) return;

    final logger = LLMLoggers.http;
    logger.info('=== ${runtimeType} HTTP Request ===');
    logger.info('URL: ${request.url}');
    logger.info('Method: ${request.method}');
    logger.info('Headers: ${request.headers}');
    logger.info('Body: ${_truncateForLog(request.body)}');
    logger.info('${'=' * 40}');
  }

  /// Log HTTP response for debugging
  void _logResponse(http.Response response) {
    if (!enableLogging) return;

    final logger = LLMLoggers.http;
    print(logger.level);
    logger.info('=== ${runtimeType} HTTP Response ===');
    logger.info('Status: ${response.statusCode}');
    logger.info('Headers: ${response.headers}');
    logger.info('Body: ${_truncateForLog(response.body)}');
    logger.info('${'=' * 40}');
  }

  /// Truncate content for logging
  String _truncateForLog(String content) {
    const maxLength = 1000;
    return content.length > maxLength
        ? '${content.substring(0, maxLength)}...'
        : content;
  }

  /// Get request configuration information for debugging
  Map<String, dynamic> getRequestInfo() {
    return {
      'clientType': runtimeType.toString(),
      'model': model,
      'timeout': timeout.inMilliseconds,
      'maxRetries': maxRetries,
      'retryDelay': retryDelay.inMilliseconds,
      'enableLogging': enableLogging,
      'customHeaders': customHeaders.keys.toList(),
      'hasDefaultHeaders': defaultHeaders.isNotEmpty,
      'totalUsage': {
        'promptTokens': _totalUsage.promptTokens,
        'completionTokens': _totalUsage.completionTokens,
      },
    };
  }

  /// Perform health check to verify API connectivity
  Future<bool> healthCheck() async {
    try {
      // Simple test request - subclasses can override for provider-specific checks
      final testMessages = [UserMessage(text: 'test')];
      validateRequest(testMessages, null, null);
      return true; // If validation passes, assume healthy
    } catch (e) {
      return false;
    }
  }
}

/// Exception thrown when a request is cancelled
class CancellationException implements Exception {
  final String message;
  const CancellationException(this.message);

  @override
  String toString() => 'CancellationException: $message';
}
