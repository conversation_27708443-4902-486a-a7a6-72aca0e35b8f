/// Types of requests that can be made to LLM APIs
enum RequestType {
  /// Standard chat completion request
  chatCompletion,

  /// Streaming chat completion request
  chatCompletionStream,

  /// Token counting request
  tokenCount,

  /// Model listing request
  listModels,

  /// Model information request
  modelInfo,

  /// Embedding request
  embedding,

  /// Batch embedding request
  batchEmbedding,
}

/// Extension methods for RequestType
extension RequestTypeExtension on RequestType {
  /// Get a human-readable name for the request type
  String get displayName {
    switch (this) {
      case RequestType.chatCompletion:
        return 'Chat Completion';
      case RequestType.chatCompletionStream:
        return 'Chat Completion Stream';
      case RequestType.tokenCount:
        return 'Token Count';
      case RequestType.listModels:
        return 'List Models';
      case RequestType.modelInfo:
        return 'Model Info';
      case RequestType.embedding:
        return 'Embedding';
      case RequestType.batchEmbedding:
        return 'Batch Embedding';
    }
  }

  /// Check if this is a streaming request type
  bool get isStreaming {
    return this == RequestType.chatCompletionStream;
  }
}
