import 'dart:math' as math;

import '../model_context/chat_completion_context.dart';
import 'model_types.dart';

/// Streaming chunk for chat completions
class ChatStreamChunk extends LlmResponse<AssistantMessage> {
  final String? id;
  final bool isFirst;
  final bool isLast;

  ChatStreamChunk({
    this.id,
    String? text,
    List<FunctionCall>? functionCalls,
    String? finishReason,
    RequestUsage? usage,
    this.isFirst = false,
    this.isLast = false,
    Map<String, dynamic> metadata = const {},
    ModelInfo? modelInfo,
    DateTime? timestamp,
  }) : super(
          content: AssistantMessage(
            text: text,
            functionCalls: functionCalls ?? [],
            metadata: metadata,
          ),
          finishReason: finishReason,
          usage: usage,
          modelInfo: modelInfo,
          metadata: metadata,
          timestamp: timestamp,
        );

  // Convenience accessors that delegate to content (AssistantMessage)
  String? get text => content.text;
  List<FunctionCall> get functionCalls => content.functionCalls;
  bool get hasText => text != null && text!.isNotEmpty;
  bool get hasFunctionCalls => functionCalls.isNotEmpty;

  @override
  Map<String, dynamic> toJson() {
    final json = super.toJson();
    json.addAll({
      'id': id,
      'isFirst': isFirst,
      'isLast': isLast,
    });
    return json;
  }

  factory ChatStreamChunk.fromJson(Map<String, dynamic> json) {
    return ChatStreamChunk(
      id: json['id'] as String?,
      text: json['text'] as String?,
      functionCalls: (json['functionCalls'] as List<dynamic>?)
          ?.map((call) => FunctionCall(
                id: call['id'] as String,
                name: call['name'] as String,
                arguments: call['arguments'],
              ))
          .toList(),
      finishReason: json['finishReason'] as String?,
      usage: json['usage'] != null
          ? RequestUsage.fromJson(json['usage'] as Map<String, dynamic>)
          : null,
      isFirst: json['isFirst'] as bool? ?? false,
      isLast: json['isLast'] as bool? ?? false,
      metadata: json['metadata'] as Map<String, dynamic>? ?? {},
      modelInfo: json['modelInfo'] != null
          ? ModelInfo.fromJson(json['modelInfo'] as Map<String, dynamic>)
          : null,
      timestamp: json['timestamp'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['timestamp'] as int)
          : null,
    );
  }
}

/// Model response with usage information
class LlmResponse<T> {
  LlmResponse({
    required this.content,
    this.finishReason,
    this.usage,
    this.modelInfo,
    this.metadata = const {},
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();
  final T content;
  final String? finishReason;
  final RequestUsage? usage;
  final ModelInfo? modelInfo;
  final Map<String, dynamic> metadata;
  final DateTime timestamp;

  /// Check if the response was completed successfully
  bool get isComplete => finishReason != 'length' && finishReason != 'error';

  /// Check if the response was truncated due to length limits
  bool get wasTruncated => finishReason == 'length';

  /// Check if there was an error
  bool get hasError => finishReason == 'error';

  /// Get estimated cost if model info and usage are available
  double? get estimatedCost {
    if (usage != null && modelInfo != null) {
      return usage!.calculateCost(modelInfo!);
    }
    return null;
  }

  Map<String, dynamic> toJson() {
    return {
      'content': _serializeContent(content),
      'finishReason': finishReason,
      'usage': usage?.toJson(),
      'modelInfo': modelInfo?.toJson(),
      'metadata': metadata,
      'timestamp': timestamp.millisecondsSinceEpoch,
      'isComplete': isComplete,
      'wasTruncated': wasTruncated,
      'hasError': hasError,
      'estimatedCost': estimatedCost,
    };
  }

  /// Serialize content based on its type
  dynamic _serializeContent(dynamic content) {
    if (content is String) {
      return content;
    } else if (content is List) {
      return content.map(_serializeContent).toList();
    } else if (content is Map) {
      return content
          .map((k, v) => MapEntry(k.toString(), _serializeContent(v)));
    } else if (content is LLMMessage) {
      return content.toJson();
    } else {
      return content.toString();
    }
  }

  @override
  String toString() {
    return 'CreateResult(${content.runtimeType}, $finishReason, ${usage?.effectiveTotalTokens ?? 0} tokens)';
  }
}

/// Chat completion specific result
class ChatResponse extends LlmResponse<AssistantMessage> {
  ChatResponse({
    required AssistantMessage content,
    String? finishReason,
    RequestUsage? usage,
    ModelInfo? modelInfo,
    Map<String, dynamic> metadata = const {},
    DateTime? timestamp,
  }) : super(
          content: content,
          finishReason: finishReason,
          usage: usage,
          modelInfo: modelInfo,
          metadata: metadata,
          timestamp: timestamp,
        );

  /// Get the text content of the assistant message
  String? get text => content.text;

  /// Get function calls from the assistant message
  List<FunctionCall> get functionCalls => content.functionCalls;

  /// Check if the response contains function calls
  bool get hasFunctionCalls => functionCalls.isNotEmpty;
}

/// Embedding result
class EmbeddingResponse extends LlmResponse<List<List<double>>> {
  EmbeddingResponse({
    required List<List<double>> content,
    required this.inputs,
    RequestUsage? usage,
    ModelInfo? modelInfo,
    Map<String, dynamic> metadata = const {},
    DateTime? timestamp,
  }) : super(
          content: content,
          usage: usage,
          modelInfo: modelInfo,
          metadata: metadata,
          timestamp: timestamp,
        );
  final List<String> inputs;

  /// Get embeddings
  List<List<double>> get embeddings => content;

  /// Get the number of embeddings
  int get embeddingCount => content.length;

  /// Get the dimension of embeddings
  int get embeddingDimension => content.isNotEmpty ? content.first.length : 0;

  /// Get a specific embedding by index
  List<double>? getEmbedding(int index) {
    return index >= 0 && index < content.length ? content[index] : null;
  }

  /// Calculate similarity between two embeddings (cosine similarity)
  static double cosineSimilarity(List<double> a, List<double> b) {
    if (a.length != b.length) {
      throw ArgumentError('Embeddings must have the same dimension');
    }

    double dotProduct = 0.0;
    double normA = 0.0;
    double normB = 0.0;

    for (int i = 0; i < a.length; i++) {
      dotProduct += a[i] * b[i];
      normA += a[i] * a[i];
      normB += b[i] * b[i];
    }

    return dotProduct / (math.sqrt(normA) * math.sqrt(normB));
  }
}
