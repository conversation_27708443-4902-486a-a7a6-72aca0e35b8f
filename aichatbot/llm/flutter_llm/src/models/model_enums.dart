/// 模型能力枚举
///
/// 定义了LLM模型支持的所有能力类型
enum ModelCapability {
  /// 支持文本生成
  textGeneration('text_generation'),

  /// 支持图像输入
  imageInput('image_input'),

  /// 支持图像生成
  imageGeneration('image_generation'),

  /// 支持音频输入
  audioInput('audio_input'),

  /// 支持音频生成
  audioGeneration('audio_generation'),

  /// 支持视频输入
  videoInput('video_input'),

  /// 支持视频生成
  videoGeneration('video_generation'),

  /// 支持函数调用
  functionCalling('function_calling'),

  /// 支持向量嵌入
  embeddings('embeddings'),

  /// 支持微调
  fineTuning('fine_tuning'),

  /// 支持流式输出
  streaming('streaming'),

  /// 支持系统消息
  systemMessages('system_messages'),

  /// 支持批处理
  batching('batching');

  const ModelCapability(this.value);

  /// 枚举对应的字符串值
  final String value;

  /// 从字符串值获取枚举
  static ModelCapability? fromString(String value) {
    for (final capability in ModelCapability.values) {
      if (capability.value == value) {
        return capability;
      }
    }
    return null;
  }

  /// 获取所有能力的字符串值列表
  static List<String> get allValues {
    return ModelCapability.values.map((e) => e.value).toList();
  }
}

/// 定价层级枚举
///
/// 定义了模型的定价层级类型
enum PricingTier {
  /// 免费层级
  free('free'),

  /// 标准层级
  standard('standard'),

  /// 高级层级
  premium('premium'),

  /// 企业层级
  enterprise('enterprise'),

  /// 开发者层级
  developer('developer'),

  /// 学术研究层级
  academic('academic');

  const PricingTier(this.value);

  /// 枚举对应的字符串值
  final String value;

  /// 从字符串值获取枚举
  static PricingTier? fromString(String value) {
    for (final tier in PricingTier.values) {
      if (tier.value == value) {
        return tier;
      }
    }
    return null;
  }
}

/// 货币枚举
///
/// 定义了系统支持的所有货币类型
enum Currency {
  /// 美元
  usd('USD'),

  /// 人民币
  cny('CNY'),

  /// 欧元
  eur('EUR'),

  /// 英镑
  gbp('GBP'),

  /// 日元
  jpy('JPY'),

  /// 加拿大元
  cad('CAD'),

  /// 澳大利亚元
  aud('AUD'),

  /// 瑞士法郎
  chf('CHF');

  const Currency(this.code);

  /// 货币代码
  final String code;

  /// 从字符串代码获取货币枚举
  static Currency? fromCode(String code) {
    for (final currency in Currency.values) {
      if (currency.code.toLowerCase() == code.toLowerCase()) {
        return currency;
      }
    }
    return null;
  }

  /// 获取货币符号
  String get symbol {
    switch (this) {
      case Currency.usd:
        return '\$';
      case Currency.cny:
        return '¥';
      case Currency.eur:
        return '€';
      case Currency.gbp:
        return '£';
      case Currency.jpy:
        return '¥';
      case Currency.cad:
        return 'C\$';
      case Currency.aud:
        return 'A\$';
      case Currency.chf:
        return 'CHF';
    }
  }
}

/// 支持的语言枚举
///
/// 定义了模型支持的所有语言类型
enum SupportedLanguage {
  /// 中文（简体）
  chineseSimplified('zh-CN'),

  /// 中文（繁体）
  chineseTraditional('zh-TW'),

  /// 英文
  english('en'),

  /// 日文
  japanese('ja'),

  /// 韩文
  korean('ko'),

  /// 法文
  french('fr'),

  /// 德文
  german('de'),

  /// 西班牙文
  spanish('es'),

  /// 意大利文
  italian('it'),

  /// 葡萄牙文
  portuguese('pt'),

  /// 俄文
  russian('ru'),

  /// 阿拉伯文
  arabic('ar'),

  /// 印地文
  hindi('hi'),

  /// 泰文
  thai('th'),

  /// 越南文
  vietnamese('vi'),

  /// 荷兰文
  dutch('nl'),

  /// 瑞典文
  swedish('sv'),

  /// 挪威文
  norwegian('no'),

  /// 丹麦文
  danish('da'),

  /// 芬兰文
  finnish('fi'),

  /// 波兰文
  polish('pl'),

  /// 土耳其文
  turkish('tr'),

  /// 希腊文
  greek('el'),

  /// 希伯来文
  hebrew('he'),

  /// 印尼文
  indonesian('id'),

  /// 马来文
  malay('ms');

  const SupportedLanguage(this.code);

  /// 语言代码
  final String code;

  /// 从语言代码获取枚举
  static SupportedLanguage? fromCode(String code) {
    for (final language in SupportedLanguage.values) {
      if (language.code.toLowerCase() == code.toLowerCase()) {
        return language;
      }
    }
    return null;
  }

  /// 获取语言显示名称
  String get displayName {
    switch (this) {
      case SupportedLanguage.chineseSimplified:
        return '中文（简体）';
      case SupportedLanguage.chineseTraditional:
        return '中文（繁体）';
      case SupportedLanguage.english:
        return 'English';
      case SupportedLanguage.japanese:
        return '日本語';
      case SupportedLanguage.korean:
        return '한국어';
      case SupportedLanguage.french:
        return 'Français';
      case SupportedLanguage.german:
        return 'Deutsch';
      case SupportedLanguage.spanish:
        return 'Español';
      case SupportedLanguage.italian:
        return 'Italiano';
      case SupportedLanguage.portuguese:
        return 'Português';
      case SupportedLanguage.russian:
        return 'Русский';
      case SupportedLanguage.arabic:
        return 'العربية';
      case SupportedLanguage.hindi:
        return 'हिन्दी';
      case SupportedLanguage.thai:
        return 'ไทย';
      case SupportedLanguage.vietnamese:
        return 'Tiếng Việt';
      case SupportedLanguage.dutch:
        return 'Nederlands';
      case SupportedLanguage.swedish:
        return 'Svenska';
      case SupportedLanguage.norwegian:
        return 'Norsk';
      case SupportedLanguage.danish:
        return 'Dansk';
      case SupportedLanguage.finnish:
        return 'Suomi';
      case SupportedLanguage.polish:
        return 'Polski';
      case SupportedLanguage.turkish:
        return 'Türkçe';
      case SupportedLanguage.greek:
        return 'Ελληνικά';
      case SupportedLanguage.hebrew:
        return 'עברית';
      case SupportedLanguage.indonesian:
        return 'Bahasa Indonesia';
      case SupportedLanguage.malay:
        return 'Bahasa Melayu';
    }
  }
}

/// Extension methods for [Set<ModelCapability>] to provide convenient capability checks
extension ModelCapabilitySetExtension on Set<ModelCapability> {
  /// 检查是否支持图像输入
  bool get supportsImageInput => contains(ModelCapability.imageInput);
  
  /// 检查是否支持函数调用
  bool get supportsFunctionCalling => contains(ModelCapability.functionCalling);
  
  /// 检查是否支持嵌入
  bool get supportsEmbeddings => contains(ModelCapability.embeddings);
  
  /// 检查是否支持流式输出
  bool get supportsStreaming => contains(ModelCapability.streaming);
  
  /// 检查是否支持系统消息
  bool get supportsSystemMessages => contains(ModelCapability.systemMessages);
  
  /// 检查是否支持音频输入
  bool get supportsAudioInput => contains(ModelCapability.audioInput);
  
  /// 检查是否支持视频输入
  bool get supportsVideoInput => contains(ModelCapability.videoInput);
  
  /// 检查是否支持任意多模态输入
  bool get supportsMultimodalInput => 
      supportsImageInput || supportsAudioInput || supportsVideoInput;
      
  /// 检查是否支持内容生成
  bool get supportsContentGeneration => 
      contains(ModelCapability.imageGeneration) || 
      contains(ModelCapability.audioGeneration) || 
      contains(ModelCapability.videoGeneration);
}