import 'model_enums.dart';

/// Information about a specific model
class ModelInfo {
  const ModelInfo({
    required this.name,
    this.version,
    this.description,
    this.family,
    this.releaseDate,
    required this.capabilities,
    this.inputTokens,
    this.outputTokens,
    this.totalTokens,
    this.currency = Currency.usd,
    this.pricingTier,
    this.maxTokens,
    this.maxInputTokens,
    this.maxOutputTokens,
    this.supportedLanguages = const {SupportedLanguage.english},
  });

  /// 模型名称（如 "gpt-4-turbo"）
  final String name;

  final String? version;
  final String? description;
  final String? family;

  final DateTime? releaseDate;

  /// 模型能力列表
  final Set<ModelCapability> capabilities;

  /// 定价信息
  /// 输入Token价格（每1K tokens）
  final double? inputTokens;

  /// 输出Token价格（每1K tokens）
  final double? outputTokens;

  /// 总Token价格（每1K tokens）
  final double? totalTokens;

  /// 货币单位
  final Currency currency;

  /// 定价层级
  final PricingTier? pricingTier;

  /// Token限制
  /// 最大Token数
  final int? maxTokens;

  /// 最大输入Token数
  final int? maxInputTokens;

  /// 最大输出Token数
  final int? maxOutputTokens;

  /// 支持的语言集合
  final Set<SupportedLanguage> supportedLanguages;

  /// 从JSON创建LlmModel
  factory ModelInfo.fromJson(Map<String, dynamic> json) {
    return ModelInfo(
      name: json['name'] as String,
      version: json['version'] as String?,
      description: json['description'] as String?,
      family: json['family'] as String?,
      releaseDate: json['releaseDate'] != null
          ? DateTime.parse(json['releaseDate'] as String)
          : null,
      capabilities: (json['capabilities'] as List<dynamic>? ?? [])
          .map((e) => ModelCapability.fromString(e as String))
          .where((e) => e != null)
          .cast<ModelCapability>()
          .toSet(),
      inputTokens: json['inputTokens'] as double?,
      outputTokens: json['outputTokens'] as double?,
      totalTokens: json['totalTokens'] as double?,
      currency: Currency.fromCode(json['currency'] as String? ?? 'USD') ??
          Currency.usd,
      pricingTier: json['pricingTier'] != null
          ? PricingTier.fromString(json['pricingTier'] as String)
          : null,
      maxTokens: json['maxTokens'] as int?,
      maxInputTokens: json['maxInputTokens'] as int?,
      maxOutputTokens: json['maxOutputTokens'] as int?,
      supportedLanguages:
          (json['supportedLanguages'] as List<dynamic>? ?? ['en'])
              .map((e) => SupportedLanguage.fromCode(e as String))
              .where((e) => e != null)
              .cast<SupportedLanguage>()
              .toSet(),
    );
  }

  /// 获取完整的模型标识符
  String get fullId => version != null ? '$name:$version' : name;

  /// 获取显示名称
  String get displayName => version != null ? '$name ($version)' : name;

  /// 检查模型是否支持特定能力
  bool hasCapability(ModelCapability capability) {
    return capabilities.contains(capability);
  }

  /// 检查模型是否支持多个能力
  bool hasAllCapabilities(Set<ModelCapability> requiredCapabilities) {
    return requiredCapabilities.every(hasCapability);
  }

  /// 检查模型是否支持任意一个能力
  bool hasAnyCapability(Set<ModelCapability> anyCapabilities) {
    return anyCapabilities.any(hasCapability);
  }

  /// 计算预估成本
  double? calculateCost({
    int? inputTokenCount,
    int? outputTokenCount,
    int? totalTokenCount,
  }) {
    double totalCost = 0.0;
    bool hasPricing = false;

    // 优先使用分别计价
    if (inputTokenCount != null && inputTokens != null) {
      totalCost += (inputTokenCount / 1000) * inputTokens!;
      hasPricing = true;
    }

    if (outputTokenCount != null && outputTokens != null) {
      totalCost += (outputTokenCount / 1000) * outputTokens!;
      hasPricing = true;
    }

    // 如果没有分别计价，使用总价格
    if (!hasPricing && totalTokenCount != null && totalTokens != null) {
      totalCost = (totalTokenCount / 1000) * totalTokens!;
      hasPricing = true;
    }

    return hasPricing ? totalCost : null;
  }

  /// 获取有效的最大Token数
  int? get effectiveMaxTokens => maxTokens ?? maxOutputTokens;

  /// 获取有效的最大输入Token数
  int? get effectiveMaxInputTokens => maxInputTokens ?? maxTokens;

  /// 是否支持多模态
  bool get isMultimodal {
    return hasAnyCapability({
      ModelCapability.imageInput,
      ModelCapability.audioInput,
      ModelCapability.videoInput,
    });
  }

  /// 是否支持内容生成
  bool get supportsContentGeneration {
    return hasAnyCapability({
      ModelCapability.imageGeneration,
      ModelCapability.audioGeneration,
      ModelCapability.videoGeneration,
    });
  }

  /// 创建副本
  ModelInfo copyWith({
    String? name,
    String? version,
    String? description,
    String? family,
    DateTime? releaseDate,
    Set<ModelCapability>? capabilities,
    double? inputTokens,
    double? outputTokens,
    double? totalTokens,
    Currency? currency,
    PricingTier? pricingTier,
    int? maxTokens,
    int? maxInputTokens,
    int? maxOutputTokens,
    Set<SupportedLanguage>? supportedLanguages,
  }) {
    return ModelInfo(
      name: name ?? this.name,
      version: version ?? this.version,
      description: description ?? this.description,
      family: family ?? this.family,
      releaseDate: releaseDate ?? this.releaseDate,
      capabilities: capabilities ?? this.capabilities,
      inputTokens: inputTokens ?? this.inputTokens,
      outputTokens: outputTokens ?? this.outputTokens,
      totalTokens: totalTokens ?? this.totalTokens,
      currency: currency ?? this.currency,
      pricingTier: pricingTier ?? this.pricingTier,
      maxTokens: maxTokens ?? this.maxTokens,
      maxInputTokens: maxInputTokens ?? this.maxInputTokens,
      maxOutputTokens: maxOutputTokens ?? this.maxOutputTokens,
      supportedLanguages: supportedLanguages ?? this.supportedLanguages,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'version': version,
      'description': description,
      'family': family,
      'releaseDate': releaseDate?.toIso8601String(),
      'capabilities': capabilities.map((e) => e.value).toList(),
      'inputTokens': inputTokens,
      'outputTokens': outputTokens,
      'totalTokens': totalTokens,
      'currency': currency.code,
      'pricingTier': pricingTier?.value,
      'maxTokens': maxTokens,
      'maxInputTokens': maxInputTokens,
      'maxOutputTokens': maxOutputTokens,
      'supportedLanguages': supportedLanguages.map((e) => e.code).toList(),
      'fullId': fullId,
      'displayName': displayName,
      'isMultimodal': isMultimodal,
      'supportsContentGeneration': supportsContentGeneration,
    };
  }

  @override
  String toString() => displayName;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ModelInfo &&
        other.name == name &&
        other.version == version;
  }

  @override
  int get hashCode {
    return Object.hash(name, version);
  }
}

/// 请求使用情况跟踪
class RequestUsage {
  RequestUsage({
    this.promptTokens,
    this.completionTokens,
    this.totalTokens,
    this.responseTime,
    DateTime? timestamp,
    this.additionalMetrics = const {},
  }) : timestamp = timestamp ?? DateTime.now();

  factory RequestUsage.fromJson(Map<String, dynamic> json) {
    return RequestUsage(
      promptTokens: json['prompt_tokens'] as int?,
      completionTokens: json['completion_tokens'] as int?,
      totalTokens: json['total_tokens'] as int?,
      responseTime: json['responseTime'] != null
          ? Duration(milliseconds: json['responseTime'] as int)
          : null,
      timestamp: json['timestamp'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['timestamp'] as int)
          : DateTime.now(),
      additionalMetrics:
          json['additionalMetrics'] as Map<String, dynamic>? ?? {},
    );
  }

  final int? promptTokens;
  final int? completionTokens;
  final int? totalTokens;
  final Duration? responseTime;
  final DateTime timestamp;
  final Map<String, dynamic> additionalMetrics;

  /// 获取有效的总Token数
  int get effectiveTotalTokens =>
      totalTokens ?? ((promptTokens ?? 0) + (completionTokens ?? 0));

  /// 使用模型定价计算成本
  double? calculateCost(ModelInfo model) {
    return model.calculateCost(
      inputTokenCount: promptTokens,
      outputTokenCount: completionTokens,
      totalTokenCount: effectiveTotalTokens,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'promptTokens': promptTokens,
      'completionTokens': completionTokens,
      'totalTokens': totalTokens,
      'effectiveTotalTokens': effectiveTotalTokens,
      'responseTime': responseTime?.inMilliseconds,
      'timestamp': timestamp.millisecondsSinceEpoch,
      'additionalMetrics': additionalMetrics,
    };
  }

  @override
  String toString() {
    return 'RequestUsage(${effectiveTotalTokens} tokens, ${responseTime?.inMilliseconds ?? 0}ms)';
  }
}

/// 模型注册表，用于管理可用模型
class CoreModelRegistry {
  final Map<String, ModelInfo> _models = {};
  final Map<String, List<ModelInfo>> _modelsByFamily = {};

  /// 注册模型
  void registerModel(ModelInfo model) {
    _models[model.fullId] = model;
    if (model.family != null) {
      _modelsByFamily.putIfAbsent(model.family!, () => []).add(model);
    }
  }

  /// 根据完整ID获取模型
  ModelInfo? getModel(String fullId) {
    return _models[fullId];
  }

  /// 根据模型名称或完整ID获取模型
  /// 如果传入的是模型名称，返回该名称下的第一个活跃模型
  /// 如果传入的是完整ID，直接返回对应模型
  ModelInfo? getModelByNameOrId(String nameOrId) {
    // 首先尝试作为完整ID查找
    final byId = _models[nameOrId];
    if (byId != null) {
      return byId;
    }

    // 然后尝试作为模型名称查找，优先返回活跃模型
    final byName =
        _models.values.where((model) => model.name == nameOrId).toList();

    if (byName.isNotEmpty) {
      return byName.first;
    }

    // 如果没有活跃模型，返回第一个匹配的模型
    final anyByName =
        _models.values.where((model) => model.name == nameOrId).toList();

    return anyByName.isNotEmpty ? anyByName.first : null;
  }

  /// 根据模型名称获取模型（可能返回多个版本）
  List<ModelInfo> getModelsByName(String name) {
    return _models.values.where((model) => model.name == name).toList();
  }

  /// 获取所有模型
  List<ModelInfo> getAllModels() {
    return _models.values.toList();
  }


  /// 根据模型家族获取模型
  List<ModelInfo> getModelsByFamily(String family) {
    return _modelsByFamily[family] ?? [];
  }


  /// 获取所有模型家族
  List<String> getFamilies() {
    return _modelsByFamily.keys.toList();
  }

  /// 查找支持特定能力的模型
  List<ModelInfo> findModelsWithCapability(ModelCapability capability) {
    return _models.values
        .where((model) => model.hasCapability(capability))
        .toList();
  }

  /// 查找支持所有指定能力的模型
  List<ModelInfo> findModelsWithAllCapabilities(
      Set<ModelCapability> capabilities) {
    return _models.values
        .where((model) => model.hasAllCapabilities(capabilities))
        .toList();
  }

  /// 查找支持任意一个指定能力的模型
  List<ModelInfo> findModelsWithAnyCapability(
      Set<ModelCapability> capabilities) {
    return _models.values
        .where((model) => model.hasAnyCapability(capabilities))
        .toList();
  }

  /// 查找在Token限制范围内的模型
  List<ModelInfo> findModelsWithTokenLimit(int minTokens) {
    return _models.values
        .where((model) => (model.effectiveMaxTokens ?? 0) >= minTokens)
        .toList();
  }

  /// 查找支持特定语言的模型
  List<ModelInfo> findModelsWithLanguage(SupportedLanguage language) {
    return _models.values
        .where((model) => model.supportedLanguages.contains(language))
        .toList();
  }

  /// 查找特定定价层级的模型
  List<ModelInfo> findModelsByPricingTier(PricingTier tier) {
    return _models.values.where((model) => model.pricingTier == tier).toList();
  }

  /// 查找最经济高效的模型（按输入Token价格排序）
  List<ModelInfo> findMostCostEffectiveModels({
    Set<ModelCapability>? requiredCapabilities,
    int? minTokenLimit,
    SupportedLanguage? language,
  }) {
    var models = _models.values.where((model) => model.inputTokens != null);

    if (requiredCapabilities != null) {
      models = models
          .where((model) => model.hasAllCapabilities(requiredCapabilities));
    }

    if (minTokenLimit != null) {
      models = models
          .where((model) => (model.effectiveMaxTokens ?? 0) >= minTokenLimit);
    }

    if (language != null) {
      models =
          models.where((model) => model.supportedLanguages.contains(language));
    }

    final modelList = models.toList();
    modelList.sort((a, b) => (a.inputTokens ?? double.infinity)
        .compareTo(b.inputTokens ?? double.infinity));

    return modelList;
  }

  /// 移除模型
  bool removeModel(String fullId) {
    final model = _models.remove(fullId);
    if (model != null) {
      if (model.family != null) {
        _modelsByFamily[model.family!]?.remove(model);
      }
      return true;
    }
    return false;
  }

  /// 清空所有模型
  void clear() {
    _models.clear();
    _modelsByFamily.clear();
  }

  /// 获取注册表统计信息
  Map<String, dynamic> getStats() {
    final capabilityStats = <String, int>{};
    final familyStats = <String, int>{};
    final languageStats = <String, int>{};
    final pricingTierStats = <String, int>{};

    int modelsWithPricing = 0;

    for (final model in _models.values) {
      if (model.inputTokens != null ||
          model.outputTokens != null ||
          model.totalTokens != null) {
        modelsWithPricing++;
      }

      // 统计家族
      if (model.family != null) {
        familyStats[model.family!] = (familyStats[model.family!] ?? 0) + 1;
      }

      // 统计能力
      for (final capability in model.capabilities) {
        capabilityStats[capability.value] =
            (capabilityStats[capability.value] ?? 0) + 1;
      }

      // 统计语言
      for (final language in model.supportedLanguages) {
        languageStats[language.code] = (languageStats[language.code] ?? 0) + 1;
      }

      // 统计定价层级
      if (model.pricingTier != null) {
        pricingTierStats[model.pricingTier!.value] =
            (pricingTierStats[model.pricingTier!.value] ?? 0) + 1;
      }
    }

    return {
      'totalModels': _models.length,
      'modelsWithPricing': modelsWithPricing,
      'totalFamilies': _modelsByFamily.length,
      'modelsByFamily': familyStats,
      'modelsByCapability': capabilityStats,
      'modelsByLanguage': languageStats,
      'modelsByPricingTier': pricingTierStats,
    };
  }
}
