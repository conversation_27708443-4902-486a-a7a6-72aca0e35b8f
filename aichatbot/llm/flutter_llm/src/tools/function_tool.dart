import 'package:cancellation_token/src/tokens/cancellation_token.dart';
import '../models/tool_schema.dart';

import 'tool.dart';

/// Function tool that wraps annotated functions with schema and parameter info
///
/// This is a simpler implementation that requires manual schema provision
/// but avoids complex reflection. It's designed to work with code generation.
///
/// Usage example:
/// ```dart
/// @ToolFunction('Process user data')
/// Future<String> processUserData(
///     @Param("User information") String userData,
///     @Param("List of tags to apply") List<String> tags,
/// ) async {
///   return 'Processed: $userData with tags: ${tags.join(", ")}';
/// }
///
/// final tool = FunctionTool(
///   function: processUserData,
///   schema: ToolSchema(
///     name: 'processUserData',
///     description: 'Process user data',
///     parameters: ParametersSchema(
///       type: 'object',
///       properties: {
///         'userData': PropertySchema(type: 'string', description: 'User information'),
///         'tags': PropertySchema(type: 'array', description: 'List of tags to apply'),
///       },
///       required: ['userData', 'tags'],
///     ),
///   ),
///   parameterConverter: (args) => [args['userData'], args['tags']],
/// );
/// ```
class FunctionTool extends BaseTool<Map<String, dynamic>, dynamic> {
  FunctionTool({
    required ToolSchema schema,
    List<dynamic> Function(Map<String, dynamic> args)? parameterConverter,
    required Function function,
  })  : _function = function,
        _schema = schema,
        _parameterConverter = parameterConverter ?? _defaultParameterConverter;

  final Function _function;
  final ToolSchema _schema;
  final List<dynamic> Function(Map<String, dynamic> args) _parameterConverter;

  static List<dynamic> _defaultParameterConverter(Map<String, dynamic> args) {
    return args.values.toList();
  }

  @override
  ToolSchema generateSchema() {
    return _schema;
  }

  @override
  Map<String, dynamic> parseArgs(Map<String, dynamic> args) {
    return args;
  }

  @override
  Future<dynamic> run(
      Map<String, dynamic> args, CancellationToken cancellationToken) async {
    try {
      // Convert args to parameter list
      final parameters = _parameterConverter(args);

      // Call the function using Function.apply
      final result = Function.apply(_function, parameters);

      // Handle Future return types
      if (result is Future) {
        return await result;
      }

      return result;
    } catch (e) {
      throw Exception('Failed to execute function: $e');
    }
  }
}

/// Simple helper function to create FunctionTool from basic function
/// For more complex scenarios, use the full constructor
FunctionTool createSimpleFunctionTool(
  Function function, {
  required String name,
  required String description,
  Map<String, PropertySchema>? parameters,
  List<String>? requiredParameters,
  List<dynamic> Function(Map<String, dynamic> args)? parameterConverter,
}) {
  final schema = ToolSchema(
    name: name,
    description: description,
    parameters: ParametersSchema(
      type: 'object',
      properties: parameters ?? {},
      required: requiredParameters,
      additionalProperties: false,
    ),
  );

  return FunctionTool(
      schema: schema,
      parameterConverter: parameterConverter,
      function: function);
}
