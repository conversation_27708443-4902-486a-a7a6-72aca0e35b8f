/// Simplified annotations for function tools
///
/// This provides a minimal annotation system focused on core functionality,
/// removing the complexity of the previous implementation.

/// Annotation for marking functions to be wrapped as tools
///
/// Usage:
/// ```dart
/// @ToolFunction('Add two numbers together')
/// Future<double> addNumbers(double a, double b) async {
///   return a + b;
/// }
/// ```
class ToolFunction {
  const ToolFunction(
    this.description, {
    this.name,
  });

  /// Description of what the tool does (required)
  final String description;

  /// Optional custom name (defaults to function name)
  final String? name;
}

/// Annotation for parameter descriptions
///
/// Usage:
/// ```dart
/// @ToolFunction('Process user data')
/// Future<String> processUser(
///   String username,
///   @Param("User's age in years") int age,
///   @Param("User's email", name: "email_address") String email,
/// ) async {
///   return 'Processed: $username ($age years old)';
/// }
/// ```
class Param {
  const Param(this.description, {this.name});

  /// Description of what the parameter does
  final String description;

  /// Optional custom name for the parameter (defaults to parameter name)
  final String? name;
}
