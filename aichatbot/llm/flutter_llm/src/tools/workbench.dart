import 'dart:async';

import 'package:cancellation_token/cancellation_token.dart';

import '../models/tool_schema.dart';
import 'tool.dart';

/// 工具包装器，支持名称override
class _ToolWrapper implements Tool, StreamTool {
  const _ToolWrapper(this._tool, this._overrideName);

  final Tool _tool;
  final String _overrideName;

  @override
  ToolSchema get schema {
    final originalSchema = _tool.schema;
    return ToolSchema(
      name: _overrideName,
      description: originalSchema.description,
      parameters: originalSchema.parameters,
      strict: originalSchema.strict,
    );
  }

  @override
  String get name => _overrideName;

  @override
  String? get description => _tool.description;

  @override
  Future<dynamic> runJson(String args, CancellationToken cancellationToken,
      {String? callId}) {
    return _tool.runJson(args, cancellationToken, callId: callId);
  }

  @override
  String returnValueAsString(dynamic value) {
    return _tool.returnValueAsString(value);
  }

  @override
  Stream<dynamic> runJsonStream(
      String args, CancellationToken cancellationToken,
      {String? callId}) {
    if (_tool is StreamTool) {
      return (_tool as StreamTool)
          .runJsonStream(args, cancellationToken, callId: callId);
    } else {
      // 对于非流工具，返回单一结果的流
      return _tool.runJson(args, cancellationToken, callId: callId).asStream();
    }
  }
}

/// Result of a tool execution
class ToolResult {
  const ToolResult({
    required this.content,
    required this.tool,
    this.callId,
  });

  /// The result content (can be any type)
  final dynamic content;

  /// Optional call ID for tracking
  final String? callId;

  final Tool tool;

  /// Convert result to string representation
  String asString() {
    return tool.returnValueAsString(content);
  }
}

/// Simple workbench for executing tools
///
/// This workbench supports both regular and streaming tools, matching
/// the Python Workbench functionality.
class Workbench {
  /// 主构造函数：支持工具实例和/或函数
  Workbench({
    List<Tool>? tools,
    Map<String, String>? toolOverrides,
  }) : _toolOverrides = toolOverrides ?? {} {
    if (tools != null) {
      _addTools(tools);
    }
    if (_tools.isEmpty) {
      throw ArgumentError('At least one tool or function must be provided');
    }
  }

  /// 便利构造函数：只接受工具（向后兼容）
  Workbench.fromTools(List<Tool> tools) : this(tools: tools);

  /// 便利构造函数：支持工具名称override
  Workbench.withOverrides({
    required List<Tool> tools,
    required Map<String, String> overrides,
  }) : this(tools: tools, toolOverrides: overrides);

  final Map<String, Tool> _tools = {};
  final Map<String, String> _toolOverrides;

  /// 添加工具实例
  void _addTools(List<Tool> tools) {
    for (final tool in tools) {
      final overrideName = _toolOverrides[tool.name];
      if (overrideName != null) {
        // 如果有override，使用包装器
        _tools[overrideName] = _ToolWrapper(tool, overrideName);
      } else {
        // 否则直接使用原工具
        _tools[tool.name] = tool;
      }
    }
  }

  /// Get all available tools
  List<Tool> get tools => _tools.values.toList();

  /// Get tool schemas for LLM integration
  List<ToolSchema> get schemas => tools.map((tool) => tool.schema).toList();

  /// Check if a tool with the given name exists
  bool hasTool(String name) => _tools.containsKey(name);

  /// Get a tool by name
  Tool? getTool(String name) => _tools[name];

  /// Execute a tool by name with JSON arguments
  Future<ToolResult> callTool({
    required String name,
    required String arguments,
    CancellationToken? cancellationToken,
    String? callId,
  }) async {
    final tool = _tools[name];
    if (tool == null) {
      throw ArgumentError('Tool not found: $name');
    }

    final result = await tool.runJson(
      arguments,
      cancellationToken ?? CancellationToken(),
      callId: callId,
    );

    return ToolResult(content: result, tool: tool, callId: callId);
  }

  /// Execute a streaming tool by name with JSON arguments
  Stream<dynamic> callToolStream({
    required String name,
    required String arguments,
    CancellationToken? cancellationToken,
    String? callId,
  }) async* {
    final tool = _tools[name];
    if (tool == null) {
      throw ArgumentError('Tool not found: $name');
    }

    if (tool is StreamTool) {
      await for (final result in tool.runJsonStream(
        arguments,
        cancellationToken ?? CancellationToken(),
        callId: callId,
      )) {
        yield result;
      }
    } else {
      // For non-streaming tools, just yield the final result
      final result = await tool.runJson(
        arguments,
        cancellationToken ?? CancellationToken(),
        callId: callId,
      );
      yield result;
    }
  }

  /// Check if a tool supports streaming
  bool isStreamingTool(String name) {
    final tool = _tools[name];
    return tool is StreamTool;
  }
}
