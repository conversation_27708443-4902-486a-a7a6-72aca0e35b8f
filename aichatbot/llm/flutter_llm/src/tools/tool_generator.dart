/// Simplified code generator for FunctionTool wrappers
///
/// This generator creates complete tool classes from @ToolFunction annotated
/// functions, providing the same zero-configuration experience as Python's
/// FunctionTool while maintaining type safety.
library tool_generator;

import 'dart:async';

import 'package:analyzer/dart/element/element.dart';
import 'package:analyzer/dart/element/nullability_suffix.dart';
import 'package:analyzer/dart/element/type.dart';
import 'package:build/build.dart';
import 'package:source_gen/source_gen.dart';

import 'annotations.dart';

/// Builder for function tool code generation
Builder functionToolGenerator(BuilderOptions options) {
  return LibraryBuilder(FunctionToolGenerator(),
      generatedExtension: '.tool.dart');
}

/// Generator that creates complete FunctionTool implementations
class FunctionToolGenerator extends Generator {
  @override
  Future<String> generate(LibraryReader library, BuildStep buildStep) async {
    final functions = <_FunctionAnalysis>[];

    // 收集所有带@ToolFunction注解的函数
    for (final element in library.allElements) {
      if (element is ExecutableElement) {
        final annotation = const TypeChecker.fromRuntime(ToolFunction)
            .firstAnnotationOf(element);
        if (annotation != null) {
          final annotationReader = ConstantReader(annotation);
          final analysis = _analyzeFunction(element, annotationReader);
          functions.add(analysis);
        }
      }
    }

    if (functions.isEmpty) {
      return ''; // 没有找到任何带注解的函数
    }

    // 生成完整的工具文件
    return _generateCompleteToolFile(functions, library.element.source.uri);
  }

  /// 生成完整的工具文件
  String _generateCompleteToolFile(
      List<_FunctionAnalysis> functions, Uri sourceUri) {
    final fileName = sourceUri.pathSegments.last.replaceAll('.dart', '');
    final imports = '''
// GENERATED CODE - DO NOT MODIFY BY HAND
// Generated by function_tool_generator

import 'dart:async';
import 'package:cancellation_token/cancellation_token.dart';
import 'tool.dart';
import '../models/tool_schema.dart';
import '$fileName.dart'; // Import original functions

''';

    final toolClasses = functions.map(_generateSingleTool).join('\n\n');
    final mappingFunction = _generateFunctionMapping(functions, fileName);

    return '''$imports$toolClasses

$mappingFunction
''';
  }

  /// 为单个函数生成工具代码
  String _generateSingleTool(_FunctionAnalysis analysis) {
    return '''
// ============== ${analysis.functionName} 工具 ==============

${_generateArgsClass(analysis)}

${_generateToolClass(analysis)}''';
  }

  /// Analyze function and extract metadata
  _FunctionAnalysis _analyzeFunction(
    ExecutableElement function,
    ConstantReader annotation,
  ) {
    final toolName = annotation.read('name').literalValue as String? ??
        _camelToSnake(function.name);
    final description = annotation.read('description').literalValue as String;

    final parameters = function.parameters
        .where((p) =>
            p.type.getDisplayString(withNullability: false) !=
            'CancellationToken')
        .toList();

    final returnType = function.returnType;
    final isAsync =
        returnType.isDartAsyncFuture || returnType.isDartAsyncFutureOr;

    return _FunctionAnalysis(
      functionName: function.name,
      toolName: toolName,
      description: description,
      parameters: parameters,
      returnType: _getCleanTypeName(returnType),
      isAsync: isAsync,
      toolClassName: '\$${_capitalize(function.name)}Tool', // 使用 $ 前缀（公共类）
      argsClassName: '\$${_capitalize(function.name)}Args', // 使用 $ 前缀（公共类）
    );
  }

  /// Generate typed arguments class
  String _generateArgsClass(_FunctionAnalysis analysis) {
    if (analysis.parameters.isEmpty) {
      return '''
/// Arguments class for ${analysis.functionName} (no parameters)
class ${analysis.argsClassName} {
  const ${analysis.argsClassName}();
  
  factory ${analysis.argsClassName}.fromJson(Map<String, dynamic> json) {
    return const ${analysis.argsClassName}();
  }
  
  Map<String, dynamic> toJson() => <String, dynamic>{};
}''';
    }

    final fields = <String>[];
    final constructorParams = <String>[];
    final fromJsonParams = <String>[];
    final toJsonEntries = <String>[];

    for (final param in analysis.parameters) {
      final fieldName = _getParameterName(param);
      final actualParamName = param.name; // 保留实际参数名用于字段名
      final fieldType = _getCleanTypeName(param.type);
      final isRequired =
          param.type.nullabilitySuffix != NullabilitySuffix.question &&
              !param.hasDefaultValue;
      final description = _getParameterDescription(param);

      // Field declaration - 使用实际参数名作为字段名
      fields.add('  /// $description');
      fields.add('  final $fieldType $actualParamName;');

      // Constructor parameter - 使用实际参数名
      if (isRequired) {
        constructorParams.add('    required this.$actualParamName,');
      } else {
        constructorParams.add('    this.$actualParamName,');
      }

      // fromJson parsing - 支持自定义名和原参数名
      final jsonConversion =
          _generateJsonConversion(fieldName, actualParamName, param.type);
      fromJsonParams.add('      $actualParamName: $jsonConversion,');

      // toJson entry - JSON键名使用自定义名，值使用实际参数名
      toJsonEntries.add('      \'$fieldName\': $actualParamName,');
    }

    return '''
/// Arguments class for ${analysis.functionName}
class ${analysis.argsClassName} {
${fields.join('\n')}

  const ${analysis.argsClassName}({
${constructorParams.join('\n')}
  });

  factory ${analysis.argsClassName}.fromJson(Map<String, dynamic> json) {
    return ${analysis.argsClassName}(
${fromJsonParams.join('\n')}
    );
  }

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
${toJsonEntries.join('\n')}
    };
  }
}''';
  }

  /// Generate complete tool class
  String _generateToolClass(_FunctionAnalysis analysis) {
    final schemaProperties = _generateSchemaProperties(analysis);
    final runMethod = _generateRunMethod(analysis);

    return '''
/// Generated tool for ${analysis.functionName}
class ${analysis.toolClassName} extends BaseTool<${analysis.argsClassName}, ${analysis.returnType}> {
  ${analysis.toolClassName}();

  @override
  ToolSchema generateSchema() {
    return ToolSchema(
      name: '${analysis.toolName}',
      description: '${_escapeString(analysis.description)}',
      parameters: ParametersSchema(
        type: 'object',
        properties: {
$schemaProperties
        },
        required: [${_generateRequiredList(analysis)}],
        additionalProperties: false,
      ),
    );
  }

$runMethod

  @override
  ${analysis.argsClassName} parseArgs(Map<String, dynamic> args) {
    return ${analysis.argsClassName}.fromJson(args);
  }
}''';
  }

  /// Generate schema properties
  String _generateSchemaProperties(_FunctionAnalysis analysis) {
    if (analysis.parameters.isEmpty) return '';

    final properties = analysis.parameters.map((param) {
      final paramName = _getParameterName(param);
      final description = _getParameterDescription(param);
      final jsonType = _getJsonSchemaType(param.type);

      return '''          '$paramName': PropertySchema(
            type: '$jsonType',
            description: '${_escapeString(description)}',
          )''';
    });

    return properties.join(',\n');
  }

  /// Generate required parameters list
  String _generateRequiredList(_FunctionAnalysis analysis) {
    final required = analysis.parameters
        .where((p) =>
            p.type.nullabilitySuffix != NullabilitySuffix.question &&
            !p.hasDefaultValue)
        .map((p) => "'${_getParameterName(p)}'");

    return required.join(', ');
  }

  /// Generate run method implementation
  String _generateRunMethod(_FunctionAnalysis analysis) {
    final functionCall = _generateFunctionCall(analysis);

    if (analysis.isAsync) {
      return '''
  @override
  Future<${analysis.returnType}> run(${analysis.argsClassName} args, CancellationToken cancellationToken) async {
    return await $functionCall;
  }''';
    } else {
      return '''
  @override
  Future<${analysis.returnType}> run(${analysis.argsClassName} args, CancellationToken cancellationToken) async {
    return $functionCall;
  }''';
    }
  }

  /// Generate function call with proper arguments
  String _generateFunctionCall(_FunctionAnalysis analysis) {
    if (analysis.parameters.isEmpty) {
      return '${analysis.functionName}()';
    }

    // 使用实际参数名访问args对象的字段
    final argsList =
        analysis.parameters.map((p) => 'args.${p.name}').join(', ');

    return '${analysis.functionName}($argsList)';
  }

  /// Utility methods
  String _getParameterDescription(ParameterElement param) {
    // Check for @Param annotation
    for (final annotation in param.metadata) {
      if (annotation.element?.displayName == 'Param') {
        try {
          final constantValue = annotation.computeConstantValue();
          final description =
              constantValue?.getField('description')?.toStringValue();
          if (description != null) return description;
        } catch (e) {
          // Ignore annotation parsing errors
        }
      }
    }

    // Fallback to parameter name with basic formatting
    return _camelToSentence(param.name);
  }

  String _getParameterName(ParameterElement param) {
    // Check for @Param annotation with custom name
    for (final annotation in param.metadata) {
      if (annotation.element?.displayName == 'Param') {
        try {
          final constantValue = annotation.computeConstantValue();
          final customName = constantValue?.getField('name')?.toStringValue();
          if (customName != null) return customName;
        } catch (e) {
          // Ignore annotation parsing errors
        }
      }
    }

    // Fallback to actual parameter name
    return param.name;
  }

  String _generateJsonConversion(
      String customName, String originalName, DartType type) {
    final typeString = type.getDisplayString(withNullability: false);
    final nullable =
        type.nullabilitySuffix == NullabilitySuffix.question ? '?' : '';

    // 生成获取值的表达式，优先使用自定义名，回退到原参数名
    String getValueExpression(String conversion) {
      if (customName == originalName) {
        // 如果自定义名和原参数名相同，直接使用
        return 'json[\'$customName\'] $conversion';
      } else {
        // 否则，先尝试自定义名，再尝试原参数名
        return '(json[\'$customName\'] ?? json[\'$originalName\']) $conversion';
      }
    }

    switch (typeString) {
      case 'String':
        return getValueExpression('as String$nullable');
      case 'int':
        return getValueExpression('as int$nullable');
      case 'double':
        return '(${getValueExpression('as num$nullable')})${nullable.isEmpty ? '' : '?'}.toDouble()';
      case 'bool':
        return getValueExpression('as bool$nullable');
      case 'num':
        return getValueExpression('as num$nullable');
      default:
        if (typeString.startsWith('List<')) {
          return '(${getValueExpression('as List<dynamic>$nullable')})${nullable.isEmpty ? '' : '?'}.cast<${typeString.substring(5, typeString.length - 1)}>()';
        }
        return getValueExpression('as $typeString$nullable');
    }
  }

  String _getJsonSchemaType(DartType type) {
    final typeString = type.getDisplayString(withNullability: false);

    switch (typeString) {
      case 'String':
        return 'string';
      case 'int':
      case 'double':
      case 'num':
        return 'number';
      case 'bool':
        return 'boolean';
      default:
        if (typeString.startsWith('List<')) {
          return 'array';
        }
        return 'object';
    }
  }

  String _getCleanTypeName(DartType type) {
    final displayString = type.getDisplayString(withNullability: true);

    // Handle Future<T> -> T for return types
    if (displayString.startsWith('Future<') && displayString.endsWith('>')) {
      return displayString.substring(7, displayString.length - 1);
    }

    return displayString;
  }

  String _capitalize(String text) {
    if (text.isEmpty) return text;
    return text[0].toUpperCase() + text.substring(1);
  }

  String _camelToSnake(String text) {
    return text
        .replaceAllMapped(
          RegExp(r'[A-Z]'),
          (match) => '_${match.group(0)!.toLowerCase()}',
        )
        .replaceFirst(RegExp(r'^_'), '');
  }

  String _camelToSentence(String text) {
    return text
        .replaceAllMapped(
          RegExp(r'[A-Z]'),
          (match) => ' ${match.group(0)!.toLowerCase()}',
        )
        .replaceFirst(RegExp(r'^ '), '')
        .capitalize();
  }

  String _camelCase(String text) {
    // 将下划线分隔的文件名转换为驼峰命名
    return text.split('_').map((part) => _capitalize(part)).join('');
  }

  String _escapeString(String text) {
    return text.replaceAll("'", "\\'").replaceAll('\n', '\\n');
  }

  /// 生成函数到工具的映射函数
  String _generateFunctionMapping(
      List<_FunctionAnalysis> functions, String fileName) {
    final cases = functions.map((analysis) => '''
    case ${analysis.functionName}:
      return ${analysis.toolClassName}();''').join('\n');

    // 基于文件名生成唯一的函数名
    final functionName = 'get${_capitalize(_camelCase(fileName))}ForFunction';

    return '''
// ============== 函数映射 ==============

/// 从函数获取对应的工具实例
Tool? $functionName(Function function) {
  switch (function) {$cases
    default:
      return null;
  }
}''';
  }
}

/// Analysis result for a function
class _FunctionAnalysis {
  const _FunctionAnalysis({
    required this.functionName,
    required this.toolName,
    required this.description,
    required this.parameters,
    required this.returnType,
    required this.isAsync,
    required this.toolClassName,
    required this.argsClassName,
  });

  final String functionName;
  final String toolName;
  final String description;
  final List<ParameterElement> parameters;
  final String returnType;
  final bool isAsync;
  final String toolClassName;
  final String argsClassName;
}

extension StringExtension on String {
  String capitalize() {
    if (isEmpty) return this;
    return this[0].toUpperCase() + substring(1);
  }
}
