import 'dart:async';
import 'dart:convert';

import 'package:cancellation_token/cancellation_token.dart';

import '../models/tool_schema.dart';

/// Core protocol interface for all tools (matches Python autogen_core.tools.Tool)
abstract class Tool {
  /// JSON schema definition for the tool (contains name, description, and parameters)
  ToolSchema get schema;

  /// Execute the tool with JSON string arguments
  Future<dynamic> runJson(
    String args,
    CancellationToken cancellationToken, {
    String? callId,
  });

  /// Convert return value to string representation
  String returnValueAsString(dynamic value);

  /// Tool name (from schema)
  String get name;

  /// Tool description (from schema)
  String? get description;
}

/// Protocol interface for streaming tools (matches Python autogen_core.tools.StreamTool)
abstract class StreamTool implements Tool {
  /// Execute the tool with streaming output
  Stream<dynamic> runJsonStream(
    String args,
    CancellationToken cancellationToken, {
    String? callId,
  });
}

/// Base implementation for typed tools (matches Python BaseTool)
abstract class BaseTool<TArgs, TReturn> implements Tool {
  BaseTool();

  @override
  ToolSchema get schema => generateSchema();

  /// Generate the tool's schema (implemented by concrete tools)
  ToolSchema generateSchema();

  @override
  String get name => schema.name;

  @override
  String? get description => schema.description;

  /// Execute the tool with typed arguments
  Future<TReturn> run(TArgs args, CancellationToken cancellationToken);

  @override
  Future<dynamic> runJson(
    String args,
    CancellationToken cancellationToken, {
    String? callId,
  }) async {
    try {
      final jsonArgs = jsonDecode(args);
      final typedArgs = parseArgs(jsonArgs);
      return await run(typedArgs, cancellationToken);
    } catch (e) {
      throw ArgumentError('Failed to parse tool arguments: $e. Args: $args');
    }
  }

  /// Parse JSON arguments to typed arguments (implemented by concrete tools)
  TArgs parseArgs(Map<String, dynamic> args);

  @override
  String returnValueAsString(dynamic value) {
    if (value == null) return 'null';
    if (value is String) return value;
    return value.toString();
  }
}

/// Base implementation for streaming tools (matches Python BaseStreamTool)
abstract class BaseStreamTool<TArgs, TStream, TReturn>
    extends BaseTool<TArgs, TReturn> implements StreamTool {
  BaseStreamTool();

  /// Execute the tool with streaming output (implemented by concrete tools)
  ///
  /// This method should yield intermediate results of type TStream followed by
  /// a final result of type TReturn (similar to Python's AsyncGenerator pattern)
  Stream<dynamic> runStream(TArgs args, CancellationToken cancellationToken);

  @override
  Stream<dynamic> runJsonStream(
    String args,
    CancellationToken cancellationToken, {
    String? callId,
  }) async* {
    try {
      final jsonArgs = jsonDecode(args);
      final typedArgs = parseArgs(jsonArgs);

      var hasFinalResult = false;

      await for (final result in runStream(typedArgs, cancellationToken)) {
        hasFinalResult = true;
        yield result;
      }

      // Ensure we have a final result (similar to Python validation)
      if (!hasFinalResult) {
        throw StateError(
          'Streaming tool must yield at least one result as the final return value.',
        );
      }
    } catch (e) {
      throw ArgumentError('Failed to parse tool arguments: $e. Args: $args');
    }
  }

  @override
  Future<TReturn> run(TArgs args, CancellationToken cancellationToken) async {
    // For non-streaming calls, collect all results and return the last one
    dynamic finalResult;

    await for (final result in runStream(args, cancellationToken)) {
      finalResult = result;
    }

    return finalResult as TReturn;
  }

  @override
  Future<dynamic> runJson(
    String args,
    CancellationToken cancellationToken, {
    String? callId,
  }) async {
    // For non-streaming calls, collect all results and return the last one
    dynamic finalResult;

    await for (final result
        in runJsonStream(args, cancellationToken, callId: callId)) {
      finalResult = result;
    }

    return finalResult;
  }
}
