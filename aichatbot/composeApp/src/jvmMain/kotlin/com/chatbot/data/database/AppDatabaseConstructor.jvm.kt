package com.chatbot.data.database

import androidx.room.Room
import androidx.room.RoomDatabaseConstructor
import java.io.File

/**
 * JVM平台的数据库构造器
 */
actual object AppDatabaseConstructor : RoomDatabaseConstructor<AppDatabase> {
    actual override fun initialize(): AppDatabase {
        val dbFile = File(System.getProperty("java.io.tmpdir"), "app_database.db")

        return Room.databaseBuilder<AppDatabase>(
            name = dbFile.absolutePath,
        ).build()
    }
}