package com.chatbot.data.database

import android.content.Context
import androidx.room.Room
import androidx.room.RoomDatabaseConstructor
import java.io.File

/**
 * Android平台的数据库构造器
 */
actual object AppDatabaseConstructor : RoomDatabaseConstructor<AppDatabase> {
    actual override fun initialize(): AppDatabase {
        val context = androidContext
            ?: throw IllegalStateException("Android context not initialized")

        val appContext = context.applicationContext
        val dbFile = appContext.getDatabasePath("app_database.db")
        return Room.databaseBuilder<AppDatabase>(
            context = appContext,
            name = dbFile.absolutePath
        ).build()
    }
}

private var androidContext: Context? = null

/**
 * 设置Android上下文
 * 必须在应用启动时调用
 */
fun setAndroidContext(context: Context) {
    androidContext = context.applicationContext
}