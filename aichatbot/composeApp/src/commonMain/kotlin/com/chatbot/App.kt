package com.chatbot

import androidx.compose.runtime.Composable
import com.chatbot.di.appModul
import com.chatbot.di.platformModule
import com.chatbot.ui.pages.ProviderSettingsPage
import com.drna.shadcn.compose.themes.ShadcnTheme
import org.koin.compose.KoinApplication

@Composable
fun App() {
    KoinApplication(
        application = {
            modules(platformModule(), appModule())
        }
    ) {
            ShadcnTheme {
                ProviderSettingsPage()
            }
    }
}