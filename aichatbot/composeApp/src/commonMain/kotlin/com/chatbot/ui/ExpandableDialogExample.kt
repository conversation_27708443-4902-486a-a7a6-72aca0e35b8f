package com.chatbot.ui

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.unit.dp
import com.drna.shadcn.compose.component.Button
import com.drna.shadcn.compose.component.ButtonVariant

/**
 * ExpandableDialog 使用示例
 */
@Composable
fun ExpandableDialogExample() {
    var showDialog by remember { mutableStateOf(false) }
    var expanded by remember { mutableStateOf(false) }

    // 示例状态
    var selectedOption by remember { mutableStateOf("option1") }
    var enableNotifications by remember { mutableStateOf(true) }
    var autoSave by remember { mutableStateOf(false) }
    var theme by remember { mutableStateOf("light") }
    var language by remember { mutableStateOf("zh") }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Button(
            onClick = { showDialog = true },
            variant = ButtonVariant.Default
        ) {
            Text("显示可展开对话框")
        }

        if (expanded) {
            Spacer(modifier = Modifier.height(16.dp))
            Text("当前展开状态: ${if (expanded) "展开" else "收起"}")
        }
    }

    // 可展开对话框
    ExpandableDialog(
        open = showDialog,
        onDismissRequest = { showDialog = false },
        title = {
            Text("设置")
        },
        description = {
            Text("配置您的应用偏好设置")
        },
        content = {
            // 主要设置内容
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                // 基本选项
                Text(
                    text = "基本设置",
                    style = MaterialTheme.typography.titleMedium
                )

                // 单选选项组
                Column(modifier = Modifier.selectableGroup()) {
                    listOf(
                        "option1" to "选项一",
                        "option2" to "选项二",
                        "option3" to "选项三"
                    ).forEach { (value, label) ->
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .selectable(
                                    selected = selectedOption == value,
                                    onClick = { selectedOption = value },
                                    role = Role.RadioButton
                                )
                                .padding(vertical = 4.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            RadioButton(
                                selected = selectedOption == value,
                                onClick = null
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(label)
                        }
                    }
                }

                // 开关设置
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text("启用通知")
                    Switch(
                        checked = enableNotifications,
                        onCheckedChange = { enableNotifications = it }
                    )
                }
            }
        },
        expandableContent = {
            // 高级设置内容
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                Text(
                    text = "高级设置",
                    style = MaterialTheme.typography.titleMedium
                )

                // 自动保存开关
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Column(modifier = Modifier.weight(1f)) {
                        Text("自动保存")
                        Text(
                            text = "定期自动保存您的更改",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                    Switch(
                        checked = autoSave,
                        onCheckedChange = { autoSave = it }
                    )
                }

                HorizontalDivider()

                // 主题选择
                Column {
                    Text(
                        text = "主题",
                        style = MaterialTheme.typography.titleSmall
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Column(modifier = Modifier.selectableGroup()) {
                        listOf(
                            "light" to "浅色主题",
                            "dark" to "深色主题",
                            "auto" to "跟随系统"
                        ).forEach { (value, label) ->
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .selectable(
                                        selected = theme == value,
                                        onClick = { theme = value },
                                        role = Role.RadioButton
                                    )
                                    .padding(vertical = 4.dp),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                RadioButton(
                                    selected = theme == value,
                                    onClick = null
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Text(label)
                            }
                        }
                    }
                }

                HorizontalDivider()

                // 语言选择
                Column {
                    Text(
                        text = "语言",
                        style = MaterialTheme.typography.titleSmall
                    )
                    Spacer(modifier = Modifier.height(8.dp))

                    var expandedLanguage by remember { mutableStateOf(false) }

                    ExposedDropdownMenuBox(
                        expanded = expandedLanguage,
                        onExpandedChange = { expandedLanguage = !expandedLanguage }
                    ) {
                        TextField(
                            value = when (language) {
                                "zh" -> "中文"
                                "en" -> "English"
                                "ja" -> "日本語"
                                else -> "中文"
                            },
                            onValueChange = {},
                            readOnly = true,
                            label = { Text("选择语言") },
                            trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = expandedLanguage) },
                            modifier = Modifier
                                .menuAnchor()
                                .fillMaxWidth()
                        )

                        ExposedDropdownMenu(
                            expanded = expandedLanguage,
                            onDismissRequest = { expandedLanguage = false }
                        ) {
                            listOf(
                                "zh" to "中文",
                                "en" to "English",
                                "ja" to "日本語"
                            ).forEach { (value, label) ->
                                DropdownMenuItem(
                                    text = { Text(label) },
                                    onClick = {
                                        language = value
                                        expandedLanguage = false
                                    }
                                )
                            }
                        }
                    }
                }

                // 额外的设置项可以继续添加...
                Text(
                    text = "更多高级设置项可以在这里添加...",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        },
        footer = {
            Button(
                onClick = { showDialog = false },
                variant = ButtonVariant.Outline
            ) {
                Text("取消")
            }

            Button(
                onClick = {
                    // 保存设置逻辑
                    showDialog = false
                },
                variant = ButtonVariant.Default
            ) {
                Text("保存")
            }
        },
        onExpandedChange = { expanded = it },
        expandButtonText = "高级选项",
        collapseButtonText = "收起选项"
    )
}

/**
 * 简化版本的使用示例
 */
@Composable
fun SimpleExpandableDialogExample() {
    var showDialog by remember { mutableStateOf(false) }

    Button(
        onClick = { showDialog = true },
        variant = ButtonVariant.Default
    ) {
        Text("显示简单可展开对话框")
    }

    // 使用便捷方法显示对话框
    ExpandableDialogDefaults.show(
        open = showDialog,
        onDismissRequest = { showDialog = false },
        title = "确认操作",
        description = "这是一个简单的确认对话框示例",
        content = {
            Text("您确定要执行此操作吗？此操作不可撤销。")
        },
        expandableContent = {
            Column(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Text("详细信息:")
                Text("• 操作将立即生效")
                Text("• 相关数据将被永久删除")
                Text("• 建议在操作前备份重要数据")

                HorizontalDivider()

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text("我已了解风险")
                    var acknowledged by remember { mutableStateOf(false) }
                    Checkbox(
                        checked = acknowledged,
                        onCheckedChange = { acknowledged = it }
                    )
                }
            }
        },
        onConfirm = { showDialog = false },
        onCancel = { showDialog = false },
        expandButtonText = "查看详情",
        collapseButtonText = "隐藏详情"
    )
}