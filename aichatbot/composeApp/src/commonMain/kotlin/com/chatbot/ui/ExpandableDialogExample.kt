package com.chatbot.ui

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.unit.dp
import com.drna.shadcn.compose.component.Button
import com.drna.shadcn.compose.component.ButtonVariant

/**
 * ExpandableDialog 的符合 Compose 最佳实践的使用示例
 * 展示了状态提升、组合、和现代 Compose 模式
 */
@Composable
fun ExpandableDialogExample() {
    var showDialog by remember { mutableStateOf(false) }
    val dialogState = rememberExpandableDialogState()

    // 应用设置状态
    var settings by remember {
        mutableStateOf(
            AppSettings(
                theme = "auto",
                language = "zh",
                enableNotifications = true,
                autoSave = true,
                saveInterval = 5,
                fontSize = "medium"
            )
        )
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Button(
            onClick = { showDialog = true },
            variant = ButtonVariant.Default
        ) {
            Icon(
                imageVector = Icons.Default.Settings,
                contentDescription = null,
                modifier = Modifier.size(16.dp)
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text("打开设置")
        }

        if (dialogState.expanded) {
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                "高级设置已展开",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }

    // 使用新的 ExpandableDialog
    ExpandableDialog(
        open = showDialog,
        onDismissRequest = { showDialog = false },
        state = dialogState,
        title = {
            DialogTitle("应用设置")
        },
        description = {
            DialogDescription("自定义您的应用体验和偏好设置")
        },
        content = {
            // 基本设置内容
            BasicSettingsContent(
                settings = settings,
                onSettingsChange = { settings = it }
            )
        },
        expandableContent = {
            // 高级设置内容
            AdvancedSettingsContent(
                settings = settings,
                onSettingsChange = { settings = it }
            )
        },
        actions = {
            DialogCancel(
                onClick = { showDialog = false }
            ) {
                Text("取消")
            }

            DialogAction(
                onClick = {
                    // 保存设置
                    showDialog = false
                }
            ) {
                Text("保存")
            }
        }
    )
}

/**
 * 基本设置内容组件 - 无状态组件
 */
@Composable
private fun BasicSettingsContent(
    settings: AppSettings,
    onSettingsChange: (AppSettings) -> Unit
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "基本设置",
            style = MaterialTheme.typography.titleMedium
        )

        // 主题选择
        ThemeSelector(
            currentTheme = settings.theme,
            onThemeChange = { onSettingsChange(settings.copy(theme = it)) }
        )

        // 字体大小选择
        FontSizeSelector(
            currentSize = settings.fontSize,
            onSizeChange = { onSettingsChange(settings.copy(fontSize = it)) }
        )

        // 通知开关
        NotificationSwitch(
            enabled = settings.enableNotifications,
            onToggle = { onSettingsChange(settings.copy(enableNotifications = it)) }
        )
    }
}

/**
 * 高级设置内容组件 - 无状态组件
 */
@Composable
private fun AdvancedSettingsContent(
    settings: AppSettings,
    onSettingsChange: (AppSettings) -> Unit
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "高级设置",
            style = MaterialTheme.typography.titleMedium
        )

        // 自动保存设置
        AutoSaveSettings(
            enabled = settings.autoSave,
            interval = settings.saveInterval,
            onEnabledChange = { onSettingsChange(settings.copy(autoSave = it)) },
            onIntervalChange = { onSettingsChange(settings.copy(saveInterval = it)) }
        )

        HorizontalDivider()

        // 语言选择
        LanguageSelector(
            currentLanguage = settings.language,
            onLanguageChange = { onSettingsChange(settings.copy(language = it)) }
        )

        // 数据使用警告
        DataUsageWarning()
    }
}

/**
 * 主题选择器组件
 */
@Composable
private fun ThemeSelector(
    currentTheme: String,
    onThemeChange: (String) -> Unit
) {
    Column {
        Text(
            text = "主题",
            style = MaterialTheme.typography.titleSmall
        )
        Spacer(modifier = Modifier.height(8.dp))

        Column(modifier = Modifier.selectableGroup()) {
            val themes = listOf(
                "light" to "浅色",
                "dark" to "深色",
                "auto" to "跟随系统"
            )

            themes.forEach { (value, label) ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .selectable(
                            selected = currentTheme == value,
                            onClick = { onThemeChange(value) },
                            role = Role.RadioButton
                        )
                        .padding(vertical = 4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = currentTheme == value,
                        onClick = null
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(label)
                }
            }
        }
    }
}

/**
 * 字体大小选择器组件
 */
@Composable
private fun FontSizeSelector(
    currentSize: String,
    onSizeChange: (String) -> Unit
) {
    Column {
        Text(
            text = "字体大小",
            style = MaterialTheme.typography.titleSmall
        )
        Spacer(modifier = Modifier.height(8.dp))

        Row(
            modifier = Modifier.selectableGroup(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            val sizes = listOf(
                "small" to "小",
                "medium" to "中",
                "large" to "大"
            )

            sizes.forEach { (value, label) ->
                FilterChip(
                    selected = currentSize == value,
                    onClick = { onSizeChange(value) },
                    label = { Text(label) }
                )
            }
        }
    }
}

/**
 * 通知开关组件
 */
@Composable
private fun NotificationSwitch(
    enabled: Boolean,
    onToggle: (Boolean) -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column {
            Text(
                text = "推送通知",
                style = MaterialTheme.typography.bodyMedium
            )
            Text(
                text = "接收应用通知和更新",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
        Switch(
            checked = enabled,
            onCheckedChange = onToggle
        )
    }
}

/**
 * 自动保存设置组件
 */
@Composable
private fun AutoSaveSettings(
    enabled: Boolean,
    interval: Int,
    onEnabledChange: (Boolean) -> Unit,
    onIntervalChange: (Int) -> Unit
) {
    Column {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column {
                Text(
                    text = "自动保存",
                    style = MaterialTheme.typography.bodyMedium
                )
                Text(
                    text = "定期自动保存您的更改",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            Switch(
                checked = enabled,
                onCheckedChange = onEnabledChange
            )
        }

        if (enabled) {
            Spacer(modifier = Modifier.height(12.dp))

            Text(
                text = "保存间隔: ${interval}分钟",
                style = MaterialTheme.typography.bodySmall
            )

            Slider(
                value = interval.toFloat(),
                onValueChange = { onIntervalChange(it.toInt()) },
                valueRange = 1f..30f,
                steps = 28
            )
        }
    }
}

/**
 * 语言选择器组件
 */
@Composable
private fun LanguageSelector(
    currentLanguage: String,
    onLanguageChange: (String) -> Unit
) {
    var expanded by remember { mutableStateOf(false) }

    Column {
        Text(
            text = "语言",
            style = MaterialTheme.typography.titleSmall
        )
        Spacer(modifier = Modifier.height(8.dp))

        ExposedDropdownMenuBox(
            expanded = expanded,
            onExpandedChange = { expanded = !expanded }
        ) {
            TextField(
                value = when (currentLanguage) {
                    "zh" -> "中文"
                    "en" -> "English"
                    "ja" -> "日本語"
                    else -> "中文"
                },
                onValueChange = {},
                readOnly = true,
                label = { Text("选择语言") },
                trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = expanded) },
                modifier = Modifier
                    .menuAnchor()
                    .fillMaxWidth()
            )

            ExposedDropdownMenu(
                expanded = expanded,
                onDismissRequest = { expanded = false }
            ) {
                listOf(
                    "zh" to "中文",
                    "en" to "English",
                    "ja" to "日本語"
                ).forEach { (value, label) ->
                    DropdownMenuItem(
                        text = { Text(label) },
                        onClick = {
                            onLanguageChange(value)
                            expanded = false
                        }
                    )
                }
            }
        }
    }
}

/**
 * 数据使用警告组件
 */
@Composable
private fun DataUsageWarning() {
    Card(
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.errorContainer
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "数据使用提醒",
                style = MaterialTheme.typography.titleSmall,
                color = MaterialTheme.colorScheme.onErrorContainer
            )
            Spacer(modifier = Modifier.height(4.dp))
            Text(
                text = "某些功能可能会消耗额外的数据流量。",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onErrorContainer
            )
        }
    }
}

/**
 * 应用设置数据类
 */
data class AppSettings(
    val theme: String = "auto",
    val language: String = "zh",
    val enableNotifications: Boolean = true,
    val autoSave: Boolean = true,
    val saveInterval: Int = 5,
    val fontSize: String = "medium"
)