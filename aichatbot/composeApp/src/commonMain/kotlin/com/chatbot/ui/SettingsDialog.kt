package com.chatbot.ui

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.unit.dp
import com.drna.shadcn.compose.component.Button
import com.drna.shadcn.compose.component.ButtonVariant

/**
 * 设置数据类
 */
data class AppSettings(
    val theme: String = "auto",
    val language: String = "zh",
    val enableNotifications: Boolean = true,
    val autoSave: Boolean = true,
    val saveInterval: Int = 5, // 分钟
    val fontSize: String = "medium",
    val enableAnimation: Boolean = true,
    val enableSound: Boolean = false,
    val debugMode: Boolean = false
)

/**
 * 预构建的设置对话框组件
 *
 * @param open 是否显示对话框
 * @param onDismissRequest 关闭对话框的回调
 * @param settings 当前设置值
 * @param onSettingsChange 设置更改时的回调
 * @param onSave 保存设置的回调
 * @param onReset 重置设置的回调
 */
@Composable
fun SettingsDialog(
    open: Boolean,
    onDismissRequest: () -> Unit,
    settings: AppSettings,
    onSettingsChange: (AppSettings) -> Unit,
    onSave: (AppSettings) -> Unit,
    onReset: (() -> Unit)? = null
) {
    var currentSettings by remember(settings) { mutableStateOf(settings) }

    ExpandableDialog(
        open = open,
        onDismissRequest = onDismissRequest,
        title = {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.Settings,
                    contentDescription = null,
                    modifier = Modifier.size(20.dp)
                )
                Text("应用设置")
            }
        },
        description = {
            Text("自定义您的应用体验")
        },
        content = {
            // 基本设置
            BasicSettingsSection(
                settings = currentSettings,
                onSettingsChange = { currentSettings = it }
            )
        },
        expandableContent = {
            // 高级设置
            AdvancedSettingsSection(
                settings = currentSettings,
                onSettingsChange = { currentSettings = it }
            )
        },
        footer = {
            // 重置按钮（可选）
            onReset?.let {
                Button(
                    onClick = it,
                    variant = ButtonVariant.Ghost
                ) {
                    Text("重置")
                }
            }

            Button(
                onClick = onDismissRequest,
                variant = ButtonVariant.Outline
            ) {
                Text("取消")
            }

            Button(
                onClick = {
                    onSettingsChange(currentSettings)
                    onSave(currentSettings)
                    onDismissRequest()
                },
                variant = ButtonVariant.Default
            ) {
                Text("保存")
            }
        },
        expandButtonText = "高级设置",
        collapseButtonText = "收起高级设置"
    )
}

/**
 * 基本设置区域
 */
@Composable
private fun BasicSettingsSection(
    settings: AppSettings,
    onSettingsChange: (AppSettings) -> Unit
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "基本设置",
            style = MaterialTheme.typography.titleMedium
        )

        // 主题设置
        SettingGroup(title = "外观") {
            SettingItem(
                title = "主题",
                subtitle = "选择应用主题"
            ) {
                var expanded by remember { mutableStateOf(false) }

                ExposedDropdownMenuBox(
                    expanded = expanded,
                    onExpandedChange = { expanded = !expanded }
                ) {
                    TextField(
                        value = when (settings.theme) {
                            "light" -> "浅色"
                            "dark" -> "深色"
                            "auto" -> "跟随系统"
                            else -> "跟随系统"
                        },
                        onValueChange = {},
                        readOnly = true,
                        trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = expanded) },
                        modifier = Modifier
                            .menuAnchor()
                            .width(120.dp)
                    )

                    ExposedDropdownMenu(
                        expanded = expanded,
                        onDismissRequest = { expanded = false }
                    ) {
                        listOf(
                            "light" to "浅色",
                            "dark" to "深色",
                            "auto" to "跟随系统"
                        ).forEach { (value, label) ->
                            DropdownMenuItem(
                                text = { Text(label) },
                                onClick = {
                                    onSettingsChange(settings.copy(theme = value))
                                    expanded = false
                                }
                            )
                        }
                    }
                }
            }

            SettingItem(
                title = "字体大小",
                subtitle = "调整界面字体大小"
            ) {
                Column(modifier = Modifier.selectableGroup()) {
                    listOf(
                        "small" to "小",
                        "medium" to "中",
                        "large" to "大"
                    ).forEach { (value, label) ->
                        Row(
                            modifier = Modifier
                                .selectable(
                                    selected = settings.fontSize == value,
                                    onClick = { onSettingsChange(settings.copy(fontSize = value)) },
                                    role = Role.RadioButton
                                ),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            RadioButton(
                                selected = settings.fontSize == value,
                                onClick = null
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(label)
                        }
                    }
                }
            }
        }

        // 通知设置
        SettingGroup(title = "通知") {
            SwitchSettingItem(
                title = "启用通知",
                subtitle = "接收应用通知",
                checked = settings.enableNotifications,
                onCheckedChange = { onSettingsChange(settings.copy(enableNotifications = it)) }
            )

            SwitchSettingItem(
                title = "声音提醒",
                subtitle = "通知时播放声音",
                checked = settings.enableSound,
                onCheckedChange = { onSettingsChange(settings.copy(enableSound = it)) },
                enabled = settings.enableNotifications
            )
        }
    }
}

/**
 * 高级设置区域
 */
@Composable
private fun AdvancedSettingsSection(
    settings: AppSettings,
    onSettingsChange: (AppSettings) -> Unit
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "高级设置",
            style = MaterialTheme.typography.titleMedium
        )

        // 性能设置
        SettingGroup(title = "性能") {
            SwitchSettingItem(
                title = "启用动画",
                subtitle = "界面过渡动画效果",
                checked = settings.enableAnimation,
                onCheckedChange = { onSettingsChange(settings.copy(enableAnimation = it)) }
            )

            SwitchSettingItem(
                title = "自动保存",
                subtitle = "定期自动保存您的更改",
                checked = settings.autoSave,
                onCheckedChange = { onSettingsChange(settings.copy(autoSave = it)) }
            )

            if (settings.autoSave) {
                SettingItem(
                    title = "保存间隔",
                    subtitle = "自动保存的时间间隔（分钟）"
                ) {
                    var sliderValue by remember(settings.saveInterval) {
                        mutableStateOf(settings.saveInterval.toFloat())
                    }

                    Column {
                        Slider(
                            value = sliderValue,
                            onValueChange = { sliderValue = it },
                            onValueChangeFinished = {
                                onSettingsChange(settings.copy(saveInterval = sliderValue.toInt()))
                            },
                            valueRange = 1f..30f,
                            steps = 28
                        )
                        Text(
                            text = "${sliderValue.toInt()} 分钟",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            }
        }

        // 开发者设置
        SettingGroup(title = "开发者选项") {
            SwitchSettingItem(
                title = "调试模式",
                subtitle = "启用调试功能和详细日志",
                checked = settings.debugMode,
                onCheckedChange = { onSettingsChange(settings.copy(debugMode = it)) }
            )

            if (settings.debugMode) {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.warningContainer
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(12.dp)
                    ) {
                        Text(
                            text = "⚠️ 警告",
                            style = MaterialTheme.typography.titleSmall,
                            color = MaterialTheme.colorScheme.onWarningContainer
                        )
                        Text(
                            text = "调试模式可能会影响应用性能，建议仅在开发时使用。",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onWarningContainer
                        )
                    }
                }
            }
        }
    }
}

/**
 * 设置组容器
 */
@Composable
private fun SettingGroup(
    title: String,
    content: @Composable ColumnScope.() -> Unit
) {
    Column {
        Text(
            text = title,
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.primary
        )
        Spacer(modifier = Modifier.height(8.dp))
        content()
    }
}

/**
 * 设置项容器
 */
@Composable
private fun SettingItem(
    title: String,
    subtitle: String? = null,
    enabled: Boolean = true,
    content: @Composable () -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.bodyMedium,
                color = if (enabled) {
                    MaterialTheme.colorScheme.onSurface
                } else {
                    MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                }
            )
            subtitle?.let {
                Text(
                    text = it,
                    style = MaterialTheme.typography.bodySmall,
                    color = if (enabled) {
                        MaterialTheme.colorScheme.onSurfaceVariant
                    } else {
                        MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.6f)
                    }
                )
            }
        }
        Spacer(modifier = Modifier.width(16.dp))
        content()
    }
}

/**
 * 开关设置项
 */
@Composable
private fun SwitchSettingItem(
    title: String,
    subtitle: String? = null,
    checked: Boolean,
    onCheckedChange: (Boolean) -> Unit,
    enabled: Boolean = true
) {
    SettingItem(
        title = title,
        subtitle = subtitle,
        enabled = enabled
    ) {
        Switch(
            checked = checked,
            onCheckedChange = onCheckedChange,
            enabled = enabled
        )
    }
}