package com.chatbot.ui

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.animation.expandVertically
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.semantics.stateDescription
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog as ComposeDialog
import com.drna.shadcn.compose.component.Button
import com.drna.shadcn.compose.component.ButtonSize
import com.drna.shadcn.compose.component.ButtonVariant
import com.drna.shadcn.compose.themes.radius
import com.drna.shadcn.compose.themes.shadcnColors

/**
 * 可展开的对话框组件，支持"更多设置"折叠功能
 *
 * @param onDismissRequest 当用户尝试关闭对话框时的回调
 * @param open 控制对话框显示/隐藏的状态
 * @param modifier 应用到对话框内容区域的修饰符
 * @param title 对话框标题的可组合内容
 * @param description 对话框描述的可组合内容
 * @param content 对话框主要内容
 * @param expandableContent 可展开的额外内容（"更多设置"）
 * @param footer 对话框底部操作按钮
 * @param showCloseButton 是否显示关闭按钮
 * @param expandButtonText 展开按钮的文本
 * @param collapseButtonText 收起按钮的文本
 * @param initialExpanded 初始展开状态
 * @param onExpandedChange 展开状态变化时的回调
 * @param maxExpandedHeight 展开内容的最大高度
 */
@Composable
fun ExpandableDialog(
    onDismissRequest: () -> Unit,
    open: Boolean,
    modifier: Modifier = Modifier,
    title: @Composable () -> Unit = {},
    description: @Composable () -> Unit = {},
    content: @Composable () -> Unit = {},
    expandableContent: @Composable () -> Unit = {},
    footer: @Composable () -> Unit = {},
    showCloseButton: Boolean = true,
    expandButtonText: String = "更多设置",
    collapseButtonText: String = "收起设置",
    initialExpanded: Boolean = false,
    onExpandedChange: ((Boolean) -> Unit)? = null,
    maxExpandedHeight: Int = 300
) {
    val colors = MaterialTheme.shadcnColors
    val radius = MaterialTheme.radius
    var expanded by remember { mutableStateOf(initialExpanded) }

    // 箭头旋转动画
    val arrowRotation by animateFloatAsState(
        targetValue = if (expanded) 180f else 0f,
        animationSpec = tween(durationMillis = 300),
        label = "arrow_rotation"
    )

    if (open) {
        ComposeDialog(onDismissRequest = onDismissRequest) {
            Column(
                modifier = modifier
                    .fillMaxWidth()
                    .background(colors.background, RoundedCornerShape(radius.lg))
                    .border(1.dp, colors.border, RoundedCornerShape(radius.lg))
                    .padding(24.dp)
            ) {
                // 头部区域（标题、描述、关闭按钮）
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                ) {
                    Column(modifier = Modifier.weight(1f)) {
                        ProvideTextStyle(
                            value = TextStyle(
                                color = colors.foreground,
                                fontSize = 18.sp,
                                fontWeight = FontWeight.SemiBold
                            )
                        ) {
                            title()
                        }

                        Spacer(modifier = Modifier.height(8.dp))

                        ProvideTextStyle(
                            value = TextStyle(
                                color = colors.mutedForeground,
                                fontSize = 14.sp
                            )
                        ) {
                            description()
                        }
                    }

                    if (showCloseButton) {
                        Box(
                            modifier = Modifier.offset(y = (-12).dp)
                        ) {
                            Button(
                                onClick = onDismissRequest,
                                size = ButtonSize.Icon,
                                variant = ButtonVariant.Ghost,
                                modifier = Modifier
                                    .width(24.dp)
                                    .height(24.dp)
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Close,
                                    contentDescription = "关闭对话框",
                                    tint = colors.mutedForeground
                                )
                            }
                        }
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))

                // 主要内容区域
                content()

                Spacer(modifier = Modifier.height(16.dp))

                // 底部操作区域
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 更多设置展开按钮
                    ExpandToggleButton(
                        expanded = expanded,
                        onToggle = { newExpanded ->
                            expanded = newExpanded
                            onExpandedChange?.invoke(newExpanded)
                        },
                        expandText = expandButtonText,
                        collapseText = collapseButtonText,
                        arrowRotation = arrowRotation,
                        colors = colors
                    )

                    // 操作按钮
                    Row(
                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        footer()
                    }
                }

                // 可展开的内容区域
                AnimatedVisibility(
                    visible = expanded,
                    enter = expandVertically(
                        animationSpec = tween(300),
                        expandFrom = Alignment.Top
                    ),
                    exit = shrinkVertically(
                        animationSpec = tween(300),
                        shrinkTowards = Alignment.Top
                    )
                ) {
                    Column {
                        Spacer(modifier = Modifier.height(16.dp))

                        // 分隔线
                        HorizontalDivider(
                            color = colors.border,
                            thickness = 1.dp
                        )

                        Spacer(modifier = Modifier.height(16.dp))

                        // 可滚动的展开内容
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .heightIn(max = maxExpandedHeight.dp)
                        ) {
                            Column(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .verticalScroll(rememberScrollState())
                            ) {
                                expandableContent()
                            }
                        }
                    }
                }
            }
        }
    }
}

/**
 * 展开/收起切换按钮组件
 */
@Composable
private fun ExpandToggleButton(
    expanded: Boolean,
    onToggle: (Boolean) -> Unit,
    expandText: String,
    collapseText: String,
    arrowRotation: Float,
    colors: Any
) {
    TextButton(
        onClick = { onToggle(!expanded) },
        modifier = Modifier.semantics {
            contentDescription = if (expanded) "收起更多设置" else "展开更多设置"
        },
        colors = ButtonDefaults.textButtonColors(
            containerColor = Color.Transparent,
            contentColor = MaterialTheme.colorScheme.primary
        )
    ) {
        Icon(
            imageVector = Icons.Default.KeyboardArrowDown,
            contentDescription = null,
            modifier = Modifier
                .size(16.dp)
                .rotate(arrowRotation),
            tint = MaterialTheme.colorScheme.primary
        )

        Spacer(modifier = Modifier.width(4.dp))

        Text(
            text = if (expanded) collapseText else expandText,
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium
        )
    }
}

/**
 * 便捷的对话框显示方法
 */
object ExpandableDialogDefaults {

    /**
     * 显示可展开对话框的便捷方法
     */
    @Composable
    fun show(
        open: Boolean,
        onDismissRequest: () -> Unit,
        title: String? = null,
        description: String? = null,
        content: @Composable () -> Unit = {},
        expandableContent: @Composable () -> Unit = {},
        onConfirm: (() -> Unit)? = null,
        onCancel: (() -> Unit)? = null,
        confirmText: String = "确认",
        cancelText: String = "取消",
        showCloseButton: Boolean = true,
        expandButtonText: String = "更多设置",
        collapseButtonText: String = "收起设置",
        initialExpanded: Boolean = false,
        onExpandedChange: ((Boolean) -> Unit)? = null
    ) {
        ExpandableDialog(
            onDismissRequest = onDismissRequest,
            open = open,
            title = {
                title?.let { Text(it) }
            },
            description = {
                description?.let { Text(it) }
            },
            content = content,
            expandableContent = expandableContent,
            footer = {
                onCancel?.let {
                    Button(
                        onClick = it,
                        variant = ButtonVariant.Outline
                    ) {
                        Text(cancelText)
                    }
                }

                onConfirm?.let {
                    Button(
                        onClick = it,
                        variant = ButtonVariant.Default
                    ) {
                        Text(confirmText)
                    }
                }
            },
            showCloseButton = showCloseButton,
            expandButtonText = expandButtonText,
            collapseButtonText = collapseButtonText,
            initialExpanded = initialExpanded,
            onExpandedChange = onExpandedChange
        )
    }
}