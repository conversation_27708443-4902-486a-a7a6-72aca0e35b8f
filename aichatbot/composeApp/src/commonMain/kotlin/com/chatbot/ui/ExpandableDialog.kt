package com.chatbot.ui

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.animation.expandVertically
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.semantics.stateDescription
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog as ComposeDialog
import com.drna.shadcn.compose.component.Button
import com.drna.shadcn.compose.component.ButtonSize
import com.drna.shadcn.compose.component.ButtonVariant
import com.drna.shadcn.compose.themes.radius
import com.drna.shadcn.compose.themes.shadcnColors

/**
 * 可展开对话框的状态类
 * 使用 Compose 的状态提升模式
 */
@Stable
class ExpandableDialogState(
    initialExpanded: Boolean = false
) {
    var expanded by mutableStateOf(initialExpanded)
        private set

    fun toggle() {
        expanded = !expanded
    }

    fun expand() {
        expanded = true
    }

    fun collapse() {
        expanded = false
    }
}

/**
 * 创建并记住可展开对话框状态
 */
@Composable
fun rememberExpandableDialogState(
    initialExpanded: Boolean = false
): ExpandableDialogState {
    return remember { ExpandableDialogState(initialExpanded) }
}

/**
 * 基于 shadcn Dialog 的可展开对话框组件
 * 完全遵循 Compose 设计原则和最佳实践
 *
 * @param onDismissRequest 当用户尝试关闭对话框时的回调
 * @param open 控制对话框显示状态
 * @param state 可展开状态，使用 [rememberExpandableDialogState] 创建
 * @param modifier 应用到对话框的修饰符
 * @param title 对话框标题内容
 * @param description 对话框描述内容
 * @param content 主要内容区域
 * @param expandableContent 可展开的附加内容
 * @param actions 底部操作按钮
 * @param expandToggle 自定义展开/收起按钮，为 null 时使用默认按钮
 * @param showCloseButton 是否显示关闭按钮
 * @param maxExpandedHeight 展开内容的最大高度
 */
@Composable
fun ExpandableDialog(
    onDismissRequest: () -> Unit,
    open: Boolean,
    state: ExpandableDialogState = rememberExpandableDialogState(),
    modifier: Modifier = Modifier,
    title: @Composable () -> Unit = {},
    description: @Composable () -> Unit = {},
    content: @Composable () -> Unit = {},
    expandableContent: @Composable () -> Unit = {},
    actions: @Composable RowScope.() -> Unit = {},
    expandToggle: @Composable ((Boolean, () -> Unit) -> Unit)? = null,
    showCloseButton: Boolean = true,
    maxExpandedHeight: Int = 300
) {
    val colors = MaterialTheme.shadcnColors
    val radius = MaterialTheme.radius

    if (open) {
        ComposeDialog(onDismissRequest = onDismissRequest) {
            Surface(
                modifier = modifier.fillMaxWidth(),
                shape = RoundedCornerShape(radius.lg),
                color = colors.background,
                border = BorderStroke(1.dp, colors.border)
            ) {
                Column(
                    modifier = Modifier.padding(24.dp)
                ) {
                    // 对话框头部
                    DialogHeader(
                        title = title,
                        description = description,
                        onClose = if (showCloseButton) onDismissRequest else null,
                        colors = colors
                    )

                    if (title != {} || description != {}) {
                        Spacer(modifier = Modifier.height(16.dp))
                    }

                    // 主要内容
                    content()

                    Spacer(modifier = Modifier.height(16.dp))

                    // 底部操作区域
                    DialogFooter(
                        actions = actions,
                        expandToggle = expandToggle,
                        expanded = state.expanded,
                        onToggle = state::toggle,
                        hasExpandableContent = expandableContent != {}
                    )

                    // 可展开内容
                    ExpandableContent(
                        visible = state.expanded,
                        maxHeight = maxExpandedHeight,
                        colors = colors,
                        content = expandableContent
                    )
                }
            }
        }
    }
}

/**
 * 对话框头部组件
 */
@Composable
private fun DialogHeader(
    title: @Composable () -> Unit,
    description: @Composable () -> Unit,
    onClose: (() -> Unit)?,
    colors: Any
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.Top
    ) {
        Column(modifier = Modifier.weight(1f)) {
            ProvideTextStyle(
                value = TextStyle(
                    color = MaterialTheme.colorScheme.onSurface,
                    fontSize = 18.sp,
                    fontWeight = FontWeight.SemiBold
                )
            ) {
                title()
            }

            ProvideTextStyle(
                value = TextStyle(
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    fontSize = 14.sp
                )
            ) {
                description()
            }
        }

        onClose?.let { closeAction ->
            IconButton(
                onClick = closeAction,
                modifier = Modifier
                    .size(32.dp)
                    .offset(x = 8.dp, y = (-8).dp)
                    .semantics {
                        contentDescription = "关闭对话框"
                    }
            ) {
                Icon(
                    imageVector = Icons.Default.Close,
                    contentDescription = null,
                    modifier = Modifier.size(16.dp),
                    tint = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

/**
 * 对话框底部组件
 */
@Composable
private fun DialogFooter(
    actions: @Composable RowScope.() -> Unit,
    expandToggle: @Composable ((Boolean, () -> Unit) -> Unit)?,
    expanded: Boolean,
    onToggle: () -> Unit,
    hasExpandableContent: Boolean
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 展开切换按钮
        if (hasExpandableContent) {
            if (expandToggle != null) {
                expandToggle(expanded, onToggle)
            } else {
                DefaultExpandToggle(
                    expanded = expanded,
                    onToggle = onToggle
                )
            }
        } else {
            Spacer(modifier = Modifier)
        }

        // 操作按钮
        Row(
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            actions()
        }
    }
}

/**
 * 默认的展开切换按钮
 */
@Composable
private fun DefaultExpandToggle(
    expanded: Boolean,
    onToggle: () -> Unit
) {
    val rotation by animateFloatAsState(
        targetValue = if (expanded) 180f else 0f,
        animationSpec = tween(300),
        label = "expand_toggle_rotation"
    )

    TextButton(
        onClick = onToggle,
        modifier = Modifier.semantics {
            role = Role.Button
            stateDescription = if (expanded) "已展开" else "已收起"
            contentDescription = if (expanded) "收起更多选项" else "展开更多选项"
        }
    ) {
        Icon(
            imageVector = Icons.Default.KeyboardArrowDown,
            contentDescription = null,
            modifier = Modifier
                .size(16.dp)
                .rotate(rotation)
        )
        Spacer(modifier = Modifier.width(4.dp))
        Text(
            text = if (expanded) "收起" else "更多",
            style = MaterialTheme.typography.labelMedium
        )
    }
}

/**
 * 可展开内容区域
 */
@Composable
private fun ExpandableContent(
    visible: Boolean,
    maxHeight: Int,
    colors: Any,
    content: @Composable () -> Unit
) {
    AnimatedVisibility(
        visible = visible,
        enter = expandVertically(
            animationSpec = tween(300),
            expandFrom = Alignment.Top
        ),
        exit = shrinkVertically(
            animationSpec = tween(300),
            shrinkTowards = Alignment.Top
        )
    ) {
        Column {
            Spacer(modifier = Modifier.height(16.dp))

            HorizontalDivider(
                color = MaterialTheme.colorScheme.outlineVariant,
                thickness = 1.dp
            )

            Spacer(modifier = Modifier.height(16.dp))

            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .heightIn(max = maxHeight.dp)
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .verticalScroll(rememberScrollState())
                ) {
                    content()
                }
            }
        }
    }
}

// 便捷的构建器函数，遵循 Compose API 设计模式

/**
 * 对话框标题组件
 */
@Composable
fun DialogTitle(
    text: String,
    modifier: Modifier = Modifier
) {
    Text(
        text = text,
        modifier = modifier,
        style = MaterialTheme.typography.headlineSmall
    )
}

/**
 * 对话框描述组件
 */
@Composable
fun DialogDescription(
    text: String,
    modifier: Modifier = Modifier
) {
    Text(
        text = text,
        modifier = modifier,
        style = MaterialTheme.typography.bodyMedium,
        color = MaterialTheme.colorScheme.onSurfaceVariant
    )
}

/**
 * 对话框操作按钮组件
 */
@Composable
fun DialogAction(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    content: @Composable () -> Unit
) {
    Button(
        onClick = onClick,
        modifier = modifier,
        enabled = enabled,
        variant = ButtonVariant.Default
    ) {
        content()
    }
}

/**
 * 对话框取消按钮组件
 */
@Composable
fun DialogCancel(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    content: @Composable () -> Unit
) {
    Button(
        onClick = onClick,
        modifier = modifier,
        enabled = enabled,
        variant = ButtonVariant.Outline
    ) {
        content()
    }
}